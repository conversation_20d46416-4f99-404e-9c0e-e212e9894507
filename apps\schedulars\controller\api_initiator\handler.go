package sample

import (
	"fmt"
	 "libs/shared"

	"github.com/labstack/echo/v4"
)

type Handler interface {
}

type handler struct {
	service Service
}

var newHandlerObj *handler //singleton object

// singleton function
func NewHandler() *handler {
	if newHandlerObj != nil {
		return newHandlerObj
	}

	new_service := NewService()
	newHandlerObj = &handler{new_service}
	return newHandlerObj
}

// ------------------Product Brand--------------------------------------------------------------------------------------------------
func (h *handler) InitiateSchedular(c echo.Context) error {

	schedularName := c.Param("name")

	metaData := shared.ApiMetaData{}
	shared.JsonMarshaller(c.Get("metaData"), &metaData)
	fmt.Println("Inside Initiate Schedular", schedularName)

	err := h.service.InitiateSchedular(schedularName, metaData)
	if err != nil {
		return shared.RespFailure(c, "Internal Error", err)
	}

	message := "Schedular initiated"
	return shared.RespSuccess(c, message, nil)
}
