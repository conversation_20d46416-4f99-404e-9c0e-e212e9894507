// package main

// import "fmt"

// func Hello(name string) string {
// 	result := "Hello " + name
// 	return result
// }

// func main() {
// 	fmt.Println(Hello("Spiro"))

	
// }

package main

import (
	"net/http"
	// "flag"
	"fmt"

	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"

	"libs/shared/db_connectors/migrations"
	"libs/shared/db_connectors/db"
	"libs/shared/utils"
	"libs/shared"
	env "apps/spiro/config"
	backendRouter "apps/spiro/controllers"
)

func main() {

	

	// Create new Echo instance
	e := echo.New()

	// Middleware
	e.Use(middleware.Logger())
	e.Use(middleware.Recover())
	fmt.Println("-----------------------Spiro")
	// Routes
	g := e.Group("/api/v1/spiro/api/v1/spiro")

	dbConn := db.GetProstgressConnection(env.GlobalEnv["POSTGRES_CREDENTIAL"])
	if dbConn == nil {
		panic("Failed to connect to database")
	}else{
		fmt.Println("Connected to database")
	}
	
	e.GET("/table_migration", func(c echo.Context) error {
		if err := migrations.MigrateTables(dbConn); err != nil {
			panic(fmt.Sprintf("Migration failed: %v", err))
		}
			return c.String(http.StatusOK, "Migration successful")
	})

	util.Migrate(dbConn)
	
	backendRouter.Init(g)
	// Index
	e.GET("/", func(c echo.Context) error {
		message := "Welcome to Spiro"
		return c.String(http.StatusOK, message)
	})


	// Start server
	// host := "0.0.0.0" // Listen on all network interfaces
	// port := env.GlobalEnv["PORT"].(string)
	// address := fmt.Sprintf("%s:%s", host, port)
	// e.Logger.Fatal(e.Start(address))
	port := fmt.Sprintf(":%s", env.GlobalEnv["PORT"].(string))
	e.Logger.Fatal(e.Start(port))


	e.POST("*", func(c echo.Context) error {
		return shared.RespPageNotFound(c, "Page Not Found", "Page Not Found")
	})
	e.GET("*", func(c echo.Context) error {
		return shared.RespPageNotFound(c, "Page Not Found", "Page Not Found")
	})

	
}

// Handler functions

