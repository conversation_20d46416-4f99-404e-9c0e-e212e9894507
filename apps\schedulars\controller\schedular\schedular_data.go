package schedular

import (
	"time"
	// "github.com/lib/pq"
)

type SchedulingTime struct {
	Time          int      `json:"time"`
	Hours         []int    `json:"hours"`
	Weekdays      []int    `json:"weekdays"`
	FrequencyType string   `json:"frequency_type"`
}

type Schedular struct {
	ID             int            `json:"id"`
	Name           string         `json:"name"`
	Code           string         `json:"code"`
	Description    string         `json:"description"`
	SchedulingTime SchedulingTime `json:"scheduling_time"`
	Type           string         `json:"type"`
	IsActive       bool           `json:"is_active"`
	IsEnabled      bool           `json:"is_enabled"`
	LastRunTime    *time.Time     `json:"last_run_time"`
}

type SchedularLogs struct {
	ID            int                    `json:"id"`
	SchedularName string                 `json:"schedular_name"`
	SchedularID   int                    `json:"schedular_id"`
	StartTime     time.Time              `json:"start_time"`
	EndTime       time.Time              `json:"end_time"`
	Logs          map[string]interface{} `json:"logs"`
	Status        string                 `json:"status"`
}

var schedularsArr = []Schedular{
	{
		Name:        "Clear Webhook Data",
		Code:        "CLEAR_WEBHOOK_DATA",
		Description: "This scheduler deletes the webhook data from the database.",
		SchedulingTime:SchedulingTime{
			FrequencyType: "HOUR",
			Time:          1,
			Hours:         []int{19},
			Weekdays:      []int{0, 1, 2, 3, 4, 5, 6},
		},
		Type:        "function_Call",
		IsActive:    true,
		IsEnabled:   true,
		LastRunTime: nil,
	},
}
