<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spiro Conversation Hub</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #5468ff;
            --primary-gradient: linear-gradient(135deg, #5468ff 0%, #4d54c8 100%);
            --secondary-color: #6c757d;
            --accent-color: #7b68ee;
            --success-color: #2ecc71;
            --danger-color: #e74c3c;
            --warning-color: #f39c12;
            --info-color: #3498db;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --border-color: #ededed;
            --bg-light: #f9faff;
            --bg-white: #ffffff;
            --text-color: #2d3436;
            --text-secondary: #636e72;
            --text-light: #b2bec3;
            --shadow-sm: 0 2px 8px rgba(0,0,0,0.05);
            --shadow: 0 5px 15px rgba(0,0,0,0.07);
            --shadow-lg: 0 10px 25px rgba(0,0,0,0.1);
            --border-radius-sm: 0.5rem;
            --border-radius: 1rem;
            --border-radius-lg: 1.5rem;
            --transition: all 0.3s ease;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            background-color: var(--bg-light);
            color: var(--text-color);
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        .app-container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 1.5rem;
        }
        
        .dashboard-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(84, 104, 255, 0.1);
        }
        
        .dashboard-title {
            font-weight: 700;
            font-size: 2rem;
            margin: 0;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            letter-spacing: -0.5px;
        }
        
        .dashboard-subtitle {
            font-size: 0.9rem;
            color: var(--text-secondary);
            margin-top: 0.25rem;
        }
        
        .btn {
            border-radius: var(--border-radius-sm);
            padding: 0.6rem 1.2rem;
            font-weight: 500;
            transition: var(--transition);
            box-shadow: var(--shadow-sm);
        }
        
        .btn-primary {
            background: var(--primary-gradient);
            border: none;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #4054e5 0%, #4248a1 100%);
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }
        
        .btn i {
            margin-right: 0.5rem;
            font-size: 0.9rem;
        }
        
        .main-content {
            display: flex;
            background: var(--bg-white);
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow);
            min-height: calc(100vh - 9rem);
        }
        
        .sessions-sidebar {
            width: 350px;
            background: var(--bg-white);
            border-right: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
        }
        
        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            background: rgba(84, 104, 255, 0.03);
        }
        
        .sessions-count {
            font-size: 0.9rem;
            color: var(--text-secondary);
            margin-top: 0.25rem;
        }
        
        .search-box {
            position: relative;
            margin-bottom: 0;
        }
        
        .search-box input {
            width: 100%;
            padding: 0.75rem 1rem 0.75rem 2.5rem;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-sm);
            font-size: 0.9rem;
            transition: var(--transition);
            background-color: rgba(84, 104, 255, 0.02);
        }
        
        .search-box input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(84, 104, 255, 0.1);
        }
        
        .search-box i {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-light);
        }
        
        .sessions-list {
            flex: 1;
            overflow-y: auto;
            padding: 1rem 0;
        }
        
        .session-item {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
            transition: var(--transition);
            position: relative;
        }
        
        .session-item:hover {
            background-color: rgba(84, 104, 255, 0.05);
        }
        
        .session-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 3px;
            background: var(--primary-gradient);
            opacity: 0;
            transition: var(--transition);
        }
        
        .session-item.active {
            background-color: rgba(84, 104, 255, 0.08);
        }
        
        .session-item.active::before {
            opacity: 1;
        }
        
        .session-phone {
            font-weight: 600;
            font-size: 1rem;
            margin-bottom: 0.25rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .session-date {
            font-size: 0.75rem;
            color: var(--text-secondary);
        }
        
        .session-meta {
            display: flex;
            align-items: center;
            margin-top: 0.5rem;
        }
        
        .session-status {
            font-size: 0.7rem;
            padding: 0.2rem 0.5rem;
            border-radius: 10rem;
            background-color: rgba(84, 104, 255, 0.1);
            color: var(--primary-color);
            display: inline-block;
            margin-right: 0.5rem;
        }
        
        .session-message-count {
            font-size: 0.7rem;
            color: var(--text-secondary);
        }
        
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: linear-gradient(to bottom, #f9faff, #f3f5ff);
        }
        
        .chat-header {
            padding: 1.25rem 1.5rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            background-color: var(--bg-white);
        }
        
        .chat-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
            color: var(--text-color);
        }
        
        .chat-subtitle {
            font-size: 0.8rem;
            color: var(--text-secondary);
            margin-top: 0.25rem;
        }
        
        .chat-body {
            flex: 1;
            overflow-y: auto;
            padding: 1.5rem;
            display: flex;
            flex-direction: column;
        }
        
        .message-wrapper {
            margin-bottom: 1.5rem;
            display: flex;
            flex-direction: column;
            max-width: 75%;
            position: relative;
        }
        
        .message-wrapper.user {
            align-self: flex-end;
        }
        
        .message-wrapper.assistant {
            align-self: flex-start;
        }
        
        .message {
            padding: 1rem 1.25rem;
            border-radius: var(--border-radius-sm);
            position: relative;
            box-shadow: var(--shadow-sm);
            font-size: 0.95rem;
            line-height: 1.5;
        }
        
        .user .message {
            background: var(--primary-gradient);
            color: white;
            border-top-right-radius: 0;
        }
        
        .assistant .message {
            background: white;
            color: var(--text-color);
            border-top-left-radius: 0;
        }
        
        .message-time {
            font-size: 0.7rem;
            margin-top: 0.5rem;
            opacity: 0.7;
            align-self: flex-end;
        }
        
        .user .message-time {
            color: var(--text-secondary);
        }
        
        .assistant .message-time {
            color: var(--text-secondary);
        }
        
        .user-info {
            padding: 1.5rem;
            background-color: rgba(84, 104, 255, 0.03);
            border-radius: var(--border-radius);
            margin-bottom: 1.5rem;
            border: 1px solid var(--border-color);
        }
        
        .user-info-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .user-info-icon {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            background: var(--primary-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            color: white;
        }
        
        .user-info-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
            color: var(--text-color);
        }
        
        .user-info-subtitle {
            font-size: 0.8rem;
            color: var(--text-secondary);
            margin-top: 0.25rem;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .info-item {
            margin-bottom: 0.5rem;
        }
        
        .info-label {
            font-size: 0.75rem;
            color: var(--text-secondary);
            margin-bottom: 0.25rem;
            font-weight: 500;
        }
        
        .info-value {
            font-size: 0.9rem;
            color: var(--text-color);
            font-weight: 500;
        }
        
        .conversation-date {
            text-align: center;
            margin: 1.5rem 0;
            position: relative;
        }
        
        .conversation-date::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            width: 100%;
            height: 1px;
            background-color: var(--border-color);
            z-index: 1;
        }
        
        .conversation-date span {
            background-color: var(--bg-light);
            padding: 0.5rem 1rem;
            font-size: 0.8rem;
            color: var(--text-secondary);
            position: relative;
            z-index: 2;
            border-radius: var(--border-radius-sm);
            border: 1px solid var(--border-color);
        }
        
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            padding: 2rem;
            text-align: center;
        }
        
        .empty-state-icon {
            font-size: 5rem;
            margin-bottom: 1.5rem;
            color: rgba(84, 104, 255, 0.2);
        }
        
        .empty-state-title {
            font-weight: 600;
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            color: var(--text-color);
        }
        
        .empty-state-message {
            font-size: 1rem;
            color: var(--text-secondary);
            max-width: 300px;
            margin: 0 auto;
        }
        
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
            padding: 2rem;
        }
        
        .spinner {
            width: 3rem;
            height: 3rem;
            border-radius: 50%;
            background: conic-gradient(#0000 10%, var(--primary-color));
            -webkit-mask: radial-gradient(farthest-side, #0000 calc(100% - 8px), #000 0);
            animation: spinner-animation 1s infinite linear;
        }
        
        @keyframes spinner-animation {
            to {
                transform: rotate(1turn);
            }
        }
        
        .tooltip {
            position: relative;
            display: inline-block;
        }
        
        .tooltip-text {
            visibility: hidden;
            width: 120px;
            background-color: rgba(0, 0, 0, 0.8);
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 5px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -60px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 0.7rem;
        }
        
        .tooltip-text::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: rgba(0, 0, 0, 0.8) transparent transparent transparent;
        }
        
        .tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }
        
        /* Responsive styles */
        @media (max-width: 992px) {
            .main-content {
                flex-direction: column;
                min-height: auto;
            }
            
            .sessions-sidebar {
                width: 100%;
                border-right: none;
                border-bottom: 1px solid var(--border-color);
                max-height: 300px;
            }
            
            .chat-container {
                min-height: 600px;
            }
        }
        
        @media (max-width: 768px) {
            .dashboard-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }
            
            .message-wrapper {
                max-width: 90%;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
        }
        
        @media (max-width: 576px) {
            .app-container {
                padding: 1rem;
            }
            
            .dashboard-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="dashboard-header">
            <div>
                <h1 class="dashboard-title">Spiro Conversation Hub</h1>
                <p class="dashboard-subtitle">Monitor and analyze all WhatsApp conversations</p>
            </div>
            <button class="btn btn-primary refresh-btn">
                <i class="fas fa-sync-alt"></i> Refresh Data
            </button>
        </div>
        
        <div class="main-content">
            <div class="sessions-sidebar">
                <div class="sidebar-header">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="search-sessions" placeholder="Search phone numbers...">
                    </div>
                    <p class="sessions-count mt-2"><span id="session-count">0</span> active sessions</p>
                </div>
                <div class="sessions-list" id="sessions-list-container">
                    <!-- Sessions will be populated here -->
                    <div class="loading">
                        <div class="spinner"></div>
                    </div>
                </div>
            </div>
            
            <div class="chat-container">
                <div class="chat-header">
                    <div>
                        <h2 class="chat-title" id="chat-title">Conversation History</h2>
                        <p class="chat-subtitle" id="chat-subtitle">Select a session to view messages</p>
                    </div>
                </div>
                
                <div class="chat-body" id="messages-container">
                    <div class="empty-state">
                        <div class="empty-state-icon">
                            <i class="fas fa-comments"></i>
                        </div>
                        <h3 class="empty-state-title">No Conversation Selected</h3>
                        <p class="empty-state-message">Select a phone number from the sidebar to view conversation history</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.7/dayjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.7/plugin/relativeTime.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize dayjs relative time plugin
            dayjs.extend(window.dayjs_plugin_relativeTime);
            
            // State
            let currentPhoneNumber = null;
            let sessions = [];
            
            // DOM Elements
            const sessionsContainer = document.getElementById('sessions-list-container');
            const messagesContainer = document.getElementById('messages-container');
            const searchInput = document.getElementById('search-sessions');
            const refreshButton = document.querySelector('.refresh-btn');
            const sessionCountElement = document.getElementById('session-count');
            const chatTitleElement = document.getElementById('chat-title');
            const chatSubtitleElement = document.getElementById('chat-subtitle');
            
            // Format date for display
            function formatDate(timestamp) {
                if (!timestamp) return 'Unknown';
                
                try {
                    // Check if timestamp is a string that needs converting to number
                    if (typeof timestamp === 'string' && !isNaN(timestamp)) {
                        timestamp = parseInt(timestamp) * 1000; // Convert seconds to milliseconds
                    }
                    
                    // If it's a date string, convert to Date object
                    if (typeof timestamp === 'string') {
                        timestamp = new Date(timestamp).getTime();
                    }
                    
                    // Use dayjs for formatting
                    return dayjs(timestamp).format('MMM D, YYYY h:mm A');
                } catch (e) {
                    console.error('Error formatting date:', e);
                    return 'Invalid date';
                }
            }
            
            // Format relative time
            function formatRelativeTime(timestamp) {
                if (!timestamp) return '';
                
                try {
                    // Convert to appropriate format for dayjs
                    if (typeof timestamp === 'string' && !isNaN(timestamp)) {
                        timestamp = parseInt(timestamp) * 1000;
                    }
                    
                    if (typeof timestamp === 'string') {
                        timestamp = new Date(timestamp).getTime();
                    }
                    
                    return dayjs(timestamp).fromNow();
                } catch (e) {
                    return '';
                }
            }
            
            // Group messages by date
            function groupMessagesByDate(messages) {
                const groups = {};
                
                messages.forEach(message => {
                    // Use the message timestamp if available, otherwise use current time
                    let timestamp = new Date().getTime();
                    
                    // Try to extract timestamp from message
                    if (message.timestamp) {
                        timestamp = message.timestamp;
                    }
                    
                    // Format date as YYYY-MM-DD for grouping
                    const dateKey = dayjs(timestamp).format('YYYY-MM-DD');
                    
                    if (!groups[dateKey]) {
                        groups[dateKey] = [];
                    }
                    
                    groups[dateKey].push(message);
                });
                
                return groups;
            }
            
            // Load all sessions
            function loadSessions() {
                sessionsContainer.innerHTML = `
                    <div class="loading">
                        <div class="spinner"></div>
                    </div>
                `;
                
                fetch('/api/sessions/list')
                    .then(response => response.json())
                    .then(data => {
                        sessions = data.sessions || [];
                        sessionCountElement.textContent = sessions.length;
                        renderSessionsList(sessions);
                    })
                    .catch(error => {
                        console.error('Error loading sessions:', error);
                        sessionsContainer.innerHTML = `
                            <div class="empty-state" style="padding: 2rem;">
                                <div class="empty-state-icon" style="font-size: 3rem;">
                                    <i class="fas fa-exclamation-circle"></i>
                                </div>
                                <h3 class="empty-state-title" style="font-size: 1.2rem;">Could Not Load Sessions</h3>
                                <p class="empty-state-message" style="font-size: 0.9rem;">Please check your connection and try again</p>
                                <button class="btn btn-primary mt-3" onclick="loadSessions()">Retry</button>
                            </div>
                        `;
                    });
            }
            
            // Render sessions list
            function renderSessionsList(sessions) {
                if (!sessions || sessions.length === 0) {
                    sessionsContainer.innerHTML = `
                        <div class="empty-state" style="padding: 2rem;">
                            <div class="empty-state-icon" style="font-size: 3rem;">
                                <i class="fas fa-users-slash"></i>
                            </div>
                            <h3 class="empty-state-title" style="font-size: 1.2rem;">No Active Sessions</h3>
                            <p class="empty-state-message" style="font-size: 0.9rem;">There are no active WhatsApp conversations</p>
                        </div>
                    `;
                    return;
                }
                
                // Sort sessions by last_active (most recent first)
                sessions.sort((a, b) => {
                    const dateA = new Date(a.last_active);
                    const dateB = new Date(b.last_active);
                    return dateB - dateA;
                });
                
                let html = '';
                sessions.forEach((session, index) => {
                    const isActive = session.phone_number === currentPhoneNumber;
                    const relativeTime = formatRelativeTime(session.last_active);
                    
                    html += `
                        <div class="session-item ${isActive ? 'active' : ''}" data-phone="${session.phone_number}" data-index="${index}">
                            <div class="session-phone">
                                ${session.phone_number}
                                <span class="tooltip">
                                    <i class="fas fa-info-circle" style="font-size: 0.8rem; color: var(--text-secondary);"></i>
                                    <span class="tooltip-text">Session ID: ${session.id || 'Unknown'}</span>
                                </span>
                            </div>
                            <div class="session-date">Last active: ${relativeTime}</div>
                            <div class="session-meta">
                                <span class="session-status">Active</span>
                                <span class="session-message-count">
                                    <i class="fas fa-comment-alt" style="margin-right: 3px;"></i>
                                    <span id="message-count-${index}">-</span> messages
                                </span>
                            </div>
                        </div>
                    `;
                });
                
                sessionsContainer.innerHTML = html;
                
                // Add click event to session items
                document.querySelectorAll('.session-item').forEach(item => {
                    item.addEventListener('click', function() {
                        const phoneNumber = this.getAttribute('data-phone');
                        loadSessionHistory(phoneNumber);
                        
                        // Update active class
                        document.querySelectorAll('.session-item').forEach(el => {
                            el.classList.remove('active');
                        });
                        this.classList.add('active');
                    });
                });
            }
            
            // Load conversation history for a session
            function loadSessionHistory(phoneNumber) {
                currentPhoneNumber = phoneNumber;
                
                // Update header
                chatTitleElement.textContent = `Conversation with ${phoneNumber}`;
                chatSubtitleElement.textContent = 'Loading conversation history...';
                
                messagesContainer.innerHTML = `
                    <div class="loading">
                        <div class="spinner"></div>
                    </div>
                `;
                
                fetch(`/api/sessions/history/${phoneNumber}`)
                    .then(response => response.json())
                    .then(data => {
                        if (!data.exists) {
                            messagesContainer.innerHTML = `
                                <div class="empty-state">
                                    <div class="empty-state-icon">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <h3 class="empty-state-title">No Conversation Found</h3>
                                    <p class="empty-state-message">This session exists but has no message history</p>
                                </div>
                            `;
                            
                            chatSubtitleElement.textContent = 'No conversation history available';
                            return;
                        }
                        
                        // Update the message count in the sidebar
                        const sessionIndex = sessions.findIndex(s => s.phone_number === phoneNumber);
                        if (sessionIndex !== -1) {
                            const countElement = document.getElementById(`message-count-${sessionIndex}`);
                            if (countElement) {
                                countElement.textContent = data.conversation_history?.length || 0;
                            }
                        }
                        
                        chatSubtitleElement.textContent = `${data.conversation_history?.length || 0} messages`;
                        renderConversationHistory(data);
                    })
                    .catch(error => {
                        console.error('Error loading session history:', error);
                        messagesContainer.innerHTML = `
                            <div class="empty-state">
                                <div class="empty-state-icon">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <h3 class="empty-state-title">Error Loading Conversation</h3>
                                <p class="empty-state-message">There was a problem retrieving this conversation</p>
                            </div>
                        `;
                        
                        chatSubtitleElement.textContent = 'Error loading conversation history';
                    });
            }
            
            // Render conversation history
            function renderConversationHistory(data) {
                if (!data.conversation_history || data.conversation_history.length === 0) {
                    messagesContainer.innerHTML = `
                        <div class="empty-state">
                            <div class="empty-state-icon">
                                <i class="fas fa-comment-slash"></i>
                            </div>
                            <h3 class="empty-state-title">No Messages</h3>
                            <p class="empty-state-message">This conversation doesn't have any messages yet</p>
                        </div>
                    `;
                    return;
                }
                
                let html = '';
                
                // Add user info section if available
                if (data.user_info && Object.keys(data.user_info).length > 0) {
                    const nonEmptyFields = Object.entries(data.user_info).filter(([key, value]) => 
                        value && String(value).trim()
                    );
                    
                    if (nonEmptyFields.length > 0) {
                        html += `
                            <div class="user-info">
                                <div class="user-info-header">
                                    <div class="user-info-icon">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div>
                                        <h3 class="user-info-title">User Information</h3>
                                        <p class="user-info-subtitle">Collected during conversation</p>
                                    </div>
                                </div>
                                <div class="info-grid">
                        `;
                        
                        nonEmptyFields.forEach(([key, value]) => {
                            const formattedKey = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                            html += `
                                <div class="info-item">
                                    <div class="info-label">${formattedKey}</div>
                                    <div class="info-value">${value}</div>
                                </div>
                            `;
                        });
                        
                        html += `
                                </div>
                            </div>
                        `;
                    }
                }
                
                // Group messages by date
                const messageGroups = groupMessagesByDate(data.conversation_history);
                
                // Sort dates chronologically (oldest first)
                const sortedDates = Object.keys(messageGroups).sort();
                
                // Add conversation with date separators
                sortedDates.forEach(dateKey => {
                    const messages = messageGroups[dateKey];
                    const formattedDate = dayjs(dateKey).format('MMMM D, YYYY');
                    
                    // Add date separator
                    html += `
                        <div class="conversation-date">
                            <span>${formattedDate}</span>
                        </div>
                    `;
                    
                    // Add messages for this date
                    messages.forEach(message => {
                        const isUser = message.role === 'user';
                        const wrapperClass = isUser ? 'user' : 'assistant';
                        
                        html += `
                            <div class="message-wrapper ${wrapperClass}">
                                <div class="message">
                                    ${message.content}
                                </div>
                                <div class="message-time">
                                    ${isUser ? 'User' : 'Assistant'} 
                                </div>
                            </div>
                        `;
                    });
                });
                
                messagesContainer.innerHTML = html;
                
                // Scroll to the bottom of the messages
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }
            
            // Search sessions
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase().trim();
                if (searchTerm === '') {
                    renderSessionsList(sessions);
                    return;
                }
                
                const filteredSessions = sessions.filter(session => {
                    return session.phone_number.toLowerCase().includes(searchTerm);
                });
                
                renderSessionsList(filteredSessions);
            });
            
            // Refresh button
            refreshButton.addEventListener('click', function() {
                this.disabled = true;
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
                
                loadSessions();
                
                if (currentPhoneNumber) {
                    loadSessionHistory(currentPhoneNumber);
                }
                
                setTimeout(() => {
                    this.disabled = false;
                    this.innerHTML = '<i class="fas fa-sync-alt"></i> Refresh Data';
                }, 1000);
            });
            
            // Initial load
            loadSessions();
        });
    </script>
</body>
</html> 
</html> 