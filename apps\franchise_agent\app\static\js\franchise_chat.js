// Global variables
let phoneNumber = null;
let isLoading = false;

// DOM elements
const messagesContainer = document.getElementById('messages');
const userInput = document.getElementById('user-input');
const sendButton = document.getElementById('send-button');
const phoneNumberInput = document.getElementById('phone-number');
const clearSessionButton = document.getElementById('clear-session');
const loadingIndicator = document.getElementById('loading-indicator');
const refreshChatButton = document.getElementById('refresh-chat');
const toggleInfoButton = document.getElementById('toggle-info');
const infoContent = document.querySelector('.info-content');

// Info panel elements
const businessTypeValue = document.getElementById('business-type-value');
const investmentCapacityValue = document.getElementById('investment-capacity-value');
const locationValue = document.getElementById('location-value');
const experienceValue = document.getElementById('experience-value');
const leadScoreValue = document.getElementById('lead-score-value');
const leadQuality = document.getElementById('lead-quality');

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    // Set up event listeners
    sendButton.addEventListener('click', sendMessage);
    userInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            sendMessage();
        }
    });
    
    clearSessionButton.addEventListener('click', clearSession);
    refreshChatButton.addEventListener('click', refreshChat);
    toggleInfoButton.addEventListener('click', toggleInfo);
    
    // Check for phone number in local storage
    const savedPhoneNumber = localStorage.getItem('franchiseAgentPhoneNumber');
    if (savedPhoneNumber) {
        phoneNumber = savedPhoneNumber;
        phoneNumberInput.value = phoneNumber;
        startConversation();
    }
    
    // Listen for phone number changes
    phoneNumberInput.addEventListener('change', () => {
        phoneNumber = phoneNumberInput.value.trim();
        if (phoneNumber) {
            localStorage.setItem('franchiseAgentPhoneNumber', phoneNumber);
            refreshChat();
        }
    });
});

// Helper function to create WhatsApp-compatible request payload
function createWhatsAppPayload(phoneNumber, messageText = '') {
    const timestamp = Math.floor(Date.now() / 1000).toString();
    const messageId = 'WEB_MSG_' + Date.now();
    
    return {
        entry: [
            {
                changes: [
                    {
                        field: "messages",
                        value: {
                            contacts: [
                                {
                                    profile: {
                                        name: "WebUser"
                                    },
                                    wa_id: phoneNumber
                                }
                            ],
                            messages: [
                                {
                                    from: phoneNumber,
                                    id: messageId,
                                    timestamp: timestamp,
                                    type: "text",
                                    text: {
                                        body: messageText
                                    }
                                }
                            ],
                            messaging_product: "whatsapp",
                            metadata: {
                                display_phone_number: "WEB",
                                phone_number_id: "WEB_INTERFACE"
                            }
                        }
                    }
                ],
                id: "web_interface"
            }
        ],
        object: "whatsapp_business_account",
        // Include backward compatibility fields
        from: phoneNumber,
        message: messageText,
        force_restart: false
    };
}

// Send a message to the agent
async function sendMessage() {
    // Get the message from the input
    const message = userInput.value.trim();
    
    // Don't send empty messages
    if (!message) return;
    
    // Check if we have a phone number
    if (!phoneNumber) {
        phoneNumber = phoneNumberInput.value.trim();
        if (!phoneNumber) {
            alert('Please enter your phone number to start the conversation.');
            phoneNumberInput.focus();
            return;
        }
        localStorage.setItem('franchiseAgentPhoneNumber', phoneNumber);
    }
    
    // Add the message to the chat
    addMessage('user', message);
    
    // Clear the input
    userInput.value = '';
    
    // Show loading indicator
    setLoading(true);
    
    try {
        // Create WhatsApp-compatible payload
        const payload = createWhatsAppPayload(phoneNumber, message);
        
        // Send the message to the server
        const response = await fetch('/api/franchise/continue', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(payload)
        });
        
        // Parse the response
        const data = await response.json();
        
        // Check if the request was successful
        if (data.success) {
            // Add the response to the chat
            addMessage('assistant', data.message);
            
            // Log additional data if present
            if (data.current_agent) {
                console.log('Current Agent:', data.current_agent);
            }
            if (data.current_node) {
                console.log('Current Node:', data.current_node);
            }
            
            // Update the state information if still using the old format
            if (data.state_updates && Array.isArray(data.state_updates)) {
                updateStateInfo(data.state_updates);
            }
        } else {
            // Show error message
            addMessage('assistant', data.message || 'Sorry, I encountered an error. Please try again.');
        }
    } catch (error) {
        console.error('Error sending message:', error);
        addMessage('assistant', 'Sorry, I encountered an error. Please try again.');
    } finally {
        // Hide loading indicator
        setLoading(false);
    }
}

// Start a new conversation
async function startConversation(forceRestart = false) {
    // Check if we have a phone number
    if (!phoneNumber) {
        phoneNumber = phoneNumberInput.value.trim();
        if (!phoneNumber) {
            return;
        }
        localStorage.setItem('franchiseAgentPhoneNumber', phoneNumber);
    }
    
    // Show loading indicator
    setLoading(true);
    
    try {
        // Create WhatsApp-compatible payload
        const payload = createWhatsAppPayload(phoneNumber);
        
        // Add force_restart flag for backward compatibility
        payload.force_restart = forceRestart;
        
        // Send the start request to the server
        const response = await fetch('/api/franchise/start', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(payload)
        });
        
        // Parse the response
        const data = await response.json();
        
        // Check if the request was successful
        if (data.success) {
            // Clear the chat
            messagesContainer.innerHTML = '';
            
            // Add the welcome message to the chat
            addMessage('assistant', data.message);
            
            // Log additional data if present
            if (data.current_agent) {
                console.log('Current Agent:', data.current_agent);
            }
            if (data.current_node) {
                console.log('Current Node:', data.current_node);
            }
            
            // Update the state information if still using the old format
            if (data.state_updates && Array.isArray(data.state_updates)) {
                updateStateInfo(data.state_updates);
            }
        } else {
            // Show error message
            alert(data.message || 'Sorry, I encountered an error starting the conversation.');
        }
    } catch (error) {
        console.error('Error starting conversation:', error);
        alert('Sorry, I encountered an error starting the conversation.');
    } finally {
        // Hide loading indicator
        setLoading(false);
    }
}

// Clear the current session
async function clearSession() {
    // Check if we have a phone number
    if (!phoneNumber) {
        phoneNumber = phoneNumberInput.value.trim();
        if (!phoneNumber) {
            alert('Please enter your phone number to clear the session.');
            phoneNumberInput.focus();
            return;
        }
    }
    
    // Confirm with the user
    if (!confirm('Are you sure you want to clear your session? This will delete all your conversation history.')) {
        return;
    }
    
    try {
        // Send the clear request to the server
        const response = await fetch(`/api/franchise/clear/${phoneNumber}`, {
            method: 'POST'
        });
        
        // Parse the response
        const data = await response.json();
        
        // Check if the request was successful
        if (data.success) {
            // Clear the chat
            messagesContainer.innerHTML = '';
            
            // Reset the state information
            resetStateInfo();
            
            // Start a new conversation
            startConversation(true);
        } else {
            // Show error message
            alert(data.message || 'Sorry, I encountered an error clearing the session.');
        }
    } catch (error) {
        console.error('Error clearing session:', error);
        alert('Sorry, I encountered an error clearing the session.');
    }
}

// Refresh the chat
function refreshChat() {
    // Start a new conversation without forcing restart
    startConversation(false);
}

// Toggle the info panel
function toggleInfo() {
    // Toggle the info content visibility
    infoContent.style.display = infoContent.style.display === 'none' ? 'grid' : 'none';
    
    // Toggle the icon
    const icon = toggleInfoButton.querySelector('i');
    icon.classList.toggle('fa-chevron-up');
    icon.classList.toggle('fa-chevron-down');
}

// Add a message to the chat
function addMessage(role, content) {
    // Create message wrapper
    const messageWrapper = document.createElement('div');
    messageWrapper.classList.add('message-wrapper');
    if (role === 'user') {
        messageWrapper.classList.add('user-message-wrapper');
    }
    
    // Create avatar
    const avatar = document.createElement('div');
    avatar.classList.add('avatar');
    avatar.classList.add(`${role}-avatar`);
    avatar.innerHTML = role === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';
    
    // Create message
    const message = document.createElement('div');
    message.classList.add('message');
    message.classList.add(`${role}-message`);
    message.textContent = content;
    
    // Assemble the message
    messageWrapper.appendChild(avatar);
    messageWrapper.appendChild(message);
    
    // Add to the chat
    messagesContainer.appendChild(messageWrapper);
    
    // Scroll to the bottom
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// Set the loading state
function setLoading(loading) {
    isLoading = loading;
    loadingIndicator.style.display = loading ? 'flex' : 'none';
    userInput.disabled = loading;
    sendButton.disabled = loading;
}

// Update the state information
function updateStateInfo(stateUpdates) {
    if (!stateUpdates || !stateUpdates.length) return;
    
    stateUpdates.forEach(update => {
        const key = update.key;
        const value = update.value;
        const action = update.action;
        
        if (action === 'delete') {
            // Handle delete action
            switch (key) {
                case 'business_type':
                    businessTypeValue.textContent = 'Not specified';
                    break;
                case 'investment_capacity':
                    investmentCapacityValue.textContent = 'Not specified';
                    break;
                case 'location':
                    locationValue.textContent = 'Not specified';
                    break;
                case 'experience':
                    experienceValue.textContent = 'Not specified';
                    break;
                case 'lead_score':
                    leadScoreValue.textContent = 'Not scored';
                    leadQuality.textContent = 'Not classified';
                    leadQuality.className = 'lead-quality';
                    break;
            }
        } else {
            // Handle update action
            switch (key) {
                case 'business_type':
                    businessTypeValue.textContent = value;
                    break;
                case 'investment_capacity':
                    investmentCapacityValue.textContent = `$${value.toLocaleString()}`;
                    break;
                case 'location':
                    locationValue.textContent = value;
                    break;
                case 'experience':
                    experienceValue.textContent = value;
                    break;
                case 'lead_score':
                    leadScoreValue.textContent = `${value}/100`;
                    break;
                case 'lead_quality':
                    leadQuality.textContent = value;
                    leadQuality.className = 'lead-quality';
                    leadQuality.classList.add(value.toLowerCase());
                    break;
            }
        }
    });
}

// Reset the state information
function resetStateInfo() {
    businessTypeValue.textContent = 'Not specified';
    investmentCapacityValue.textContent = 'Not specified';
    locationValue.textContent = 'Not specified';
    experienceValue.textContent = 'Not specified';
    leadScoreValue.textContent = 'Not scored';
    leadQuality.textContent = 'Not classified';
    leadQuality.className = 'lead-quality';
}
