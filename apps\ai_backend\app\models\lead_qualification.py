from sqlalchemy import Column, String, Integer, DateTime, Text, Boolean, Float, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime
import json

Base = declarative_base()

class LeadQualificationResult(Base):
    """Model for storing lead qualification results"""
    __tablename__ = "lead_qualification_results"

    id = Column(Integer, primary_key=True, autoincrement=True)
    phone_number = Column(String(20), nullable=False, index=True)
    
    # Lead classification
    category = Column(String(20), nullable=False)  # Hot/Warm/Cold
    total_score = Column(Integer, nullable=False, default=0)
    confidence = Column(Float, nullable=False, default=0.0)
    
    # Individual dimension scores
    primary_use_score = Column(Integer, nullable=True)
    model_selection_score = Column(Integer, nullable=True)
    purchase_timeline_score = Column(Integer, nullable=True)
    budget_range_score = Column(Integer, nullable=True)
    interest_level_score = Column(Integer, nullable=True)
    
    # Extracted details
    primary_use = Column(String(50), nullable=True)
    model_selection = Column(String(50), nullable=True)
    purchase_timeline = Column(String(50), nullable=True)
    budget_range = Column(String(50), nullable=True)
    interest_level = Column(String(100), nullable=True)
    selected_model = Column(String(100), nullable=True)
    
    # Analysis results
    rationale = Column(Text, nullable=True)
    recommendation = Column(Text, nullable=True)
    qualification_report = Column(Text, nullable=True)
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = Column(Boolean, default=True)  # Track if the qualification is active
    
    def to_dict(self):
        """Convert to dictionary"""
        return {
            "id": self.id,
            "phone_number": self.phone_number,
            "category": self.category,
            "total_score": self.total_score,
            "confidence": self.confidence,
            "scores": {
                "primary_use": self.primary_use_score,
                "model_selection": self.model_selection_score,
                "purchase_timeline": self.purchase_timeline_score,
                "budget_range": self.budget_range_score,
                "interest_level": self.interest_level_score
            },
            "lead_details": {
                "primary_use": self.primary_use,
                "model_selection": self.model_selection,
                "purchase_timeline": self.purchase_timeline,
                "budget_range": self.budget_range,
                "interest_level": self.interest_level,
                "selected_model": self.selected_model,
            },
            "rationale": self.rationale,
            "recommendation": self.recommendation,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "is_active": self.is_active
        }
    
    def __repr__(self):
        return f"<LeadQualificationResult(id={self.id}, phone_number={self.phone_number}, category={self.category})>" 