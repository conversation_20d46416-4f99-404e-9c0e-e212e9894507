/* Base Styles */
:root {
    --primary-color: #4361ee;
    --primary-light: #4895ef;
    --primary-dark: #3f37c9;
    --secondary-color: #f72585;
    --text-color: #333;
    --text-light: #666;
    --bg-color: #f8f9fa;
    --card-bg: #ffffff;
    --border-color: #e0e0e0;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --sidebar-width: 260px;
    --header-height: 80px;
    --border-radius: 12px;
    --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background-color: var(--bg-color);
    color: var(--text-color);
    line-height: 1.6;
}

/* App Container */
.app-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.sidebar {
    width: var(--sidebar-width);
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    color: white;
    display: flex;
    flex-direction: column;
    padding: 20px;
    position: fixed;
    height: 100vh;
    left: 0;
    top: 0;
    z-index: 10;
    box-shadow: var(--box-shadow);
}

.logo-container {
    display: flex;
    align-items: center;
    padding: 20px 0;
    margin-bottom: 30px;
}

.logo {
    width: 40px;
    height: 40px;
    background-color: white;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
}

.logo i {
    color: var(--primary-color);
    font-size: 20px;
}

.logo-container h2 {
    font-weight: 600;
    font-size: 1.5rem;
}

.sidebar-menu {
    flex: 1;
}

.menu-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-radius: var(--border-radius);
    margin-bottom: 10px;
    cursor: pointer;
    transition: var(--transition);
}

.menu-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.menu-item.active {
    background-color: rgba(255, 255, 255, 0.2);
}

.menu-item i {
    margin-right: 15px;
    font-size: 18px;
}

.sidebar-footer {
    margin-top: auto;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.user-info {
    display: flex;
    align-items: center;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
}

.user-name {
    font-weight: 500;
}

.user-status {
    font-size: 0.8rem;
    opacity: 0.8;
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    padding: 20px;
    display: flex;
    flex-direction: column;
    height: 100vh;
}

/* Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
}

.page-title h1 {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--primary-dark);
}

.page-title p {
    color: var(--text-light);
    font-size: 0.9rem;
}

.header-actions {
    display: flex;
    align-items: center;
}

.phone-selection {
    display: flex;
    align-items: center;
    gap: 10px;
    background-color: var(--card-bg);
    padding: 10px 15px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.phone-selection label {
    font-weight: 500;
    color: var(--text-light);
}

#phone-number {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 0.9rem;
    width: 180px;
}

.clear-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: var(--transition);
}

.clear-btn:hover {
    background-color: var(--primary-dark);
}

/* Chat Container */
.chat-container {
    flex: 1;
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    margin-bottom: 20px;
}

.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: var(--primary-color);
    color: white;
}

.chat-title {
    display: flex;
    align-items: center;
}

.chat-title i {
    margin-right: 10px;
    font-size: 18px;
}

.chat-title span {
    font-weight: 500;
    font-size: 1.1rem;
}

.chat-actions .action-btn {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 16px;
    opacity: 0.8;
    transition: var(--transition);
}

.chat-actions .action-btn:hover {
    opacity: 1;
}

.messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.message-wrapper {
    display: flex;
    margin-bottom: 15px;
}

.user-message-wrapper {
    flex-direction: row-reverse;
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: white;
}

.user-message-wrapper .avatar {
    margin-right: 0;
    margin-left: 10px;
}

.user-avatar {
    background-color: var(--primary-color);
}

.assistant-avatar {
    background-color: var(--secondary-color);
}

.message {
    padding: 12px 16px;
    border-radius: 18px;
    max-width: 70%;
    word-wrap: break-word;
}

.user-message {
    background-color: var(--primary-light);
    color: white;
    border-bottom-right-radius: 4px;
    align-self: flex-end;
}

.assistant-message {
    background-color: #f0f2f5;
    color: var(--text-color);
    border-bottom-left-radius: 4px;
    align-self: flex-start;
}

.loading {
    display: none;
    padding: 10px;
    text-align: center;
}

.typing-indicator {
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.typing-indicator span {
    height: 8px;
    width: 8px;
    margin: 0 2px;
    background-color: var(--text-light);
    border-radius: 50%;
    display: inline-block;
    animation: typing 1.4s infinite ease-in-out both;
}

.typing-indicator span:nth-child(1) {
    animation-delay: 0s;
}

.typing-indicator span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0% {
        transform: scale(1);
        opacity: 0.7;
    }
    50% {
        transform: scale(1.5);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 0.7;
    }
}

.input-container {
    display: flex;
    padding: 15px;
    border-top: 1px solid var(--border-color);
}

#user-input {
    flex: 1;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 24px;
    font-size: 0.95rem;
    outline: none;
    transition: var(--transition);
}

#user-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.2);
}

#send-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    margin-left: 10px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

#send-button:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
}

#send-button i {
    font-size: 18px;
}

/* Info Panel */
.info-panel {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: var(--primary-color);
    color: white;
}

.info-header h3 {
    font-weight: 500;
    font-size: 1.1rem;
}

.toggle-btn {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 16px;
    transition: var(--transition);
}

.info-content {
    padding: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
}

.info-section {
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid var(--primary-color);
}

.info-section h4 {
    font-size: 0.9rem;
    color: var(--text-light);
    margin-bottom: 8px;
}

.info-value {
    font-weight: 500;
    font-size: 1.1rem;
}

.score-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.lead-quality {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
    color: white;
    background-color: var(--text-light);
}

.lead-quality.hot {
    background-color: var(--danger-color);
}

.lead-quality.warm {
    background-color: var(--warning-color);
}

.lead-quality.cold {
    background-color: var(--primary-light);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .sidebar {
        width: 80px;
        padding: 20px 10px;
    }
    
    .logo-container {
        justify-content: center;
    }
    
    .logo-container h2,
    .menu-item span,
    .user-details {
        display: none;
    }
    
    .menu-item {
        justify-content: center;
    }
    
    .menu-item i {
        margin-right: 0;
    }
    
    .main-content {
        margin-left: 80px;
    }
    
    .user-info {
        justify-content: center;
    }
    
    .user-avatar {
        margin-right: 0;
    }
}

@media (max-width: 768px) {
    .header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .header-actions {
        margin-top: 15px;
        width: 100%;
    }
    
    .phone-selection {
        width: 100%;
        flex-wrap: wrap;
    }
    
    .info-content {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .sidebar {
        display: none;
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .message {
        max-width: 85%;
    }
}
