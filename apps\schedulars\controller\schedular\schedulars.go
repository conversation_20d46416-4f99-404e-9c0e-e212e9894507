package schedular

import (
	env "apps/schedulars/config"
	"encoding/json"
	"fmt"
	shared "libs/shared"
	"libs/shared/db_connectors/model"
	whatsapp "libs/shared/db_connectors/model"

	"github.com/robfig/cron/v3"
	"gorm.io/datatypes"
	"time"
)

func UpsertSchedulars() error {
	fmt.Println("=====ENTERED UPSERT SCHEDULAR=====")
	// schedularCollectionName := shared.PostgresCollectionNames["SCHEDULARS"]

	for _, schedular := range schedularsArr {

		filterQuery := map[string]interface{}{
			"code": schedular.Code,
		}
		SchedulerData, err := shared.NewCommonRepository(whatsapp.Schedular{}).FindOne(
			env.GlobalEnv["POSTGRES_CREDENTIAL"], filterQuery,
		)
		fmt.Println("SchedulerData---", SchedulerData)
             
		if SchedulerData.ID != 0 {
			continue
		}

		if err == nil {
			continue
		}

		// ... existing code ...
        newSchedular := &whatsapp.Schedular{
            Name:        schedular.Name,
            Code:        schedular.Code,
            LastRunTime: schedular.LastRunTime,
            SchedulingTime: datatypes.JSON(func() []byte {
                b, _ := json.Marshal(SchedulingTime{
                    FrequencyType: schedular.SchedulingTime.FrequencyType,
                    Time:         schedular.SchedulingTime.Time,
                    Hours:        schedular.SchedulingTime.Hours,
                    Weekdays:     schedular.SchedulingTime.Weekdays,
                })
                return b
            }()),
            Type:      schedular.Type,
            IsActive:  schedular.IsActive,
            IsEnabled: schedular.IsEnabled,
        }
// ... existing code ...

		err = shared.NewCommonRepository(whatsapp.Schedular{}).Save(
			env.GlobalEnv["POSTGRES_CREDENTIAL"],
			newSchedular,
		)

		if err != nil {
			return fmt.Errorf("error creating schedular: %v", err)
		}
	}

	return nil
}


func isScheduledTime(schedular model.Schedular) bool {
    fmt.Println("=====ENTERED isScheduledTime SCHEDULAR=====")
    now := time.Now()
    currentHour := now.Hour()
    currentMinute := now.Minute()
    // currentDay := now.Weekday()

    if schedular.LastRunTime == nil {
        return true
    }
    fmt.Println("schedular.LastRunTime", schedular.LastRunTime, currentHour)

    lastRunTime := schedular.LastRunTime.Local()
    fmt.Println("schedular", schedular)
    
   var jsonString string
if err := json.Unmarshal(schedular.SchedulingTime, &jsonString); err != nil {
    fmt.Println("1st unmarshal error (to string)-------", err)
    return false
}

var schedulingTime SchedulingTime
if err := json.Unmarshal([]byte(jsonString), &schedulingTime); err != nil {
    fmt.Println("2nd unmarshal error (to struct)-------", err)
    return false
}
    fmt.Println("FrequencyType", schedulingTime.FrequencyType)
    switch schedulingTime.FrequencyType {
    case "MINUTE":
        diffInMinutes := now.Sub(lastRunTime).Minutes()
        return int(diffInMinutes) >= schedulingTime.Time

    case "HOUR":
        fmt.Println("schedular.SchedulingTime.Hours", schedulingTime.Hours)
        fmt.Println("int32(currentHour)) ", int32(currentHour))
        diffInHours := now.Sub(lastRunTime).Hours()
		// diffInHours=1
        fmt.Println("diffInHours", diffInHours)
        
        for _, hour := range schedulingTime.Hours {
			fmt.Println("hour", hour,currentHour)
            if hour == currentHour && diffInHours >= 1 {
                return true
            }
        }
        return false

    case "WEEKDAYS":
        lastRunHour := lastRunTime.Hour()
        lastRunDate := lastRunTime.Day()
        currentDate := now.Day()

        weekdayMatch := false
        // for _, weekday := range schedulingTime.Weekdays {
        //     if weekday == currentDay {
        //         weekdayMatch = true
        //         break
        //     }
        // }

        if weekdayMatch {
            hourMatch := false
            for _, hour := range schedulingTime.Hours {
                if hour == currentHour {
                    hourMatch = true
                    break
                }
            }

            if hourMatch {
                if currentDate != lastRunDate || (currentDate == lastRunDate && currentHour != lastRunHour) {
                    return currentMinute >= schedulingTime.Time
                }
            }
        }
        return false

    default:
        return false
    }
}




func SchedularInit() {
	fmt.Println("=======>>> Schedular started <<<=========")
	schedularCollectionName := shared.PostgresCollectionNames["SCHEDULAR"]
	// schedularLogsCollectionName := shared.PostgresCollectionNames["SCHEDULAR_LOGS"]

	cron := cron.New()
	cron.AddFunc("*/30 * * * *", func() {
		fmt.Println("Cron Running=======>>>>")

		schedulars, err := shared.PostgresRepository().GetManyWithPagination(
	        env.GlobalEnv["POSTGRES_CREDENTIAL"],
			schedularCollectionName,
			map[string]interface{}{},
			map[string]interface{}{},
			-1,
			1,
			
		)
		fmt.Println("schedulars-- ---", schedulars)
		
		// if err != nil {
		// 	fmt.Errorf("Error fetching schedulars: %v\n", err)
		// }

		var schedulars_document []whatsapp.Schedular
		shared.JsonMarshaller(schedulars["data"], &schedulars_document)

		for _, schedular := range schedulars_document {
			fmt.Println("single schedular data-- ---", schedular)
			currentTime := time.Now()
			fmt.Println("schedular.Code", schedular.Code)
			fmt.Println("schedular.LastRunTime", isScheduledTime(schedular))
			if schedular.Code == "CLEAR_WEBHOOK_DATA" && isScheduledTime(schedular) {
				fmt.Printf("CLEAR_WEBHOOK_DATA started\n")

				updateSchedular := &whatsapp.Schedular{
					LastRunTime: &currentTime,
				}

				filterQuery := map[string]interface{}{
					"id": schedular.ID,
				}

				// _, err = shared.PostgresRepository().UpdateOne(env.GlobalEnv["MONGO_CREDENTIAL"], schedularCollectionName, schedularUpdatePayload, filterQuery)
				err = shared.NewCommonRepository(whatsapp.Schedular{}).Update(
					env.GlobalEnv["POSTGRES_CREDENTIAL"],
					filterQuery,
					updateSchedular,
				)
				if err != nil {
					fmt.Errorf("Error updating last_run_time: %v\n", err)
				}
				newSchedularLogs := &whatsapp.SchedularLogs{
					SchedularID:            schedular.ID,
					StartTime:     currentTime,
					Status:        "In-progress",
					SchedularName: schedular.Code,
				}
				// logData, err := shared.MongoRepository().CreateOne(env.GlobalEnv["MONGO_CREDENTIAL"], schedularLogsCollectionName, createLogPayload)
				err := shared.NewCommonRepository(whatsapp.SchedularLogs{}).Save(
					env.GlobalEnv["POSTGRES_CREDENTIAL"],
					newSchedularLogs,
				)
				if err != nil {
					fmt.Errorf("Error creating schedular log: %v\n", err)
				}
				var logDataResp = SchedularLogs{}
				shared.JsonMarshaller(newSchedularLogs, &logDataResp)
				go clearWebhookData(logDataResp.ID)
			}
		}

	})

	cron.Start()
	select {}
}
func clearWebhookData(schedularLogID int) error {
    fmt.Println("=======>>> clearWebhookData started <<<=========")
    // Calculate time 48 hours ago
	webhookCollectionName := shared.PostgresCollectionNames["WHATSAPP_WEBHOOK"]
    time48HoursAgo := time.Now().Add(-100 * time.Hour)

    // Query for records older than 48 hours
	filterQuery := fmt.Sprintf("created_date < '%s'", time48HoursAgo.Format("2006-01-02 15:04:05.999999-07:00"))
    fmt.Printf("webhook query: %s\n", filterQuery)


	_,err := shared.PostgresRepository().DeleteMany(
        env.GlobalEnv["POSTGRES_CREDENTIAL"],
		webhookCollectionName,
        filterQuery,
    )
    if err != nil {
        return fmt.Errorf("error deleting course cache: %v", err)
    }

    // Update schedular log
    currentTime := time.Now()
    newSchedularLogs := &whatsapp.SchedularLogs{
        EndTime: currentTime,
        Status:  "completed",
    }

    filterQuery1 := map[string]interface{}{
        "id": schedularLogID,
    }

    err = shared.NewCommonRepository(whatsapp.SchedularLogs{}).Update(
        env.GlobalEnv["POSTGRES_CREDENTIAL"],
        filterQuery1,
        newSchedularLogs,
    )
    if err != nil {
        return fmt.Errorf("error updating schedular log: %v", err)
    }

    return nil
}
func InitiateSchedular(schedularName string) {
	fmt.Println("=======>>> Schedular initiated <<<=========")
}
