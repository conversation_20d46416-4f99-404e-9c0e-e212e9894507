package whatsapp

import (
	"encoding/base64"

	"encoding/json"
	"errors"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"runtime"

	"net/http"

	"strings"
	"time"

	whatsapp_helpers "apps/spiro/controllers/whatsapp/helpers"

	whatsapp "libs/shared/db_connectors/model"

	"github.com/google/uuid"
	// "gorm.io/datatypes"

	users "apps/spiro/controllers/whatsapp/users"
	whatsapp_repository "libs/shared/db_connectors/repository/whatsapp"

	helpers "libs/shared/utils/helpers"
	// "libs/shared/db_connectors/db"

	env "apps/spiro/config"
	shared "libs/shared"
)

type Webhook interface {
	Webhook(data WebhookRequest) error
	SendToWhatsapp(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact, responses []interface{}) error
	RemoveCountryCode(str string) (string, error)
	FormatMessageRasaToWhatsapp(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact, rasaResponses []RasaResponse) ([]interface{}, error)
	IntialMessage(data MessageRequest) error
	MessageResponseProcessing(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact, message MessageFromWhatsapp) ([]RasaResponse, error)
}

type webhook struct {
	helperService      whatsapp_helpers.Service
	userFlowService    UserFlowService
	sellerFlowsService users.UserFlowsService

	db shared.PostgresRepositoryFunctions
	accountRepository whatsapp_repository.WhatsappRepository[whatsapp.WhatsappAccount]
    contactRepository whatsapp_repository.WhatsappRepository[whatsapp.WhatsappContact]
    messageRepository whatsapp_repository.WhatsappRepository[whatsapp.WhatsappMessage]
    flowRepository   whatsapp_repository.WhatsappRepository[whatsapp.WhatsappFlow]

	contact_Repository             whatsapp_repository.WhatsappContact
}

var webhookObj *webhook

func NewWebhook() *webhook {
	if webhookObj != nil {
		return webhookObj
	}
	webhookObj = &webhook{
		helperService:      whatsapp_helpers.NewService(),
		userFlowService:    NewUserFlowService(),
		sellerFlowsService: users.NewUserFlowsService(),
		db:                 shared.PostgresRepository(),
		accountRepository:  whatsapp_repository.NewRepository(whatsapp.WhatsappAccount{}),
		contactRepository:  whatsapp_repository.NewRepository(whatsapp.WhatsappContact{}),
		messageRepository:  whatsapp_repository.NewRepository(whatsapp.WhatsappMessage{}),
		flowRepository:     whatsapp_repository.NewRepository(whatsapp.WhatsappFlow{}),

		contact_Repository:             whatsapp_repository.NewWhatsappContactRepository(),
	}
	return webhookObj
}

func (s *webhook) Webhook(data WebhookRequest) error {

	// helpers.PrettyPrint("WEBHOOK DATA---------", data)

	message, err := s.GetLastMessage(data)
	if err != nil {
		return err
	}

	fmt.Println("Checkpoint : Last Message",data.Entry[0].Changes[0].Value.Metadata.DisplayPhoneNumber)

	accountPhoneNumber := data.Entry[0].Changes[0].Value.Metadata.DisplayPhoneNumber
	accountPhoneNumber, err = s.RemoveCountryCode(accountPhoneNumber)
	if err != nil {
		return err
	}
	fmt.Println("accountPhoneNumber", accountPhoneNumber)

	account, err := s.accountRepository.FindOne(env.GlobalEnv["POSTGRES_CREDENTIAL"], map[string]interface{}{
		"whatsapp_number": accountPhoneNumber,
	})
	if err != nil {
		return err
	}
	fmt.Println("Checkpoint : Account", account)

	fmt.Println("Checkpoint : Account", account.WhatsappNumber)

	fmt.Println("Checkpoint1 : RasaFlow", account.RasaFlow)

	message, err = s.ProcessMediaMessage(account, message)
	if err != nil {
		return err
	}
	fmt.Println("Checkpoint : Process Media")

	err = s.UpdateChatHistoryFromUser(data, account, message)
	if err != nil {
		return err
	}

	fmt.Println("Checkpoint : Update Chat History")

	// userPhoneNumber, err := s.RemoveCountryCode(message.From)
	// if err != nil {
	// 	return err
	// }
	userPhoneNumber := message.From
	fmt.Println("userPhoneNumber",userPhoneNumber)

	contact, err := s.contact_Repository.FindOne(env.GlobalEnv["POSTGRES_CREDENTIAL"], map[string]interface{}{
		"whatsapp_number":     userPhoneNumber,
		"whatsapp_account_id": account.ID,
	})


	if err != nil {
		return err
	}


	fmt.Println("Checkpoint : Contact", contact.WhatsappNumber)


	err = s.UpdateUserChatHistoryForSummary(account, contact, message)
	if err != nil {
		return err
	}

	fmt.Println("Checkpoint : UpdateUserChatHistoryForSummary", contact.MiddlewareUserId)

	var responses []RasaResponse
	var userStausId uint
	// dbConn := db.GetProstgressConnection(env.GlobalEnv["POSTGRES_CREDENTIAL"])
	// if dbConn == nil {
	// 	panic("Failed to connect to database")
	// }else{
	// 	fmt.Println("Connected to database")
	// }
	userStausId,_ = helpers.GetLookupcodeId("USER_STATUS", "REJECTED",env.GlobalEnv["POSTGRES_CREDENTIAL"])
	fmt.Println("userStausId-----------", userStausId)
	fmt.Println("contactStatusId-----------", contact.StatusId)


	if account.RasaFlow.Code == "LEAD_FLOW" {
   		messageTypeResponses := map[string]string{
            "document":    "Please upload in image format",
            "video":      "Video format not accepted.",
			"location":    "Location sharing not supported. Please send a text message.",
            "sticker":    "Stickers are not supported. Please send a text message.",
            "contacts":   "Contacts are not supported. Please send a text message.",
            "unsupported": "Unsupported format. Please send a text message.",
        }

        if responseText, exists := messageTypeResponses[message.Type]; exists {
            // if message.Type != "document" {
			// }
			fmt.Printf("%s Format-------------------\n", strings.Title(message.Type))
            responses = []RasaResponse{{
                RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
                Text:        responseText,
            }}
        }else if message.Text != nil || message.Audio != nil {
			if(contact.StatusId == nil || userStausId != *contact.StatusId){
				responses, err = s.MessageResponseProcessing(account, contact, message)
				if err != nil {
					return err
				}
			}else{
				responses = []RasaResponse{{
					RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
					Text:        "Thank you for your response. We completely respect your decision and appreciate you taking the time to consider our request",
				}}
			}

		} else if message.Image != nil {
			fmt.Println("image----------------------")
			responses, err = s.ImageResponseProcessing(account, contact, message)
			if err != nil {
				return err
			}

		} else if message.Interactive != nil && message.Interactive.NfmReply != nil && message.Interactive.NfmReply.Name == "flow" {

			responses, err = s.userFlowService.UserProcessWhatsappFlow(account, contact, message)
			fmt.Println("nfm res------", responses)
			var flowResponse map[string]interface{}
			json.Unmarshal([]byte(message.Interactive.NfmReply.ResponseJson), &flowResponse)

			if flowResponse["flow_token"] != nil {

				if strings.Contains(flowResponse["flow_token"].(string), "ProductManagement") {

					if len(responses) > 0 && strings.Contains(responses[0].Text, "Product_Details") {
						productMessage := MessageFromWhatsapp{
							From: message.From,
							Type: "text",
							Text: &MessageFromWhatsappText{
								// Body: strings.ReplaceAll(responses[0].Text, "Product Details: ", ""),
								Body: "Ill select " + strings.ReplaceAll(responses[0].Text, "Product_Details: ", ""),
							},
						}
						fmt.Println("message--------------", productMessage)
						responses, err = s.MessageResponseProcessing(account, contact, productMessage)
						if err != nil {
							return err
						}
					}
				}

				if strings.Contains(flowResponse["flow_token"].(string), "FeadAcquisitionOnboarding") {

					if len(responses) > 0 && strings.Contains(responses[0].Text, "You've onboarded successfully!") {
					
						responses,err = s.UserOnboardingProcessing(account, contact,message)		

					}
				}

			}
			if err != nil {
				return err
			}
		} else {
			return errors.New("unidentified message ")
		}
	}else if account.RasaFlow.Code == "FRANCHISE_FLOW" {
		stopWords := map[string]bool{
			"Stop": true,
			"Quit": true,
			"stop": true,
			"quit": true,
			"QUIT": true,
			"end":true,
			"End": true,
			"END": true,
		}
		if message.Text != nil && stopWords[message.Text.Body]{
			fmt.Println("Stopping the session-------------------")
            // if stopWords[message.Text.Body] {
				Contactquery := map[string]interface{}{
					"id": contact.ID,
				}
				contact.StatusId = &userStausId  // Convert uint to *uint by taking its address
				err = s.contact_Repository.Update(Contactquery, &contact)
				if err != nil {
					return  err
				}
                responses = []RasaResponse{{
                    RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
                    Text:        "Thank you for your response. We completely respect your decision and appreciate you taking the time to consider our request",
                }}
            // }
        }
		if(contact.StatusId == nil || userStausId != *contact.StatusId){

		 messageTypeResponses := map[string]string{
            "video":      "Video format not accepted.",
			"location":    "Location sharing not supported. Please send a text message.",
            "sticker":     "Stickers are not supported. Please send a text message.",
            "contacts":    "Contacts are not supported. Please send a text message.",
            "unsupported": "Unsupported format. Please send a text message.",
        }

        if responseText, exists := messageTypeResponses[message.Type]; exists {
			fmt.Printf("%s Format-------------------\n", strings.Title(message.Type))
            responses = []RasaResponse{{
                RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
                Text:        responseText,
            }}
        }else if message.Text != nil || message.Audio != nil {
			responses, err = s.FranchiseMessageResponseProcessing(account, contact, message)
			if err != nil {
				return err
		    }
		}else if (message.Document != nil){
			responses, err = s.FranchiseDocumentResponseProcessing(account, contact, message)
			if err != nil {
				return err
		    }
		}else {
			return errors.New("unidentified message")
		}
					
		}else{
			fmt.Println("Franchise user ended the session-------------------")
			responses = []RasaResponse{{
				RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
				Text:        "Thank you for your response. We completely respect your decision and appreciate you taking the time to consider our request",
			}}
		}
	}

	fmt.Println("Checkpoint : MessageResponseProcessing", responses)

	responses, err = s.UpsertWhatsappFlows(account, contact, responses)
	if err != nil {
		return err
	}

	formattedMessages, err := s.FormatMessageRasaToWhatsapp(account, contact, responses)
	if err != nil {
		return err
	}
	fmt.Println("Checkpoint : WhatsappFormat")

	err = s.SendToWhatsapp(account, contact, formattedMessages)
	if err != nil {
		return err
	}

	return nil
}

func (s *webhook) UpdateChatHistoryFromUser(data WebhookRequest, account whatsapp.WhatsappAccount, message MessageFromWhatsapp) error {
    fmt.Println("Checkpoint : UpdateChatHistoryFromUser")
	// messageFrom, err := s.RemoveCountryCode(message.From)
	// if err != nil {
	// 	return err
	// }
	messageFrom := message.From

	formattedMessage := s.FormatMessageHistoryFromUser(message)

	contact, err := s.contact_Repository.FindOne(env.GlobalEnv["POSTGRES_CREDENTIAL"],map[string]interface{}{
		"whatsapp_number":     messageFrom,
		"whatsapp_account_id": account.ID,
	})

	fmt.Println("Checkpoint : FindOne",messageFrom,account.ID)

	fmt.Println("Checkpoint : Contact", contact)

	if err != nil {
		fmt.Println("Checkpoint : FindOne Error", err)
		contact = whatsapp.WhatsappContact{
			WhatsappNumber: messageFrom,
			// Name:           s.GetContactName(data),
		}
		contact.CompanyId = account.CompanyId
		contact.WhatsappAccountId = account.ID


		fmt.Println("Checkpoint : Contact Payload-----------", contact)

		err = s.contactRepository.Save(env.GlobalEnv["POSTGRES_CREDENTIAL"],&contact)
		
		if err != nil {
			return err
		}
	}

	formattedMessage.WhatsappContactId = contact.ID
	err = s.messageRepository.Save(env.GlobalEnv["POSTGRES_CREDENTIAL"],&formattedMessage)

	
	if err != nil {
		return err
	}

	return nil
}

func (s *webhook) RemoveCountryCode(str string) (string, error) {
    if len(str) < 2 {
        return "", errors.New("invalid number")
    }

    // Map of country codes and their lengths
    countryCodes := map[string]int{
        "91": 2,  // India
        "254": 3, // Kenya
        // Add more country codes as needed
    }

    // Try to match the beginning of the string with known country codes
    for code, length := range countryCodes {
        if strings.HasPrefix(str, code) {
            return str[length:], nil
        }
    }

    // If no matching country code is found, return error
    return "", errors.New("unknown country code")
}
func (s *webhook) GetLastMessage(data WebhookRequest) (MessageFromWhatsapp, error) {

	var emptyMessage MessageFromWhatsapp
	if len(data.Entry) == 0 {
		return emptyMessage, errors.New("data.Entry is nil")
	}

	if len(data.Entry[0].Changes) == 0 {
		return emptyMessage, errors.New("data.Entry[0].Changes is nil")
	}

	if len(data.Entry[0].Changes[0].Value.Messages) == 0 {
		return emptyMessage, errors.New("data.Entry[0].Changes[0].Value.Messages is nil")
	}

	return data.Entry[0].Changes[0].Value.Messages[len(data.Entry[0].Changes[0].Value.Messages)-1], nil
}

func (s *webhook) GetContactName(data WebhookRequest) string {

	if len(data.Entry[0].Changes[0].Value.Contacts) == 0 {
		return ""
	}
	return data.Entry[0].Changes[0].Value.Contacts[0].Profile.Name
}

func (s *webhook) FormatMessageHistoryFromUser(message MessageFromWhatsapp) whatsapp.WhatsappMessage {

	var formattedMessage whatsapp.WhatsappMessage
	formattedMessage.MessageId = message.Id
	boolVar := true
	formattedMessage.FromUser = &boolVar
	formattedMessage.Message, _ = json.Marshal(message)

	return formattedMessage
}

func (s *webhook) SendToWhatsapp(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact, responses []interface{}) error {
fmt.Println("Checkpoint : SendToWhatsapp")
	convertedImages := []string{}
	request := helpers.Request{
		Method: "POST",
		Scheme: "https",
		Path:   "/v18.0/" + account.PhoneId + "/messages",
		Header: map[string]string{
			"Content-Type":  "application/json",
			"Authorization": "Bearer " + account.WhatsappToken,
		},
	}
	Host := ""
    if account.BusinessAccountName == "Spiro" || account.BusinessAccountName == "Spiro Franchise"{
        Host = "crmapi.com.bot"
        request.Path = "/api/meta" + request.Path
    } else {
        Host = "graph.facebook.com"
    }
	request.Host = Host

	for _, response := range responses {
		fmt.Println("Checkpoint : SendToWhatsapp Response")
		response, imagePath, _ := s.ConvertImageToJpeg(response)
		if imagePath != "" {
			convertedImages = append(convertedImages, imagePath)
		}

		var message map[string]interface{}
		helpers.JsonMarshaller(response, &message)
		fmt.Println("Whatsapp Message-------------",message)
		message["messaging_product"] = "whatsapp"
		message["recipient_type"] = "individual"
		message["to"] = contact.WhatsappNumber
		request.Body = message
		metaResponse, err := helpers.MakeRequest(request)
		if err != nil {
			return err
		}
		fmt.Println(`Meta Response`, metaResponse)
		err = s.UpdateChatHistoryFromBusiness(contact, message, metaResponse)
		if err != nil {
			return err
		}


		time.Sleep(1 * time.Second)
	}
	go s.DeleteConvertedImages(convertedImages)
	return nil
}

func (s *webhook) UpdateChatHistoryFromBusiness(contact whatsapp.WhatsappContact, message interface{}, metaResponseInterface interface{}) error {
    fmt.Println("Enterd UpdateChatHistoryFromBusiness")
	var formattedMessage whatsapp.WhatsappMessage
	boolVar := false
	formattedMessage.FromUser = &boolVar
	formattedMessage.WhatsappContactId = contact.ID
	formattedMessage.Message, _ = json.Marshal(message)

	type MetaSendMessageResponse struct {
		Messages []struct {
			Id string `json:"id"`
		} `json:"messages"`
	}
	var metaSendMessageResponse MetaSendMessageResponse
	helpers.JsonMarshaller(metaResponseInterface, &metaSendMessageResponse)
	fmt.Println("metaSendMessageResponse",metaSendMessageResponse)
	if len(metaSendMessageResponse.Messages) > 0 {
		formattedMessage.MessageId = metaSendMessageResponse.Messages[0].Id
	}
	formattedMessage.MessageAdditionalInfo, _ = json.Marshal(metaResponseInterface)
	
	err := s.messageRepository.Save(env.GlobalEnv["POSTGRES_CREDENTIAL"],&formattedMessage)
	
	if err != nil {
		return err
	}

	return nil
}

func (s *webhook) FormatMessageRasaToWhatsapp(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact, rasaResponses []RasaResponse) ([]interface{}, error) {

	var formattedResponses []interface{}

	for _, rasaResponse := range rasaResponses {

		// err := s.UpdateAssistantChatHistoryForSummary(account, contact, rasaResponse)
		// if err != nil {
		// 	return nil, err
		// }



		if rasaResponse.Flow != nil {
			formattedResponses = append(formattedResponses, rasaResponse.Flow)
			continue
		}

		if rasaResponse.Custom != nil {
			if rasaResponse.Custom.Text != "" {
				rasaResponse.Text = rasaResponse.Custom.Text
			}
			if rasaResponse.Custom.Image != "" {
				rasaResponse.Image = rasaResponse.Custom.Image
			}
			if rasaResponse.Custom.Buttons != nil {
				rasaResponse.Buttons = rasaResponse.Custom.Buttons
			}
		}

		if strings.Contains(rasaResponse.Text, "#address:") {
			formattedResponse := AddressFormat{
				Type: "interactive",
				Interactive: &AddressInteractive{
					Type: "address_message",
					Body: &InteractiveBody{
						Text: strings.ReplaceAll(rasaResponse.Text, "#address:", ""),
					},
					Action: &AddressAction{
						Name: "address_message",
						Parameters: AddressParameter{
							Country: "IN",
						},
					},
				},
			}
			formattedResponses = append(formattedResponses, formattedResponse)
			continue
		}

		if rasaResponse.Buttons != nil && len(rasaResponse.Buttons) > 0 {

			if rasaResponse.Text == "" {
				rasaResponse.Text = "ERROR: text field is required with buttons"
			}

			if len(rasaResponse.Buttons) > 3 {
				formattedResponse := ListFormat{
					Type: "interactive",
					Interactive: &ListInteractive{
						Type: "list",
						Body: &InteractiveBody{
							Text: rasaResponse.Text,
						},
						Action: &ListAction{
							Button: "Select Option",
							Sections: []ListSection{{
								Rows: []ListRow{},
							}},
						},
					},
				}

				if rasaResponse.Image != "" {
					formattedResponse.Interactive.Header = &InteractiveHeader{
						Image: &InteractiveLink{
							Link: rasaResponse.Image,
						},
						Type: "image",
					}
				}

				for _, button := range rasaResponse.Buttons {
					row := ListRow{
						ID:    button.Payload,
						Title: button.Title,
					}
					formattedResponse.Interactive.Action.Sections[0].Rows = append(formattedResponse.Interactive.Action.Sections[0].Rows, row)
				}
				formattedResponses = append(formattedResponses, formattedResponse)
				continue
			}
			formattedResponse := ButtonFormat{
				Type: "interactive",
				Interactive: &ButtonInteractive{
					Type: "button",
					Body: &InteractiveBody{
						Text: rasaResponse.Text,
					},
					Action: &ButtonAction{
						Buttons: []ButtonButtons{},
					},
				},
			}

			if rasaResponse.Image != "" {
				formattedResponse.Interactive.Header = &InteractiveHeader{
					Image: &InteractiveLink{
						Link: rasaResponse.Image,
					},
					Type: "image",
				}
			}

			for _, button := range rasaResponse.Buttons {
				replyButton := ButtonButtons{
					Type: "reply",
					Reply: &ButtonReply{
						ID:    button.Payload,
						Title: button.Title,
					},
				}
				formattedResponse.Interactive.Action.Buttons = append(formattedResponse.Interactive.Action.Buttons, replyButton)
			}
			formattedResponses = append(formattedResponses, formattedResponse)
			continue
		}

		if rasaResponse.Image != "" {
			formattedResponse := MediaFormat{
				Type: "image",
				Image: &MediaLink{
					Link:    rasaResponse.Image,
					Caption: rasaResponse.Text,
				},
			}
			formattedResponses = append(formattedResponses, formattedResponse)
			continue
		}

		if rasaResponse.Text != "" {
			formattedResponse := TextFormat{
				Type: "text",
				Text: &Text{
					PreviewURL: false,
					Body:       rasaResponse.Text,
				},
			}
			if strings.HasPrefix(formattedResponse.Text.Body, "http") {
				formattedResponse.Text.PreviewURL = true
			}
			formattedResponses = append(formattedResponses, formattedResponse)
			continue
		}

		if rasaResponse.Template != "" {
			formattedResponse := TemplateFormat{
				Type: "template",
				Template: &Template{
					Name: rasaResponse.Template,
					Language: &TemplateLanguage{
						Code:       "en_US",
					},
				},
			}
			formattedResponses = append(formattedResponses, formattedResponse)
			continue
		}
	}

	return formattedResponses, nil
}

func (s *webhook) ProcessMediaMessage(account whatsapp.WhatsappAccount, message MessageFromWhatsapp) (MessageFromWhatsapp, error) {

	var err error

	if message.Image != nil {
		fmt.Println("image id", message.Image.ID)
		message.Image.Base64Data, err = s.ProcessMedia(account, "image", message.Image.ID)
		if err != nil {
			return message, err
		}
	} 
	if message.Document != nil {
		fmt.Println("document id", message.Document.ID)
		message.Document.Base64Data, err = s.ProcessMedia(account, "document", message.Document.ID)
		if err != nil {
			return message, err
		}
	} 
	//  if message.Audio != nil {
	// 	message.Audio.Url, err = s.ProcessMedia(account, "audio", message.Audio.ID)
	// 	if err != nil {
	// 		return message, err
	// 	}
	// }

	return message, nil
}

func (s *webhook) ProcessMedia(account whatsapp.WhatsappAccount, mediaType string, mediaId string) (string, error) {

	fmt.Print("MEDIA_ID::", mediaId)
	url := fmt.Sprintf("https://crmapi.com.bot/api/meta/v18.0/%s?phone_number_id=%s", mediaId, account.PhoneId)
	fmt.Println("media url--------", url)
	client := &http.Client{}
	request, err := http.NewRequest("GET", url, nil)
	fmt.Println("media url request---------",request)
	fmt.Println("media errorr----",err)
	if err != nil {
		fmt.Println("Media url Error-----------")
		return "", err
	}
	request.Header.Add("Authorization", "Bearer "+account.WhatsappToken)
	response, err := client.Do(request)
	fmt.Println("media url response---------",response)
	if err != nil {
		return "", err
	}
	defer response.Body.Close()
	
	// var jsonResponse map[string]interface{}
	// err = json.NewDecoder(response.Body).Decode(&jsonResponse)
	// if err != nil {
	// 	return "", err
	// }
	// fmt.Println("jsonResponse--------",jsonResponse)
	// mediaUrl, _ := jsonResponse["url"].(string)
	// client = &http.Client{}
	// request, err = http.NewRequest("GET", mediaUrl, nil)
	// if err != nil {
	// 	return "", err
	// }
	// request.Header.Set("Authorization", "Bearer "+account.WhatsappToken)
	// response, err = client.Do(request)
	// if err != nil {
	// 	return "", err
	// }
	// defer response.Body.Close()
	// if response.StatusCode != http.StatusOK {
	// 	return "", err
	// }
	// var responseBody []byte
	
	// responseBody, err = io.ReadAll(response.Body)
	// if err != nil {
	// 	return "", err
	// }
	// base64Data := base64.StdEncoding.EncodeToString(responseBody)
	 // Read the response body
	 responseBody, err := io.ReadAll(response.Body)
	 if err != nil {
		 return "", err
	 }
 
	 // Convert to base64
	 base64Data := base64.StdEncoding.EncodeToString(responseBody)
	 fmt.Println("base64",base64Data)

	// jsonPayload := map[string]interface{}{
	// 	"data": fmt.Sprint(base64Data),
	// }

	// if mediaType == "image" {
	// 	jsonPayload["extension"] = ".png"
	// 	jsonPayload["filetype"] = "image"
	// } else if mediaType == "audio" {
	// 	jsonPayload["extension"] = ".obb"
	// 	jsonPayload["filetype"] = "audio"
	// }

	// // fmt.Print("BASE 64 is::", jsonPayload["data"])

	// response_data, err := helpers.MakeRequest(helpers.Request{
	// 	Method: "POST",
	// 	Scheme: "https",
	// 	Host:   "preprod.ondc.adya.ai",
	// 	Path:   "/api/v1/buyer/auth/file_upload",
	// 	Header: map[string]string{
	// 		"Content-Type": "application/json",
	// 	},
	// 	Body: jsonPayload,
	// })
	// if err != nil {
	// 	return "", err
	// }

	// if uploadedUrl, ok := response_data.(map[string]interface{})["data"].(map[string]interface{})["url"]; !ok || uploadedUrl == nil {
	// 	return "", errors.New("upload failed")
	// }
	// // if uploadedUrl := response_data.(map[string]interface{})["url"]; uploadedUrl == nil {
	// // 	return "", errors.New("upload failed")
	// // }

	return base64Data, nil

	// return response_data.(map[string]interface{})["url"].(string), nil
}

func (s *webhook) ConvertImageToJpeg(response interface{}) (interface{}, string, error) {

	path := ""

	switch whatsappFormat := response.(type) {

	case MediaFormat:
		if whatsappFormat.Type == "image" {

			path = "whatsapp/" + uuid.NewString() + ".jpg"

			// whatsappFormat.Image.Filename
			newUrl, err := helpers.UploadFileFromUrl(whatsappFormat.Image.Filename, path, ".jpg")
			if err != nil {
				fmt.Println("ERROR : Whatsapp.Webhook.ConvertImageToJpg")
				break
			}
			whatsappFormat.Image.Filename = newUrl
			return whatsappFormat, path, nil
		}

	case ListFormat:
		if whatsappFormat.Interactive.Header != nil && whatsappFormat.Interactive.Header.Type == "image" {

			path = "whatsapp/" + uuid.NewString() + ".jpg"

			// whatsappFormat.Interactive.Header.Image.Link
			newUrl, err := helpers.UploadFileFromUrl(whatsappFormat.Interactive.Header.Image.Link, path, ".jpg")
			if err != nil {
				fmt.Println("ERROR : Whatsapp.Webhook.ConvertImageToJpg")
				break
			}
			whatsappFormat.Interactive.Header.Image.Link = newUrl
			return whatsappFormat, path, nil
		}
	case ButtonFormat:
		if whatsappFormat.Interactive.Header != nil && whatsappFormat.Interactive.Header.Type == "image" {

			path = "whatsapp/" + uuid.NewString() + ".jpg"

			// whatsappFormat.Interactive.Header.Image.Link
			newUrl, err := helpers.UploadFileFromUrl(whatsappFormat.Interactive.Header.Image.Link, path, ".jpg")
			if err != nil {
				fmt.Println("ERROR : Whatsapp.Webhook.ConvertImageToJpg")
				break
			}
			whatsappFormat.Interactive.Header.Image.Link = newUrl
			return whatsappFormat, path, nil
		}
	}

	return response, path, nil
}

func (s *webhook) DeleteConvertedImages(images []string) {

	for _, image := range images {
		err := helpers.DeleteFileFromUrl(image)
		if err != nil {
			fmt.Println("ERROR : Whatsapp.Webhook.DeleteConvertedImages")
		}
	}
}




// func (s *webhook) UpdateContactUserId(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact) (whatsapp.WhatsappContact, error) {

// 	if contact.MiddlewareUserId != "" {
// 		return contact, nil
// 	}

// 	fmt.Println("account", account)
// 	fmt.Println("account.MiddlewareUrl", account.MiddlewareUrl)

// 	responseInterface, err := helpers.UrlRequest(
// 		"POST",
// 		account.MiddlewareUrl+"/login",
// 		map[string]interface{}{
// 			"phone_number": contact.WhatsappNumber,
// 		},
// 		map[string]string{"Authorization": "Bearer " + account.MiddlewareToken},
// 	)
// 	if err != nil {
// 		return contact, err
// 	}

// 	var middlewareResponse LoginResponse
// 	helpers.JsonMarshaller(responseInterface, &middlewareResponse)
// 	if !middlewareResponse.Success {
// 		return contact, errors.New("error from middleware")
// 	}

// 	contact.MiddlewareUserId = middlewareResponse.Data.UserId
// 	err = s.contactRepository.Update(map[string]interface{}{"id": fmt.Sprint(contact.ID)}, &contact)
// 	if err != nil {
// 		return contact, err
// 	}

// 	return contact, nil
// }


func (s *webhook) UpsertWhatsappFlows(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact, rasaResponses []RasaResponse) ([]RasaResponse, error) {

	for index := range rasaResponses {
		if rasaResponses[index].Flow == nil {
			continue
		}
		rasaResponses[index].Flow.Interactive.Action.Parameters.FlowToken = rasaResponses[index].Flow.Interactive.Action.Parameters.FlowToken + ":" + uuid.NewString()

		flow := whatsapp.WhatsappFlow{
			Model: whatsapp.Model{
				CreatedByID: account.CreatedByID,
				CompanyId:   account.CompanyId,
			},
			WhatsappAccountRelation: whatsapp.WhatsappAccountRelation{
				WhatsappAccountId: account.ID,
			},
			Token:    rasaResponses[index].Flow.Interactive.Action.Parameters.FlowToken,
			Message:  []byte{},
			Screen:   "",
			Status:   "",
			Feedback: []byte{},
		}

		flow.Message, _ = json.Marshal(rasaResponses[index])

		err := s.flowRepository.Save(env.GlobalEnv["POSTGRES_CREDENTIAL"],&flow)
	
		if err != nil {
			return nil, err
		}
	}

	return rasaResponses, nil
}



func (s *webhook) UpdateUserChatHistoryForSummary(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact, message MessageFromWhatsapp) error {

	messageString := ""

	if message.Text != nil {
		messageString = message.Text.Body
	} else if message.Interactive != nil {
		if message.Interactive.ButtonReply != nil {
			messageString = message.Interactive.ButtonReply.Title
		} else if message.Interactive.ListReply != nil {
			messageString = message.Interactive.ListReply.Title
		}
	}

	// helpers.PrettyPrint("UpdateUserChatHistoryForSummary", messageString)
	// contactCollectionName := shared.PostgresCollectionNames["WHATSAPP_CONTACT"]
	if messageString != "" {

		query := map[string]interface{}{
			"id": contact.ID,
		}

		contact, _ = s.contact_Repository.FindOne(env.GlobalEnv["POSTGRES_CREDENTIAL"],query)
	

		if contact.ChatHistoryForSummary == "cleared" {
			contact.ChatHistoryForSummary = ""
		}

		contact.ChatHistoryForSummary += fmt.Sprintf("user: %v, ", messageString)

		// helpers.PrettyPrint("UpdateUserChatHistoryForSummary", contact.ChatHistoryForSummary)

		err := s.contact_Repository.Update(query, &contact)
		
		if err != nil {
			return err
		}
		fmt.Println("UpdateUserChatHistoryForSummary")
	}

	return nil
}

// func (s *webhook) UpdateAssistantChatHistoryForSummary(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact, response RasaResponse) error {

// 	messageString := ""

// 	if response.Flow != nil && response.Flow.Interactive != nil && response.Flow.Interactive.Body != nil {
// 		messageString = response.Flow.Interactive.Body.Text
// 	} else if response.Text != "" {
// 		messageString = response.Text
// 	}

// 	if messageString != "" {

// 		query := map[string]interface{}{
// 			"id": contact.ID,
// 		}

// 		contact, _ = s.contactRepository.FindOne(query)

// 		if contact.ChatHistoryForSummary == "cleared" {
// 			contact.ChatHistoryForSummary = ""
// 		}
// 		contact.ChatHistoryForSummary += fmt.Sprintf("assistant: %v, ", messageString)

// 		err := s.contactRepository.Update(query, &contact)
// 		if err != nil {
// 			return err
// 		}
// 	}

// 	if response.Flow != nil {

// 		payload := openai_chat.SummarizationChainRequest{
// 			ConversationId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
// 			Message:        contact.ChatHistoryForSummary,
// 		}

// 		helpers.PrettyPrint("UpdateAssistantChatHistoryForSummary Payload : ", payload)

// 		llmResponse, err := s.openaiChatService.SummarizationChain(payload)

// 		helpers.PrettyPrint("UpdateAssistantChatHistoryForSummary Response : ", llmResponse)

// 		whatsappLlmResponse := whatsapp.WhatsappLlmResponse{
// 			Model: whatsapp.Model{
// 				CreatedByID: account.CreatedByID,
// 				CompanyId:   account.CompanyId,
// 			},
// 			WhatsappAccountRelation: whatsapp.WhatsappAccountRelation{
// 				WhatsappAccountId: account.ID,
// 			},
// 			LlmUrl:      "openaiChatService.SummarizationChain",
// 			RecipientId: payload.ConversationId,
// 			Message:     payload.Message,
// 		}
// 		messageByte, _ := json.Marshal(payload)
// 		whatsappLlmResponse.Message = string(messageByte)
// 		whatsappLlmResponse.Response, _ = json.Marshal(llmResponse)
// 		s.llmResponseRepository.Save(&whatsappLlmResponse)

// 		if err != nil {
// 			fmt.Println("UpdateAssistantChatHistoryForSummary Error : ", err.Error())
// 		}

// 		summary := whatsapp.WhatsappSummary{
// 			WhatsappContactId: contact.ID,
// 			Summary:           llmResponse.Response,
// 		}

// 		s.contactRepository.SaveWhatsappSummary(&summary)

// 		query := map[string]interface{}{
// 			"id": contact.ID,
// 		}

// 		contact.ChatHistoryForSummary = "cleared"
// 		s.contactRepository.Update(query, &contact)
// 	}

// 	return nil
// }

func (s *webhook) MessageResponseProcessing(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact, message MessageFromWhatsapp) ([]RasaResponse, error) {
    fmt.Println("MessageResponseProcessing")
    Url := "http://localhost:5000/api/pipeline/continue"

	 referenceId := helpers.GenerateRandomUpperCaseString(12)
	fmt.Println("referenceId------------------",referenceId)

    payload := map[string]interface{}{
        "account":  account,
        "contact":  contact,
        "messages": message,
		"reference_id":referenceId,
    }

    helpers.PrettyPrint("MessageResponseProcessingPayload---------->", payload)

    bodyInterface, err := helpers.UrlRequest("POST", Url, payload)
    if err != nil {
        return nil, err
    }

    // Convert bodyInterface to map to access fields
    response, ok := bodyInterface.(map[string]interface{})
    if !ok {
        return nil, errors.New("invalid response format")
    }
	// helpers.PrettyPrint("ai response----------------",response)

    success, _ := response["success"].(bool)
    message_text, _ := response["message"].(string)
	message_type, _ := response["message_type"].(string)
	flow_type, _ := response["flow_type"].(string)
	data_sharing_consent,_ := response["data_sharing_consent"].(bool)
	userReferenceId,_ := response["reference_id"].(string)

	fmt.Println("flow_type-------",flow_type)
	fmt.Println("message_type-------------",message_type)
	fmt.Println("data_sharing_consent-------------",data_sharing_consent)
	fmt.Println("userReferenceId-------------",userReferenceId)
	

	if(success){
		if(message_type == "text"){
			if(!data_sharing_consent){
				fmt.Println("User not giving the permission--------")
				userStatusId, _ := helpers.GetLookupcodeId("USER_STATUS", "REJECTED",env.GlobalEnv["POSTGRES_CREDENTIAL"])
				query := map[string]interface{}{
					"id": contact.ID,
				}
				contact.StatusId = &userStatusId  // Convert uint to *uint by taking its address
				err := s.contact_Repository.Update(query, &contact)
				if err != nil {
					return nil, err
				}
			}
			if(referenceId!=""){
				fmt.Println("Updating userReferenceId--------")
				query := map[string]interface{}{
					"id": contact.ID,
				}
				contact.UserReferenceId = userReferenceId
				err := s.contact_Repository.Update(query, &contact)
				if err != nil {
					return nil, err
				}
			}
			return []RasaResponse{{
				RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
				Text:       message_text,
			}}, nil
		}
		if(message_type == "flow"){
			if(flow_type=="EV_MODELS"){
				return s.userFlowService.EV_MODELS(account, contact)
			}
		}


	}
    if !success {
        return []RasaResponse{{
            RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
            Text:       "No response from LLM",
        }}, nil
    }

    return []RasaResponse{{
        RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
        Text:       message_text,
    }}, nil
}

func (s *webhook) ImageResponseProcessing(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact, message MessageFromWhatsapp) ([]RasaResponse, error) {
	fmt.Println("ImageResponseProcessing")

		// query:= map[string]interface{}{
		// 	"id": contact.ID,
		// 	"whatsapp_number": contact.WhatsappNumber,	
		// 	"whatsapp_account_id": account.ID,
		// }
		// contact,err:=s.contact_Repository.FindOne(env.GlobalEnv["POSTGRES_CREDENTIAL"],query)
		// if err != nil {
		// 	return []RasaResponse{{
        //     	RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
        //     	Text:       "Contact not found",
        // 	}}, nil
		// }else{
		// 	contact.NationalId = message.Image.Base64Data
		// 	err := s.contact_Repository.Update(query, &contact)
		// 	if err != nil {
		// 	  	return []RasaResponse{{
        //    			 RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
        //    			 Text:       "Failed to Update",
        // 		}}, nil
		// 	}
		// }
	err1 := s.sellerFlowsService.UserImageData(account, contact,message.Image.Base64Data)
	if err1 != nil {

		return nil, err1
	}

	Url := "http://localhost:5000/api/image_upload"

    payload := map[string]interface{}{
		"image_data": message.Image.Base64Data, 
		"phone_number": message.From,
	  }

	
    // helpers.PrettyPrint("MessageResponseProcessingPayload---------->", payload)

    bodyInterface, err := helpers.UrlRequest("POST", Url, payload)
    if err != nil {
        return nil, err
    }

    // Convert bodyInterface to map to access fields
    response, ok := bodyInterface.(map[string]interface{})
    if !ok {
        return nil, errors.New("invalid response format")
    }
	// helpers.PrettyPrint("image response-------",response)

    success, _ := response["success"].(bool)
	is_image_verified,_:=response["is_image_verified"].(bool)
	extracted_info,_:=response["extracted_info"].(map[string]interface{})
	fmt.Println("extracted info----------",extracted_info)
	fmt.Println("is_image_verified----------",is_image_verified)
	if(success){
		var responses []RasaResponse	
		if(is_image_verified){
			responses = append(responses, RasaResponse{
				RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
				Text:       "Image uploaded successfully",
			})

			query:= map[string]interface{}{
				"id": contact.ID,
				"whatsapp_number": contact.WhatsappNumber,	
				"whatsapp_account_id": account.ID,
			}
			contact,err:=s.contact_Repository.FindOne(env.GlobalEnv["POSTGRES_CREDENTIAL"],query)
			if err != nil {
				responses = append(responses, RasaResponse{
					RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
					Text:       "Failed to send the Onboarding Form while fetching the contact",	
				})
				return responses, err
			}else{

			   notesJSON, err1 := json.Marshal(extracted_info)
			   if err1 != nil {
				   responses = append(responses, RasaResponse{
					   RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
					   Text:       "Failed to process extracted information",    
				   })
				   return responses, err1
			   }
			   contact.Notes = notesJSON
               contact.NationalId = message.Image.Base64Data
				err := s.contact_Repository.Update(query, &contact)
				if err != nil {
					responses = append(responses, RasaResponse{
					   RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
					   Text:       "Failed to send the Onboarding Form while updating the contact",	
				   })
				   return responses, err
			   }
				onboarding,err:=s.userFlowService.OnboardingFlow(account, contact)
			   	if err != nil {
						responses = append(responses, RasaResponse{
						   RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
						   Text:       "Failed to send the Onboarding Form",	
					   })
					   return responses, err
			   	}
			   responses = append(responses, onboarding...)
			}
			
			return responses, nil
		
		}else{
			responses = append(responses, RasaResponse{
				RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
				Text:       "Image is not verified.Please upload a valid image",
			},)
		    return responses,nil
		}
	}else{
		return []RasaResponse{{
            RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
            Text:       "Please Upload a Valid Image",
        }}, nil
	}

}

func (s *webhook) IntialMessage(data MessageRequest) error {
	accountPhoneNumber := data.WhatsappAccountNumber
	accountPhoneNumber, err := s.RemoveCountryCode(accountPhoneNumber)
	if err != nil {
		return err
	}

	account, err := s.accountRepository.FindOne(env.GlobalEnv["POSTGRES_CREDENTIAL"], map[string]interface{}{
		"whatsapp_number": accountPhoneNumber,
	})
	if err != nil {
		return err
	}

	err = s.IntialMessageContactCreation(data, account)
	if err != nil {
		return err
	}

	// userPhoneNumber, err := s.RemoveCountryCode(data.ContactNumber)
	// if err != nil {
	// 	return err
	// }
	userPhoneNumber := data.ContactNumber

	contact, err := s.contact_Repository.FindOne(env.GlobalEnv["POSTGRES_CREDENTIAL"],map[string]interface{}{
		"whatsapp_number":     userPhoneNumber,
		"whatsapp_account_id": account.ID,
	})


	if err != nil {
		return err
	}


	fmt.Println("Checkpoint : Contact", contact.WhatsappNumber)


	var responses []RasaResponse

	// responses, err = s.IntialTemplateResponseProcessing(account, contact)
	// responses, err = s.userFlowService.EV_MODELS(account, contact)
	responses, err = s.userFlowService.OnboardingFlow(account, contact)
	// responses,err = s.IntialMessageResponseProcessing(account, contact)
	if err != nil {
		return err
	}

	fmt.Println("Checkpoint : MessageResponseProcessing", responses)

	responses, err = s.UpsertWhatsappFlows(account, contact, responses)
	if err != nil {
		return err
	}

	formattedMessages, err := s.FormatMessageRasaToWhatsapp(account, contact, responses)
	if err != nil {
		return err
	}
	fmt.Println("Checkpoint : WhatsappFormat")

	err = s.SendToWhatsapp(account, contact, formattedMessages)
	if err != nil {
		return err
	}

	return nil
}


func (s *webhook) IntialTemplateResponseProcessing(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact) ([]RasaResponse, error) {
    fmt.Println("Intial Template MessageResponseProcessing")
    return []RasaResponse{{
        RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
        Template:       "hello_world",
    }}, nil
}


func (s *webhook) IntialMessageResponseProcessing(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact) ([]RasaResponse, error) {
    fmt.Println("Intial MessageResponseProcessing")
    Url := "http://localhost:5000/api/pipeline/start"

    payload := map[string]interface{}{}

    // helpers.PrettyPrint("MessageResponseProcessingPayload---------->", payload)

    bodyInterface, err := helpers.UrlRequest("POST", Url, payload)
    if err != nil {
        return nil, err
    }

    // Convert bodyInterface to map to access fields
    response, ok := bodyInterface.(map[string]interface{})
    if !ok {
        return nil, errors.New("invalid response format")
    }

    success, _ := response["success"].(bool)
    message_text, _ := response["message"].(string)
	

    if !success {
        return []RasaResponse{{
            RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
            Text:       "No response from LLM",
        }}, nil
    }

    return []RasaResponse{{
        RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
        Text:       message_text,
    }}, nil
}


func (s *webhook) IntialMessageContactCreation(data MessageRequest, account whatsapp.WhatsappAccount) error {
    fmt.Println("Checkpoint : IntialMessageContactCreation")
	// userPhoneNumber, err := s.RemoveCountryCode(data.ContactNumber)
	// if err != nil {
	// 	return err
	// }
	userPhoneNumber := data.ContactNumber

	contact, err := s.contact_Repository.FindOne(env.GlobalEnv["POSTGRES_CREDENTIAL"],map[string]interface{}{
		"whatsapp_number":     userPhoneNumber,
		"whatsapp_account_id": account.ID,
	})

	fmt.Println("Checkpoint : Contact", contact)

	if err != nil {
		fmt.Println("Checkpoint : FindOne Error", err)
		contact = whatsapp.WhatsappContact{
			WhatsappNumber: userPhoneNumber,
			// Name:           s.GetContactName(data),
		}
		contact.CompanyId = account.CompanyId
		contact.WhatsappAccountId = account.ID

		err = s.contact_Repository.Save(env.GlobalEnv["POSTGRES_CREDENTIAL"],&contact)
		
		if err != nil {
			return err
		}
	}
	return nil
}

func (s *webhook) UserOnboardingProcessing(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact, message MessageFromWhatsapp) ([]RasaResponse, error) {
	fmt.Println("UserOnboardingProcessing--------------------")
    _, filename, _, _ := runtime.Caller(0)
    currentDir := filepath.Dir(filename)
    userDataFile := filepath.Join(currentDir, "users", "data", fmt.Sprintf("user_a%d_c%d.go", account.ID, contact.ID))

    // Read and parse the user data file
    userDataBytes, err := os.ReadFile(userDataFile)
    if err != nil {
        return nil, fmt.Errorf("failed to read user data file: %v", err)
    }

    // Extract the UserData map from the file content
	content := string(userDataBytes)
    userDataKey := fmt.Sprintf("var UserData_a%d_c%d = map[string]interface{}{", account.ID, contact.ID)
    startIdx := strings.Index(content, userDataKey)
    if startIdx == -1 {
        return nil, fmt.Errorf("user data not found in file")
    }

   // Find the closing brace of the map
   startIdx = startIdx + len(userDataKey)
   endIdx := strings.Index(content[startIdx:], "}")
   if endIdx == -1 {
	   return nil, fmt.Errorf("invalid user data format")
   }
   
   // Extract the map content and parse it
   mapContent := content[startIdx : startIdx+endIdx]
   userData := make(map[string]interface{})
   
   // Parse each line of the map
   lines := strings.Split(mapContent, "\n")
   for _, line := range lines {
	   line = strings.TrimSpace(line)
	   if line == "" || line == "}" {
		   continue
	   }
	   
	   // Split by colon and remove quotes and comma
	   parts := strings.Split(line, ":")
	   if len(parts) != 2 {
		   continue
	   }
	   
	   key := strings.Trim(strings.TrimSpace(parts[0]), `"`)
	   value := strings.Trim(strings.TrimSpace(parts[1]), `", `)
	   
	   userData[key] = value
   }



	Url := "http://localhost:5000/api/sessions/update-states"

	payload := map[string]interface{}{
		"updated_states":   map[string]interface{}{
			"name":      userData["First_Name"],
			"country":   userData["Country"],
			"state":     userData["State"],
			"city":      userData["City"],
			"zip_code":  userData["Zip_Code"],
			"id_number": userData["National_ID"],
        	"dob":       userData["Birth_Date"],
        	"gender":    userData["Gender"],
        	"address":   userData["Building"],
        	"phone":     userData["Mobile"],
			"sacco_name": userData["Sacco_Name"],
		},
		"phone_number": message.From,
	}

	// helpers.PrettyPrint("MessageResponseProcessingPayload---------->", payload)

	bodyInterface, err := helpers.UrlRequest("POST", Url, payload)
	if err != nil {
		return nil, fmt.Errorf("failed to send request to LLM server")
	}
	response, ok := bodyInterface.(map[string]interface{})
	if !ok {
		return nil, errors.New("invalid response format")
	}
	// helpers.PrettyPrint("image response-------", response)

	success, _ := response["success"].(bool)
	var responses []RasaResponse
	if(success){
		onboardingMessage := MessageFromWhatsapp{
			From: message.From,
			Type: "text",
			Text: &MessageFromWhatsappText{
				Body: "I have uploaded the National ID",
			},
		}
		responses,err= s.MessageResponseProcessing(account, contact, onboardingMessage)
		if( err != nil) {
			return nil, err
		}
	}else{
		responses = append(responses, RasaResponse{
			RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
			Text:        "Error response from LLM",
		})
		return responses, nil
	}
	return responses,nil

}


func (s *webhook) FranchiseMessageResponseProcessing(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact, message MessageFromWhatsapp) ([]RasaResponse, error) {
    fmt.Println("---------FranchiseMessageResponseProcessing------")
    Url := "http://localhost:5001/api/franchise/continue"


	payload := map[string]interface{}{
        "entry": []map[string]interface{}{
            {
                "changes": []map[string]interface{}{
                    {
                        "field": "messages",
                        "value": map[string]interface{}{
                            "contacts": []map[string]interface{}{
                                {
                                    "profile": map[string]interface{}{
                                        "name": "",
                                    },
                                    "wa_id": account.WabaId,
                                },
                            },
							"messages": []MessageFromWhatsapp{message},
                            "messaging_product": "whatsapp",
                            "metadata": map[string]interface{}{
                                "display_phone_number": "91"+account.WhatsappNumber,
                                "phone_number_id":     account.PhoneId,
                            },
                        },
                    },
                },
                "id": "",
            },
        },
        "object": "whatsapp_business_account",
    }

    helpers.PrettyPrint("FranchiseMessageResponseProcessingPayload---------->", payload)

    bodyInterface, err := helpers.UrlRequest("POST", Url, payload)
    if err != nil {
        return nil, err
    }

    // Convert bodyInterface to map to access fields
    response, ok := bodyInterface.(map[string]interface{})
    if !ok {
        return nil, errors.New("invalid response format")
    }
	// helpers.PrettyPrint("ai response----------------",response)

    success, _ := response["success"].(bool)
    message_text, _ := response["message"].(string)
	message_type, _ := response["message_type"].(string)
	
	if(success){
		if(message_type == "text"){
			return []RasaResponse{{
				RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
				Text:       message_text,
			}}, nil
		}
	}
    if !success {
        return []RasaResponse{{
            RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
            Text:       "Failed to send the Response",
        }}, nil
    }

    return []RasaResponse{{
        RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
        Text:       message_text,
    }}, nil
}


func (s *webhook) CustomerSupportMessageResponseProcessing(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact, message MessageFromWhatsapp) ([]RasaResponse, error) {
    fmt.Println("---------CustomerSupportMessageResponseProcessing------")
    Url := "http://localhost:5001/api/customer_support/continue"


	payload := map[string]interface{}{
        "account":  account,
        "contact":  contact,
        "messages": message,
    }

    helpers.PrettyPrint("CustomerSupportMessageResponseProcessingPayload---------->", payload)

    bodyInterface, err := helpers.UrlRequest("POST", Url, payload)
    if err != nil {
        return nil, err
    }

    // Convert bodyInterface to map to access fields
    response, ok := bodyInterface.(map[string]interface{})
    if !ok {
        return nil, errors.New("invalid response format")
    }
	// helpers.PrettyPrint("ai response----------------",response)

    success, _ := response["success"].(bool)
    message_text, _ := response["message"].(string)
	message_type, _ := response["message_type"].(string)
	
	if(success){
		if(message_type == "text"){
			return []RasaResponse{{
				RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
				Text:       message_text,
			}}, nil
		}
	}
    if !success {
        return []RasaResponse{{
            RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
            Text:       "Failed to send the Response",
        }}, nil
    }

    return []RasaResponse{{
        RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
        Text:       message_text,
    }}, nil
}

func (s *webhook) FranchiseDocumentResponseProcessing(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact, message MessageFromWhatsapp) ([]RasaResponse, error) {
    fmt.Println("---------FranchiseDocumentResponseProcessing------")
    Url := "http://localhost:5000/api/franchise/document"

    payload := map[string]interface{}{
        "account":  account,
        "contact":  contact,
        "messages": message,
    }

    helpers.PrettyPrint("FranchiseDocumentResponseProcessingPayload---------->", payload)

    bodyInterface, err := helpers.UrlRequest("POST", Url, payload)
    if err != nil {
        return nil, err
    }

    // Convert bodyInterface to map to access fields
    response, ok := bodyInterface.(map[string]interface{})
    if !ok {
        return nil, errors.New("invalid response format")
    }
	// helpers.PrettyPrint("ai response----------------",response)

    success, _ := response["success"].(bool)
    message_text, _ := response["message"].(string)
	message_type, _ := response["message_type"].(string)
	
	if(success){
		if(message_type == "text"){
			return []RasaResponse{{
				RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
				Text:       message_text,
			}}, nil
		}
	}
    if !success {
        return []RasaResponse{{
            RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
            Text:       "Failed to send the Response",
        }}, nil
    }

    return []RasaResponse{{
        RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
        Text:       message_text,
    }}, nil
}
