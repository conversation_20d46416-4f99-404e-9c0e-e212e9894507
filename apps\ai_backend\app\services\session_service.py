import logging
import json
import datetime
from typing import Optional, Dict, Any, List, Tu<PERSON>
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
from app.models.persistent_session import Base, PersistentSession
from app.services.pipeline_service import SalePipeline
import os

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# Get database URL from environment or use SQLite as fallback
DATABASE_URL = os.environ.get('DATABASE_URL', 'sqlite:///persistent_sessions.db')

# Create SQLAlchemy engine and session
engine = create_engine(DATABASE_URL)
Base.metadata.create_all(engine)  # Create tables if they don't exist
db_session = scoped_session(sessionmaker(autocommit=False, autoflush=False, bind=engine))

class SessionManager:
    """Manages persistent sessions for WhatsApp conversations"""
    
    @staticmethod
    def get_session_by_phone(phone_number: str) -> Optional[Tuple[PersistentSession, SalePipeline]]:
        """
        Retrieve a session by phone number
        
        Args:
            phone_number: The WhatsApp phone number
            
        Returns:
            Tuple of (session object, pipeline instance) or None if not found
        """
        try:
            session = db_session.query(PersistentSession).filter_by(
                phone_number=phone_number,
                is_active=True
            ).first()
            
            if not session:
                logger.info(f"No active session found for phone {phone_number}")
                return None
                
            # Update last active timestamp
            session.last_active = datetime.datetime.utcnow()
            db_session.commit()
            
            # Deserialize the pipeline
            pipeline = session.deserialize_pipeline(session.session_data)
            
            if not pipeline:
                logger.warning(f"Found session for {phone_number} but pipeline is corrupted, creating new")
                return None
                
            # Ensure conversation history is properly loaded
            if hasattr(pipeline, 'context'):
                # Check if we have valid conversation history in the database
                history_from_db = []
                try:
                    if session.conversation_history:
                        history_from_db = json.loads(session.conversation_history)
                        logger.info(f"Loaded {len(history_from_db)} messages from database for {phone_number}")
                except (json.JSONDecodeError, TypeError) as e:
                    logger.error(f"Error parsing conversation history from database: {str(e)}")
                
                # If pipeline doesn't have conversation_history attribute, create it
                if not hasattr(pipeline.context, 'conversation_history'):
                    pipeline.context.conversation_history = []
                    
                # If database has history but pipeline doesn't, use database history
                if history_from_db and not pipeline.context.conversation_history:
                    logger.info(f"Using {len(history_from_db)} messages from database instead of empty pipeline history")
                    pipeline.context.conversation_history = history_from_db
                
                # Log the conversation history size
                logger.info(f"Session for {phone_number} has {len(pipeline.context.conversation_history)} messages in history")
            
            return session, pipeline
            
        except Exception as e:
            logger.error(f"Error retrieving session for {phone_number}: {str(e)}")
            db_session.rollback()
            return None
    
    @staticmethod
    def create_or_update_session(phone_number: str, pipeline: SalePipeline) -> bool:
        """
        Create a new session or update existing one
        
        Args:
            phone_number: The WhatsApp phone number
            pipeline: The SalePipeline instance to save
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # First ensure we have valid conversation history
            conversation_history = "[]"
            if hasattr(pipeline, 'context') and hasattr(pipeline.context, 'conversation_history'):
                try:
                    # Ensure we have valid JSON data
                    conversation_history = json.dumps(pipeline.context.conversation_history) 
                    logger.info(f"Saving conversation history with {len(pipeline.context.conversation_history)} messages for {phone_number}")
                except Exception as e:
                    logger.error(f"Error serializing conversation history: {str(e)}")
                    conversation_history = "[]"
            
            # Check if session exists
            session = db_session.query(PersistentSession).filter_by(
                phone_number=phone_number
            ).first()
            
            if not session:
                # Create new session
                session = PersistentSession(
                    phone_number=phone_number,
                    session_data=PersistentSession.serialize_pipeline(None, pipeline),
                    conversation_history=conversation_history,
                    is_active=True
                )
                db_session.add(session)
                logger.info(f"Created new session for phone {phone_number}")
            else:
                # Update existing session
                session.session_data = session.serialize_pipeline(pipeline)
                session.conversation_history = conversation_history
                session.last_active = datetime.datetime.utcnow()
                session.is_active = True
                logger.info(f"Updated session for phone {phone_number}")
                
            db_session.commit()
            return True
            
        except Exception as e:
            logger.error(f"Error saving session for {phone_number}: {str(e)}")
            db_session.rollback()
            return False
    
    @staticmethod
    def clear_session(phone_number: str) -> bool:
        """
        Clear a session for a phone number
        
        Args:
            phone_number: The WhatsApp phone number
            
        Returns:
            True if successful, False otherwise
        """
        try:
            session = db_session.query(PersistentSession).filter_by(
                phone_number=phone_number
            ).first()
            
            if session:
                # Mark as inactive
                session.is_active = False
                
                # Also mark any lead qualification records as inactive
                try:
                    # Import here to avoid circular imports
                    from app.models.lead_qualification import LeadQualificationResult
                    
                    # Find and update any associated lead qualification records
                    lead_records = db_session.query(LeadQualificationResult).filter_by(
                        phone_number=phone_number
                    ).all()
                    
                    for record in lead_records:
                        record.is_active = False
                        logger.info(f"Marked lead qualification record for {phone_number} as inactive")
                except Exception as lead_err:
                    logger.error(f"Error clearing lead qualification records: {str(lead_err)}")
                    # Continue even if there's an issue with lead qualification records
                
                db_session.commit()
                logger.info(f"Cleared session for phone {phone_number}")
                return True
            else:
                logger.warning(f"No session found to clear for phone {phone_number}")
                return False
                
        except Exception as e:
            logger.error(f"Error clearing session for {phone_number}: {str(e)}")
            db_session.rollback()
            return False
    
    @staticmethod
    def list_active_sessions() -> List[Dict[str, Any]]:
        """
        List all active sessions
        
        Returns:
            List of session dictionaries
        """
        try:
            sessions = db_session.query(PersistentSession).filter_by(
                is_active=True
            ).all()
            
            return [session.to_dict() for session in sessions]
            
        except Exception as e:
            logger.error(f"Error listing active sessions: {str(e)}")
            return []
    
    @staticmethod
    def cleanup_expired_sessions(days: int = 30) -> int:
        """
        Clean up sessions inactive for more than the specified days
        
        Args:
            days: Number of days of inactivity before cleanup
            
        Returns:
            Number of sessions cleaned up
        """
        try:
            cutoff = datetime.datetime.utcnow() - datetime.timedelta(days=days)
            expired = db_session.query(PersistentSession).filter(
                PersistentSession.last_active < cutoff,
                PersistentSession.is_active == True
            ).all()
            
            count = 0
            for session in expired:
                session.is_active = False
                count += 1
                
            db_session.commit()
            
            if count > 0:
                logger.info(f"Cleaned up {count} expired sessions")
                
            return count
            
        except Exception as e:
            logger.error(f"Error cleaning up expired sessions: {str(e)}")
            db_session.rollback()
            return 0 