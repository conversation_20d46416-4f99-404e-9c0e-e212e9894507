package users

import (
	"fmt"
	shared "libs/shared"
	env "apps/spiro/config"
)

type Service interface {
	CreateSample(data SampleCreatePayloadDto, metaData shared.ApiMetaData) (interface{}, error)
	UpdateSample(data SampleUpdatePayloadDto, metaData shared.ApiMetaData, query map[string]interface{}) (interface{}, error)
	GetSample(metaData shared.ApiMetaData, query map[string]interface{}) (interface{}, error)
	ListSample(metaData shared.ApiMetaData, query map[string]interface{}, sortings interface{}, perPage int, pageNo int) (map[string]interface{}, error)
	DeleteSample(metaData shared.ApiMetaData, query map[string]interface{}) (interface{}, error)
}

type service struct {
	db shared.PostgresRepositoryFunctions
}

var newServiceObj *service //singleton object

// singleton function
func NewService() *service {
	if newServiceObj != nil {
		return newServiceObj
	}
	new_db := shared.PostgresRepository()
	newServiceObj = &service{new_db}
	return newServiceObj
}

// ------------------Product Brand--------------------------------------------------------------------------------------------------
func (s *service) CreateSample(data SampleCreatePayloadDto, metaData shared.ApiMetaData) (interface{}, error) {

	collectionName := shared.PostgresCollectionNames["LOOKUP_TYPE"]
	var create_payload = map[string]interface{}{}
	shared.JsonMarshaller(data, &create_payload)

	resp, err := s.db.CreateOne(env.GlobalEnv["POSTGRES_CREDENTIAL"], collectionName, create_payload)
	fmt.Println("resp", resp)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
func (s *service) UpdateSample(data SampleUpdatePayloadDto, metaData shared.ApiMetaData, query map[string]interface{}) (interface{}, error) {

	// shared.PrettyPrint("data", data)
	// shared.PrettyPrint("metaData", metaData)

	//Any other db functions or doing any functionality means do it here .....

	collectionName := shared.PostgresCollectionNames["LOOKUP_TYPE"]
	var update_payload = map[string]interface{}{}
	shared.JsonMarshaller(data, &update_payload)
	resp, err := s.db.UpdateOne(env.GlobalEnv["POSTGRES_CREDENTIAL"], collectionName, update_payload, query)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (s *service) GetSample(metaData shared.ApiMetaData, query map[string]interface{}) (interface{}, error) {

	// shared.PrettyPrint("data", data)
	// shared.PrettyPrint("metaData", metaData)

	//Any other db functions or doing any functionality means do it here .....

	collectionName := shared.PostgresCollectionNames["LOOKUP_TYPE"]

	resp, err := s.db.GetOne(env.GlobalEnv["POSTGRES_CREDENTIAL"], collectionName, query)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (s *service) ListSample(metaData shared.ApiMetaData, query map[string]interface{}, sortings interface{}, perPage int, pageNo int) (map[string]interface{}, error) {

	// shared.PrettyPrint("data", data)
	// shared.PrettyPrint("metaData", metaData)

	//Any other db functions or doing any functionality means do it here .....

	collectionName := shared.PostgresCollectionNames["LOOKUP_TYPE"]
	resp, err := s.db.GetManyWithPagination(env.GlobalEnv["POSTGRES_CREDENTIAL"], collectionName, query, sortings, perPage, pageNo)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (s *service) DeleteSample(metaData shared.ApiMetaData, query map[string]interface{}) (interface{}, error) {

	// shared.PrettyPrint("data", data)
	// shared.PrettyPrint("metaData", metaData)

	//Any other db functions or doing any functionality means do it here .....

	collectionName := shared.PostgresCollectionNames["LOOKUP_TYPE"]
	resp, err := s.db.DeleteOne(env.GlobalEnv["POSTGRES_CREDENTIAL"], collectionName, query)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
