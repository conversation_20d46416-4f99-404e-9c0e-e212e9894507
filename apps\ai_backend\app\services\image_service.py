from typing import Dict, List, Any, Optional
import json
import logging
import openai
import time
import os
import random
import re
import base64

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# Image Analysis client for Azure OpenAI Vision - using environment variables
vision_client = openai.AzureOpenAI(
    api_key="********************************",
    api_version="2024-02-01",
    azure_endpoint="https://adya-openai-aus.openai.azure.com/"
)

# Vision model name
vision_model = os.getenv("AZURE_VISION_MODEL", "gpt4vision_deployed")

def format_user_context_for_prompt(user_context) -> str:
    """Create a formatted string of user context information for the image analysis prompt"""
    if not user_context:
        return ""
    
    user_info = "## USER STATE INFORMATION:\n"
    
    # Personal information section
    user_info += "### Personal Information:\n"
    if user_context.first_name or user_context.last_name:
        name = f"{user_context.first_name or ''} {user_context.last_name or ''}".strip()
        user_info += f"- Name: {name}\n"
    if user_context.email:
        user_info += f"- Email: {user_context.email}\n"
    if user_context.phone:
        user_info += f"- Phone: {user_context.phone}\n"
    if user_context.gender:
        user_info += f"- Gender: {user_context.gender}\n"
    if user_context.birth_date:
        user_info += f"- Date of Birth: {user_context.birth_date}\n"
    
    # Location information
    if any([user_context.address, user_context.city, user_context.state, user_context.country]):
        user_info += "\n### Location Information:\n"
        if user_context.address:
            user_info += f"- Address: {user_context.address}\n"
        if user_context.building:
            user_info += f"- Building: {user_context.building}\n"
        if user_context.street:
            user_info += f"- Street: {user_context.street}\n"
        if user_context.city:
            user_info += f"- City: {user_context.city}\n"
        if user_context.state:
            user_info += f"- State/Province: {user_context.state}\n"
        if user_context.country:
            user_info += f"- Country: {user_context.country}\n"
        if user_context.zip_code:
            user_info += f"- Zip/Postal Code: {user_context.zip_code}\n"
    
    # ID and document information
    user_info += "\n### ID Document Information:\n"
    user_info += f"- ID Available: {user_context.identification_available}\n"
    if user_context.national_id:
        user_info += f"- National ID: {user_context.national_id}\n"
    if user_context.image_upload_attempts > 0:
        user_info += f"- Image Upload Attempts: {user_context.image_upload_attempts}\n"
    if user_context.missing_fields:
        user_info += f"- Missing Fields: {', '.join(user_context.missing_fields)}\n"
    
    # Purchase interest information
    user_info += "\n### Purchase Information:\n"
    if user_context.selected_model:
        user_info += f"- Selected Model: {user_context.selected_model}\n"
    if user_context.is_interested is not None:
        user_info += f"- Interested in Purchase: {'Yes' if user_context.is_interested else 'No'}\n"
    
    if hasattr(user_context, 'selected_showroom') and user_context.selected_showroom:
        user_info += f"- Selected Showroom: {user_context.selected_showroom}\n"
    
    return user_info

def analyze_image(image_data, user_query=None, user_context=None):
    """Analyze an uploaded image to extract user information
    
    Args:
        image_data: Base64 encoded image data or data URL (data:image/*)
        user_query: Optional query to guide the analysis
        user_context: Optional user context for enhanced prompting
        
    Returns:
        Dict with analysis results and extracted information
    """
    try:
        # Prepare the prompt for the LLM
        prompt = """
        FIRST, determine if this image contains a valid National ID document. Look for:
        1. Official government-issued ID format with security features
        2. Personal information section with name, date of birth, etc.
        3. ID number or registration number
        4. Official seals, watermarks, or holograms (if visible)
        5. Government authority or issuing agency information
        
        If the image is NOT a valid National ID document (e.g., it's a photo of a person, a scene, a different document type like a receipt or business card), your response MUST set "success" to false and return empty strings for all fields.
        
        ONLY IF the image contains a valid National ID document, extract the following information in JSON format:
        - name: Full name of the person
        - id_number: National ID number
        - country: Country of residence or issuing country
        - state: State/Province
        - city: City
        - zip_code: Postal/ZIP code
        - dob: Date of birth (formatted as YYYY-MM-DD if possible)
        - gender: Gender
        - address: Full address
        - phone: Phone number
        
        IMPORTANT INSTRUCTIONS:
        1. Your response MUST be in valid JSON format with these exact field names.
        2. If you cannot determine if the document is a National ID with high confidence, return "success": false.
        3. If a field is not visible, unclear or not present, leave it as an empty string.
        4. Extract information exactly as it appears on the document without making assumptions.
        5. For partial information, include what is visible rather than returning empty strings.
        6. If the document is in a non-Latin script, transliterate names and addresses if possible.
        
        Example response format:
        {
          "success": true/false,
          "name": "John Smith",
          "id_number": "12345678",
          "country": "United States",
          "state": "California",
          "city": "Los Angeles",
          "zip_code": "90001",
          "dob": "1990-01-01",
          "gender": "Male",
          "address": "123 Main St",
          "phone": "******-123-4567"
        }
        """
        
        if user_query:
            prompt += f"\n\nAdditional context: {user_query}"
            
        if user_context:
            # Add any relevant context from the user's conversation
            if hasattr(user_context, 'first_name') and user_context.first_name:
                prompt += f"\n\nNote: The user has previously identified as {user_context.first_name}"
            if hasattr(user_context, 'national_id') and user_context.national_id:
                prompt += f"\n\nNote: The user has previously provided ID number: {user_context.national_id}"
            if hasattr(user_context, 'country') and user_context.country:
                prompt += f"\n\nNote: The user has previously indicated country: {user_context.country}"
        
        # Call the Vision API to analyze the image
        vision_response = call_vision_api(image_data, prompt)
        logger.info(f"Vision API response: {vision_response}")
        
        # Extract information from the response
        extracted_info = {
            "name": vision_response.get("name", ""),
            "id_number": vision_response.get("id_number", ""),
            "country": vision_response.get("country", ""),
            "state": vision_response.get("state", ""),
            "city": vision_response.get("city", ""),
            "zip_code": vision_response.get("zip_code", ""),
            "dob": vision_response.get("dob", ""),
            "gender": vision_response.get("gender", ""),
            "address": vision_response.get("address", ""),
            "phone": vision_response.get("phone", "")
        }
        
        # Determine success based on the model's response
        # First check explicit success field, then fall back to checking extracted content
        is_valid_id = vision_response.get("success", None)
        
        # If the model didn't include a success field, fall back to checking if any info was extracted
        if is_valid_id is None:
            is_valid_id = any(value for value in extracted_info.values() if value)
        
        # Prepare appropriate message based on whether it's a valid ID
        if is_valid_id:
            success_message = "Successfully extracted information from the National ID document."
        else:
            success_message = "The uploaded image does not appear to be a valid National ID document. Please upload a clear image of your National ID card or document."
        
        # Prepare the response
        return {
            "success": is_valid_id,
            "message": success_message,
            "extracted_info": extracted_info,
            "is_valid_national_id": is_valid_id
        }
            
    except Exception as e:
        logger.error(f"Error in image analysis: {str(e)}", exc_info=True)
        return {
            "success": False,
            "message": "Unable to process the image. Please try again with a clearer image of your National ID.",
            "extracted_info": {
                "name": "",
                "id_number": "",
                "country": "",
                "state": "",
                "city": "",
                "zip_code": "",
                "dob": "",
                "gender": "",
                "address": "",
                "phone": ""
            },
            "is_valid_national_id": False
        }

def handle_image_upload(image_data: str, pipeline_instance, query=None):
    """Handles image upload and processing independent of the conversation flow
    
    Args:
        image_data: Base64 encoded image data or data URL (data:image/*)
        pipeline_instance: Instance of SalePipeline to update the context
        query: Optional query to guide the image analysis
        
    Returns:
        Dict with analysis results and processing status
    """
    try:
        # Set flags to indicate image was uploaded, regardless of processing outcome
        pipeline_instance.context.image_uploaded = True
        pipeline_instance.context.id_upload_complete = True
        pipeline_instance.context.image_upload_attempts += 1
        
        # Log the state change for debugging
        logger.info("==== IMAGE UPLOAD FLAGS SET IN image_service.handle_image_upload ====")
        logger.info(f"image_uploaded flag set to: {pipeline_instance.context.image_uploaded}")
        logger.info(f"id_upload_complete flag set to: {pipeline_instance.context.id_upload_complete}")
        logger.info(f"image_upload_attempts: {pipeline_instance.context.image_upload_attempts}")
        
        # Analyze the uploaded image
        analysis_result = analyze_image(image_data, user_query=query, user_context=pipeline_instance.context)
        
        # Always ensure extracted_info exists in the result
        if "extracted_info" not in analysis_result:
            analysis_result["extracted_info"] = {}
            
        # Update context with extracted information
        if analysis_result.get("extracted_info"):
            pipeline_instance.context.update_from_image_data(analysis_result["extracted_info"])
            
            # Add a system message to conversation history about successful extraction
            if hasattr(pipeline_instance.context, 'add_message'):
                extracted_fields = [k for k, v in analysis_result["extracted_info"].items() if v]
                pipeline_instance.context.add_message(
                    "system", 
                    f"National ID document analyzed. Successfully extracted: {', '.join(extracted_fields) if extracted_fields else 'No fields'}"
                )
        else:
            # Add a system message about failed extraction if no data was extracted
            if hasattr(pipeline_instance.context, 'add_message'):
                pipeline_instance.context.add_message(
                    "system",
                    "National ID document analysis failed. No information could be extracted from the image."
                )
        
        # Let the LLM determine what fields are missing and how to proceed
        if pipeline_instance.context.current_node_id == "process_national_id":
            # Stay in the same node
            pipeline_instance.context.ready_to_transition = False
            
            # Add the extracted info to the result
            return {
                "success": True,
                "message": analysis_result.get("message", "I've processed your ID document."),
                "extracted_info": analysis_result.get("extracted_info", {}),
                "ui_action": "continue_flow",
                "continue_flow": True,
                "is_valid_national_id": analysis_result.get("is_valid_national_id", False)
            }
        else:
            # If we're not in the process_national_id node, return the analysis result directly
            return {
                "success": analysis_result.get("success", False),
                "message": analysis_result.get("message", "I've processed your ID document."),
                "extracted_info": analysis_result.get("extracted_info", {}),
                "ui_action": "continue_flow" if analysis_result.get("success", False) else "suggest_new_upload",
                "continue_flow": analysis_result.get("success", False),
                "is_valid_national_id": analysis_result.get("is_valid_national_id", False)
            }
        
    except Exception as e:
        logger.error(f"Error processing uploaded image: {str(e)}", exc_info=True)
        
        # Still mark as uploaded even if there was an error
        pipeline_instance.context.image_uploaded = True
        pipeline_instance.context.image_upload_attempts += 1
        
        # Add a system message about the error
        if hasattr(pipeline_instance.context, 'add_message'):
            pipeline_instance.context.add_message(
                "system",
                f"Error processing National ID document image: {str(e)}"
            )
        
        # Create fallback empty data
        fallback_data = {
            "name": "",
            "id_number": "",
            "country": "",
            "state": "",
            "city": "",
            "zip_code": "",
            "dob": "",
            "gender": "",
            "address": "",
            "phone": ""
        }
        
        # Store this data in the context
        pipeline_instance.context.extracted_info = fallback_data
        
        return {
            "success": False,
            "message": "There was an error processing your image. Please try again.",
            "extracted_info": fallback_data,
            "ui_action": "suggest_new_upload",
            "continue_flow": False,
            "show_upload_again": True,
            "show_manual_entry": False,
            "is_valid_national_id": False
        }

def update_missing_field(user_context, field: str, value: str) -> Dict[str, Any]:
    """
    Update a missing field in the user context from manual input
    
    Args:
        user_context: The user context object
        field: The field name to update
        value: The value to set
        
    Returns:
        Dict with update status and remaining missing fields
    """
    try:
        logger.info(f"Updating missing field: {field} = {value}")
        
        # Don't update if value is empty
        if not value or value.strip() == "":
            return {
                "success": False,
                "message": f"The {field} field cannot be empty. Please provide a value.",
                "remaining_fields": getattr(user_context, 'missing_fields', [])
            }
        
        # Update appropriate field based on field name
        if field == "name":
            # Split name into first and last if possible
            name_parts = value.split()
            if len(name_parts) > 1:
                user_context.first_name = name_parts[0]
                user_context.last_name = " ".join(name_parts[1:])
            else:
                user_context.first_name = value
                # Set last_name to empty if not provided
                if hasattr(user_context, 'last_name'):
                    user_context.last_name = ""
        elif field == "id_number":
            user_context.national_id = value
        elif field == "country":
            user_context.country = value
        elif field == "state":
            user_context.state = value
        elif field == "city":
            user_context.city = value
        elif field == "zip_code":
            user_context.zip_code = value
        elif field == "dob" or field == "date_of_birth":
            user_context.birth_date = value
        elif field == "gender":
            user_context.gender = value
        elif field == "address":
            user_context.address = value
        elif field == "phone" or field == "phone_number":
            user_context.phone = value
        
        # Update extracted_info dictionary to keep things consistent
        if not hasattr(user_context, 'extracted_info'):
            user_context.extracted_info = {}
        
        # Map back from user_context field names to standard field names
        if field == "dob" or field == "date_of_birth":
            user_context.extracted_info["dob"] = value
        elif field == "id_number":
            user_context.extracted_info["id_number"] = value
        else:
            user_context.extracted_info[field] = value
        
        # Remove field from missing_fields if it exists
        if hasattr(user_context, 'missing_fields') and field in user_context.missing_fields:
            user_context.missing_fields.remove(field)
        
        # Check if we've completed all required fields
        remaining_fields = getattr(user_context, 'missing_fields', [])
        if not remaining_fields:
            user_context.id_upload_complete = True
            user_context.ready_to_transition = True
            response_text = "Thank you! I now have all the information I need."
            
            # Format confirmation of all captured details
            details = []
            if user_context.first_name:
                name = user_context.first_name
                if hasattr(user_context, 'last_name') and user_context.last_name:
                    name += f" {user_context.last_name}"
                details.append(f"Name: {name}")
            if hasattr(user_context, 'national_id') and user_context.national_id:
                details.append(f"ID Number: {user_context.national_id}")
            if user_context.country:
                details.append(f"Country: {user_context.country}")
            
            if details:
                response_text += " Here's a summary of the information I've captured:\n" + "\n".join(details)
            
            return {
                "success": True,
                "message": response_text,
                "remaining_fields": [],
                "complete": True,
                "field_updated": field,
                "value": value
            }
        else:
            # Still missing some fields
            remaining_fields_str = ", ".join(remaining_fields)
            response_text = f"Thanks! I've updated your {field}. I still need: {remaining_fields_str}. Could you please provide these details?"
            
            return {
                "success": True,
                "message": response_text,
                "remaining_fields": remaining_fields,
                "field_updated": field,
                "value": value
            }
    
    except Exception as e:
        logger.error(f"Error updating field {field}: {str(e)}", exc_info=True)
        return {
            "success": False,
            "message": f"I couldn't update the {field} information. Could you please try again?",
            "error": str(e)
        }

def extract_information_from_text(text: str, user_context):
    """
    Extract user information from text input using LLM
    
    Args:
        text: User input text that might contain identity information
        user_context: The user context object
        
    Returns:
        Dict with extraction status and detected fields
    """
    try:
        # Create OpenAI client for text analysis with direct credentials
        client = openai.AzureOpenAI(
            api_key="3pK8Z8C2KJa2zRSiwpMGsSZjX3BDdct2nyCmR4vSOQjPvvocEFhsJQQJ99AKACfhMk5XJ3w3AAAAACOGImDO",
            api_version="2024-08-01-preview",
            azure_endpoint="https://santo-m42luu2w-swedencentral.cognitiveservices.azure.com/"
        )
        
        # Get existing missing fields
        missing_fields = getattr(user_context, 'missing_fields', [])
        
        # Get already extracted fields to inform the model
        existing_data = {}
        for field in ['name', 'id_number', 'country', 'state', 'city', 'zip_code', 'dob', 'gender', 'address', 'phone']:
            # Check main user context fields first
            if field == 'name':
                if hasattr(user_context, 'first_name') and user_context.first_name:
                    full_name = user_context.first_name
                    if hasattr(user_context, 'last_name') and user_context.last_name:
                        full_name += " " + user_context.last_name
                    existing_data['name'] = full_name
            elif field == 'id_number' and hasattr(user_context, 'national_id') and user_context.national_id:
                existing_data['id_number'] = user_context.national_id
            elif hasattr(user_context, field) and getattr(user_context, field):
                existing_data[field] = getattr(user_context, field)
            # Also check the extracted_info dictionary
            elif hasattr(user_context, 'extracted_info') and field in user_context.extracted_info and user_context.extracted_info[field]:
                existing_data[field] = user_context.extracted_info[field]
        
        # Define the fields we're focusing on
        fields_to_extract = missing_fields if missing_fields else [
            "name", "id_number", "country", "state", "city", "zip_code", 
            "dob", "gender", "address", "phone"
        ]
        
        # Prepare system message with information about existing data and missing fields
        system_message = f"""
        You are a helpful assistant specialized in extracting personal information from text.
        
        The user will provide text that might contain identity information.
        We are specifically looking for these fields: {', '.join(fields_to_extract)}.
        
        We already have the following information:
        {json.dumps(existing_data, indent=2)}
        
        We still need to extract: {', '.join(missing_fields) if missing_fields else 'any missing information'}
        
        Respond in JSON format with ONLY the fields you can confidently extract from the new text.
        DO NOT include fields that are already provided above unless the user is explicitly updating them.
        If a field is not present in the text or you're not confident, do not include it in the response.
        """
        
        # Call the API
        response = client.chat.completions.create(
            model="gpt-4o-json-mode-deployment",
            messages=[
                {"role": "system", "content": system_message},
                {"role": "user", "content": text}
            ],
            response_format={"type": "json_object"},
            max_tokens=500
        )
        
        # Parse the response
        extracted_data = json.loads(response.choices[0].message.content)
        logger.info(f"Extracted information from text: {extracted_data}")
        
        # Update user context with extracted information
        updated_fields = []
        for field, value in extracted_data.items():
            if value and field in fields_to_extract:
                update_result = update_missing_field(user_context, field, value)
                if update_result["success"]:
                    updated_fields.append(field)
        
        # Get remaining missing fields after update
        remaining_fields = getattr(user_context, 'missing_fields', [])
        
        # Generate response based on what was extracted
        if updated_fields:
            fields_str = ", ".join(updated_fields)
            response_text = f"I've extracted the following information from your message: {fields_str}."
            
            # Check if we still need additional information
            if remaining_fields:
                missing_str = ", ".join(remaining_fields)
                response_text += f" I still need: {missing_str}. Could you please provide these details?"
                return {
                    "success": True,
                    "message": response_text,
                    "extracted_fields": updated_fields,
                    "remaining_fields": remaining_fields,
                    "partial_success": True
                }
            else:
                response_text += " Thank you, I now have all the information I need."
                user_context.id_upload_complete = True
                return {
                    "success": True,
                    "message": response_text,
                    "extracted_fields": updated_fields,
                    "remaining_fields": []
                }
        else:
            response_text = "I couldn't find any new identity information in your message."
            if remaining_fields:
                missing_str = ", ".join(remaining_fields)
                response_text += f" I still need: {missing_str}. Could you please provide these details?"
                return {
                    "success": False,
                    "message": response_text,
                    "extracted_fields": [],
                    "remaining_fields": remaining_fields,
                    "next_action": "ask_missing"
                }
            else:
                response_text = "I already have all the required information. Thank you!"
                user_context.id_upload_complete = True
                return {
                    "success": True,
                    "message": response_text,
                    "extracted_fields": [],
                    "remaining_fields": []
                }
        
    except Exception as e:
        logger.error(f"Error extracting information from text: {str(e)}", exc_info=True)
        return {
            "success": False,
            "message": "I had trouble processing your information. Could you please format it more clearly, with each piece of information on a separate line?",
            "error": str(e)
        }

def process_image_upload(pipeline_instance, image_data: str) -> Dict[str, Any]:
    """
    Connector function to integrate the image agent with the sales pipeline
    
    Args:
        pipeline_instance: The sales pipeline instance
        image_data: Base64 encoded image data
        
    Returns:
        Dict with processing results and response to show the user
    """
    # Log start of image processing
    logger.info("Starting image upload processing in pipeline")
    
    # Process the image with the image agent and get results
    results = handle_image_upload(image_data, pipeline_instance)
    
    # Check for successful processing
    if results["success"]:
        # Success could be complete or partial
        is_partial = results.get("partial_success", False)
        
        if is_partial:
            # Partial success - we extracted some data but need more
            # The user context has already been updated with available fields
            missing_fields = results.get("missing_fields", [])
            
            response = {
                "success": True,  # Mark as success to continue the flow
                "message": results["message"],
                "ui_update": {
                    "show_id_fields": True,
                    "highlighted_fields": missing_fields,
                    "proceed_button": False,
                    "upload_another": False,
                    "show_manual_entry": True
                },
                "partial_success": True,
                "extracted_fields": [field for field in results.get("extracted_info", {}) if results["extracted_info"][field]]
            }
        else:
            # Complete success - all required fields extracted
            pipeline_instance.context.ready_to_transition = True
            
            response = {
                "success": True,
                "message": results["message"],
                "ui_update": {
                    "show_id_fields": False,
                    "proceed_button": True,
                    "upload_another": False
                }
            }
    else:
        # Not successful, but check if we have next_action guidance
        next_action = results.get("next_action", "")
        
        if next_action == "retry_upload_keep_data":
            # We have some data but need a better image
            response = {
                "success": False,
                "message": results["message"],
                "ui_update": {
                    "show_id_fields": False,
                    "proceed_button": False,
                    "upload_another": True,
                    "show_extracted_data": True,
                    "extracted_fields": [field for field in results.get("extracted_info", {}) if results["extracted_info"][field]]
                }
            }
        elif next_action == "manual_entry":
            # Switch to manual entry mode after multiple failed attempts
            response = {
                "success": False,
                "message": results["message"],
                "ui_update": {
                    "show_id_fields": True,
                    "proceed_button": False,
                    "upload_another": False
                }
            }
        elif next_action == "ask_missing" or "missing_fields" in results:
            # We extracted partial data, ask for specific missing fields
            response = {
                "success": False,
                "message": results["message"],
                "ui_update": {
                    "show_id_fields": True,
                    "highlighted_fields": results.get("missing_fields", []),
                    "proceed_button": False,
                    "upload_another": True
                }
            }
        else:
            # General failure, suggest trying again
            response = {
                "success": False,
                "message": results["message"],
                "ui_update": {
                    "show_id_fields": False,
                    "proceed_button": False,
                    "upload_another": True
                }
            }
    
    # Add to conversation history
    pipeline_instance.context.add_message("system", f"[Image processed: {results['success']}]")
    pipeline_instance.context.add_message("assistant", results["message"])
    
    # Update LLM call logs with this interaction
    if hasattr(pipeline_instance, 'add_llm_call'):
        pipeline_instance.add_llm_call(
            node_id="image_processing",
            node_name="ID Document Processing",
            node_type="action",
            prompt="Process ID document image",
            user_input="[IMAGE UPLOAD]",
            response=results["message"]
        )
    
    return response 

def call_vision_api(image_data: str, prompt: str) -> Dict[str, Any]:
    """Call Azure OpenAI Vision API to analyze an image
    
    Args:
        image_data: Base64 encoded image data or data URL
        prompt: The prompt to guide the image analysis
        
    Returns:
        Dict with extracted information
    """
    try:
        # Prepare the image URL - handle both data URLs and raw base64
        image_url = image_data
        if not image_data.startswith('data:'):
            # If it's raw base64, convert to a data URL
            image_url = f"data:image/jpeg;base64,{image_data}"
        
        # Make the API call to Azure OpenAI Vision
        response = vision_client.chat.completions.create(
            model=vision_model,
            messages=[
                {"role": "system", "content": "You are a document analyzer specialized in extracting structured information from IDs and documents. Always respond in valid JSON format."},
                {"role": "user", "content": [
                    {"type": "text", "text": prompt},
                    {"type": "image_url", "image_url": {"url": image_url}}
                ]}
            ],
            max_tokens=4000
        )
        
        # Extract the response content
        content = response.choices[0].message.content
        
        # Try to parse the response as JSON
        try:
            # First try direct JSON parsing
            return json.loads(content)
        except json.JSONDecodeError:
            # If direct parsing fails, try to extract JSON from the response
            # Look for JSON between triple backticks
            json_match = re.search(r"```(?:json)?\s*({[\s\S]*?})\s*```", content)
            if json_match:
                return json.loads(json_match.group(1))
            else:
                # If no JSON found, try to extract structured information
                extracted_data = {
                    "success": False,  # Default to false when we have to fallback to manual extraction
                    "name": "",
                    "id_number": "",
                    "country": "",
                    "state": "",
                    "city": "",
                    "zip_code": "",
                    "dob": "",
                    "gender": "",
                    "address": "",
                    "phone": ""
                }
                
                # Try to extract field values from the text
                lines = content.strip().split('\n')
                found_any_data = False
                for line in lines:
                    if ':' in line:
                        key, value = line.split(':', 1)
                        key = key.strip().lower().replace(' ', '_')
                        value = value.strip()
                        if value and value != 'null':
                            extracted_data[key] = value
                            found_any_data = True
                
                # Update success field if we found any data
                if "success" not in extracted_data:
                    extracted_data["success"] = found_any_data
                    
                # If the text mentions "not a valid ID" or similar, ensure success is False
                if re.search(r'not\s+a\s+(valid|national|id|document)', content.lower()):
                    extracted_data["success"] = False
                
                return extracted_data
                
    except Exception as e:
        logger.error(f"Error calling Vision API: {str(e)}", exc_info=True)
        return {
            "success": False,
            "name": "",
            "id_number": "",
            "country": "",
            "state": "",
            "city": "",
            "zip_code": "",
            "dob": "",
            "gender": "",
            "address": "",
            "phone": ""
        } 