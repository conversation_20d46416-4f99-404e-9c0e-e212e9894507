package users

import (
	flows "apps/spiro/controllers/whatsapp/flows"
)

type MiddlewareResponse struct {
	Success bool `json:"success"`
}

type SellerOrdersScreen struct {
	Message        string                  `json:"message"`
	MessageVisible bool                    `json:"message_visible"`
	Orders         []flows.FlowButtonGroup `json:"orders"`
	OrdersVisible  bool                    `json:"orders_visible"`
}

type SellerOrderViewScreen struct {
	OrderId        string                  `json:"order_id"`
	Order          string                  `json:"order"`
	Actions        []flows.FlowButtonGroup `json:"actions"`
	ActionVisible  bool                    `json:"action_visible"`
	ActionRequired bool                    `json:"action_required"`
}

type SellerOrderFulfillmentsScreen struct {
	OrderId      string                  `json:"order_id"`
	Fulfillments []flows.FlowButtonGroup `json:"fulfillments"`
}

type SellerOrderFulfillmentStatusesScreen struct {
	OrderId             string                  `json:"order_id"`
	Fulfillment         string                  `json:"fulfillment"`
	FulfillmentStatuses []flows.FlowButtonGroup `json:"fulfillment_statuses"`
}

type SellerMiddlewareOrderResponse struct {
	Success bool `json:"success"`
	Data    struct {
		TotalPages uint `json:"total_pages"`
		Orders     []struct {
			OrderId string `json:"order_id"`
			Text    string `json:"text"`
		} `json:"orders"`
	} `json:"data"`
}

type SellerMiddlewareOrderViewResponse struct {
	Success bool `json:"success"`
	Data    struct {
		OrderId       string `json:"order_id"`
		Text          string `json:"text"`
		IsCancellable bool   `json:"is_cancellable"`
		IsReturnable  bool   `json:"is_returnable"`
	} `json:"data"`
}

type SellerMiddlewareOrderFulfillmentsResponse struct {
	Success bool `json:"success"`
	Data    struct {
		OrderId      string   `json:"order_id"`
		Fulfillments []string `json:"fulfillments"`
	} `json:"data"`
}

type SellerMiddlewareOrderFulfillmentsStatusResponse struct {
	Success bool `json:"success"`
	Data    struct {
		OrderId     string   `json:"order_id"`
		Fulfillment string   `json:"fulfillment"`
		Status      []string `json:"status"`
	} `json:"data"`
}

type SellerOrdersScreenPayload struct {
	OrderId string `json:"order_id"`
}

type SellerOrderViewScreenPayload struct {
	OrderId string `json:"order_id"`
	Action  string `json:"action"`
}

type SellerOrderFulfillmentsScreenPayload struct {
	OrderId     string `json:"order_id"`
	Fulfillment string `json:"fulfillment"`
}

//-----------------------------RETURNS--------------------------------------

type SellerReturnsScreen struct {
	Message        string                  `json:"message"`
	MessageVisible bool                    `json:"message_visible"`
	Returns        []flows.FlowButtonGroup `json:"returns"`
	ReturnsVisible bool                    `json:"returns_visible"`
}

type SellerReturnViewScreen struct {
	ReturnId       string                  `json:"return_id"`
	Return         string                  `json:"return"`
	Actions        []flows.FlowButtonGroup `json:"actions"`
	ActionVisible  bool                    `json:"action_visible"`
	ActionRequired bool                    `json:"action_required"`
}

type SellerReturnStatusScreen struct {
	ReturnId string                  `json:"return_id"`
	Statuses []flows.FlowButtonGroup `json:"statuses"`
}

type SellerMiddlewareReturnResponse struct {
	Success bool `json:"success"`
	Data    struct {
		TotalPages uint `json:"total_pages"`
		Returns    []struct {
			ReturnId string `json:"return_id"`
			Text     string `json:"text"`
		} `json:"returns"`
	} `json:"data"`
}

type SellerMiddlewareReturnViewResponse struct {
	Success bool `json:"success"`
	Data    struct {
		OrderId       string `json:"order_id"`
		Text          string `json:"text"`
		IsCancellable bool   `json:"is_cancellable"`
		IsReturnable  bool   `json:"is_returnable"`
	} `json:"data"`
}

type SellerMiddlewareReturnStatusResponse struct {
	Success bool `json:"success"`
	Data    struct {
		ReturnId string   `json:"return_id"`
		Status   []string `json:"status"`
	} `json:"data"`
}

type SellerReturnsScreenPayload struct {
	ReturnId string `json:"return_id"`
}

type SellerReturnViewScreenPayload struct {
	ReturnId string `json:"return_id"`
	Action   string `json:"action"`
}

//-----------------------------IGM--------------------------------------

type SellerTicketsScreen struct {
	Message        string                  `json:"message"`
	MessageVisible bool                    `json:"message_visible"`
	Tickets        []flows.FlowButtonGroup `json:"tickets"`
	TicketsVisible bool                    `json:"tickets_visible"`
	InitValues     map[string]interface{}  `json:"init_values"`
	Pages          []flows.FlowButtonGroup `json:"pages"`
}

type SellerMiddlewareTicketResponse struct {
	Success bool `json:"success"`
	Data    struct {
		TotalPages int `json:"total_pages"`
		Tickets    []struct {
			TicketId string `json:"ticket_id"`
			Text     string `json:"text"`
		} `json:"tickets"`
	} `json:"data"`
}

type SellerTicketScreenPayload struct {
	TicketId string `json:"ticket_id"`
}

type SellerTicketViewScreen struct {
	TicketId       string                  `json:"ticket_id"`
	Ticket         string                  `json:"ticket"`
	Actions        []flows.FlowButtonGroup `json:"actions"`
	ActionVisible  bool                    `json:"action_visible"`
	ActionRequired bool                    `json:"action_required"`
}

type SellerMiddlewareTicketViewResponse struct {
	Success bool `json:"success"`
	Data    struct {
		TicketId string `json:"ticket_id"`
		Text     string `json:"text"`
	} `json:"data"`
}

type SellerTicketViewScreenPayload struct {
	TicketId string `json:"ticket_id"`
	Action   string `json:"action"`
	PageNo  string `json:"page_no"`}

type SellerTicketUpdateScreen struct {
	Statuses []flows.FlowButtonGroup `json:"statuses"`
	TicketId string                  `json:"ticket_id"`
}

type SellerMiddlewareTicketStatusResponse struct {
	Success bool `json:"success"`
	Data    struct {
		Statuses []struct {
			DisplayName string `json:"display_name"`
		} `json:"statuses"`
	} `json:"data"`
}

type SellerTicketReplyScreen struct {
	TicketId string `json:"ticket_id"`
}

//-----------------------------PRODUCTS--------------------------------------

type SellerProductsScreen struct {
	Message         string                  `json:"message"`
	MessageVisible  bool                    `json:"message_visible"`
	Products        []flows.FlowButtonGroup `json:"products"`
	ProductsVisible bool                    `json:"products_visible"`
}

type SellerProductViewScreen struct {
	ProductId      string                  `json:"product_id"`
	Product        string                  `json:"product"`
	Actions        []flows.FlowButtonGroup `json:"actions"`
	ActionVisible  bool                    `json:"action_visible"`
	ActionRequired bool                    `json:"action_required"`
}

type SellerProductUpdateScreen struct {
	ProductId string `json:"product_id"`
}

type SellerMiddlewareProductResponse struct {
	Success bool `json:"success"`
	Data    struct {
		TotalPages uint `json:"total_pages"`
		Products   []struct {
			ProductId string `json:"product_id"`
			Text      string `json:"text"`
		} `json:"products"`
	} `json:"data"`
}

type SellerMiddlewareProductViewResponse struct {
	Success bool `json:"success"`
	Data    struct {
		ProductId string `json:"product_id"`
		Text      string `json:"text"`
	} `json:"data"`
}

type SellerProductsScreenPayload struct {
	ProductId string `json:"product_id"`
}

type SellerProductViewScreenPayload struct {
	ProductId string `json:"product_id"`
	Action    string `json:"action"`
}

//-----------------------------ONBOARDING--------------------------------------

type UserDetailsResponse struct {
	Success bool        `json:"success"`
	Data    UserDetails `json:"data"`
}

type StoreDetailsResponse struct {
	Success bool         `json:"success"`
	Data    StoreDetails `json:"data"`
}

type BankDetailsResponse struct {
	Success bool        `json:"success"`
	Data    BankDetails `json:"data"`
}

type BusinessDetailsResponse struct {
	Success bool            `json:"success"`
	Data    BusinessDetails `json:"data"`
}

type UserDetails struct {
	PhoneNumber     string `json:"phone_number,omitempty"`
	Name            string `json:"name"`
	Email           string `json:"email"`
	City            string `json:"city"`
	State           string `json:"state"`
	PinCode         string `json:"pin_code"`
	Address         string `json:"address"`
	ContactToTime   string `json:"contact_to_time"`
	ContactFromTime string `json:"contact_from_time"`
}

type StoreDetails struct {
	PhoneNumber        string `json:"phone_number,omitempty"`
	Name               string `json:"name"`
	ShortDesc          string `json:"short_desc"`
	LongDesc           string `json:"long_desc"`
	Website            string `json:"website"`
	FssaiNumber        string `json:"fssai_number"`
	StoreSymbols       string `json:"store_symbols"`
	StoreBanner        string `json:"store_banner"`
	StoreContactNumber string `json:"store_contact_number"`
	StoreEmail         string `json:"store_email"`
	City               string `json:"city"`
	State              string `json:"state"`
	PinCode            string `json:"pin_code"`
	Address            string `json:"address"`
	OrderMinimumValue  string `json:"order_minimum_value"`
	FulfillmentType    string `json:"fulfillment_type,omitempty"`
}

type BusinessDetails struct {
	PhoneNumber       string `json:"phone_number,omitempty"`
	BusinessName      string `json:"business_name"`
	AccountHolderName string `json:"account_holder_name"`
	Gstin             string `json:"gstin"`
	PanCard           string `json:"pan_card"`
}

type BankDetails struct {
	PhoneNumber       string `json:"phone_number,omitempty"`
	AccountHolderName string `json:"account_holder_name"`
	AccountNumber     string `json:"account_number"`
	IfscCode          string `json:"ifsc_code"`
	BankName          string `json:"bank_name"`
}

type FiltersResponse struct {
	Status    string `json:"status"`
	StartDate string `json:"start_date"`
	EndDate   string `json:"end_date"`
}

type UserDetail struct {
	PhoneNumber     string `json:"phone_number,omitempty"`
	Name            string `json:"name"`
	Email           string `json:"email"`
	City            string `json:"city"`
	State           string `json:"state"`
	PinCode         string `json:"pin_code"`
	Address         string `json:"address"`
	ContactToTime   string `json:"contact_to_time"`
	ContactFromTime string `json:"contact_from_time"`
}

type UserExtractedData struct {
	Mobile          	string `json:"Mobile,omitempty"`
	First_Name      	string `json:"First_Name"`
	Last_Name           string `json:"Last_Name"`
	Email           	string `json:"Email"`
    Country		    	string `json:"Country"`
	State           	string `json:"Street"`
	City            	string `json:"city"`
	Zip_Code        	string `json:"Zip_Code"`
	Building         	string `json:"Building"`
	Birth_Date  		string `json:"Birth_Date"`
	Gender          	string `json:"Gender"`
}