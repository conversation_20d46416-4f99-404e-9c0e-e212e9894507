// <PERSON><PERSON><PERSON> Lead Agent Chat Interface

class ChatInterface {
    constructor() {
        this.phoneNumber = "254700000000"; // Demo phone number
        this.conversationStarted = false;
        this.isTyping = false;
        
        this.initializeElements();
        this.bindEvents();
    }
    
    initializeElements() {
        this.chatMessages = document.getElementById('chatMessages');
        this.messageInput = document.getElementById('messageInput');
        this.sendButton = document.getElementById('sendButton');
        this.startButton = document.getElementById('startButton');
        this.statusBar = document.getElementById('statusBar');
        this.typingIndicator = document.getElementById('typingIndicator');
    }
    
    bindEvents() {
        if (this.sendButton) {
            this.sendButton.addEventListener('click', () => this.sendMessage());
        }
        
        if (this.startButton) {
            this.startButton.addEventListener('click', () => this.startConversation());
        }
        
        if (this.messageInput) {
            this.messageInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });
        }
    }
    
    addMessage(message, isUser = false, messageType = 'text') {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${isUser ? 'user' : 'bot'}`;
        
        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        contentDiv.textContent = message;
        
        messageDiv.appendChild(contentDiv);
        
        if (this.chatMessages) {
            this.chatMessages.appendChild(messageDiv);
            this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
        }
    }
    
    updateStatus(status) {
        if (this.statusBar) {
            this.statusBar.textContent = status;
        }
    }
    
    showTyping() {
        if (this.typingIndicator) {
            this.typingIndicator.classList.add('show');
            this.isTyping = true;
        }
    }
    
    hideTyping() {
        if (this.typingIndicator) {
            this.typingIndicator.classList.remove('show');
            this.isTyping = false;
        }
    }
    
    enableInput() {
        if (this.messageInput) {
            this.messageInput.disabled = false;
            this.messageInput.focus();
        }
        if (this.sendButton) {
            this.sendButton.disabled = false;
        }
        if (this.startButton) {
            this.startButton.style.display = 'none';
        }
    }
    
    disableInput() {
        if (this.messageInput) {
            this.messageInput.disabled = true;
        }
        if (this.sendButton) {
            this.sendButton.disabled = true;
        }
    }
    
    async startConversation() {
        this.updateStatus('Starting conversation...');
        this.disableInput();
        
        try {
            const response = await fetch('/api/lead/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    from: this.phoneNumber,
                    force_restart: true
                })
            });

            const data = await response.json();
            
            if (data.success) {
                this.addMessage(data.message, false, data.message_type);
                this.conversationStarted = true;
                this.enableInput();
                this.updateStatus(`Active - ${data.flow_name || 'Flow'}: ${data.current_step || 'In Progress'}`);
            } else {
                this.addMessage('Error starting conversation: ' + data.message, false);
                this.updateStatus('Error - Please try again');
            }
        } catch (error) {
            this.addMessage('Error connecting to server. Please check your connection.', false);
            this.updateStatus('Connection error');
            console.error('Error:', error);
        }
    }
    
    async sendMessage() {
        if (!this.messageInput || !this.conversationStarted) return;
        
        const message = this.messageInput.value.trim();
        if (!message) return;
        
        // Add user message to chat
        this.addMessage(message, true);
        this.messageInput.value = '';
        
        // Show typing indicator and update status
        this.showTyping();
        this.updateStatus('Processing...');
        this.disableInput();
        
        try {
            const response = await fetch('/api/lead/continue', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    from: this.phoneNumber,
                    message: message
                })
            });

            const data = await response.json();
            
            this.hideTyping();
            
            if (data.success) {
                this.addMessage(data.message, false, data.message_type);
                this.updateStatus(`Active - ${data.flow_name || 'Flow'}: ${data.current_step || 'In Progress'}`);
                
                // Check if conversation is completed
                if (data.current_step === 'completed') {
                    this.updateStatus('Conversation completed - Thank you!');
                }
            } else {
                this.addMessage('Error: ' + data.message, false);
                this.updateStatus('Error - Please try again');
            }
        } catch (error) {
            this.hideTyping();
            this.addMessage('Error connecting to server. Please check your connection.', false);
            this.updateStatus('Connection error');
            console.error('Error:', error);
        } finally {
            this.enableInput();
        }
    }
}

// Initialize chat interface when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.chatInterface = new ChatInterface();
});
