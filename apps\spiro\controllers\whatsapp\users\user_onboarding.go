package users

import (
	"errors"
	"fmt"
	// "regexp"
	// "time"
    env "apps/spiro/config"
	flows "apps/spiro/controllers/whatsapp/flows"
	whatsapp "libs/shared/db_connectors/model"
	json "encoding/json"
	

	helpers "libs/shared/utils/helpers"

    "strings"
    
    "os"
    "path/filepath"
    "runtime"
    
)

func (s *userFlowsService) UserScreenOnboardingInit(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact, decryptedData flows.DataChannelDecrypted) (flows.DataChannelResponse, error) {
	fmt.Println("UserScreenOnboardingInit--------- ")
	userDetails, err := s.GetUserDetails(account, contact)
	if err != nil {
		return flows.DataChannelResponse{}, errors.New("error from middleware")
	}

	dataChannelResponse := flows.DataChannelResponse{
		Version: decryptedData.Version,
		Screen:  "BASIC_DETAILS",
		Data: map[string]interface{}{
			"init_values": userDetails,
			"Nation_ID":"Uploaded",
			"error_messages": map[string]interface{}{
				"test": "test",
			},
		},
	}
	return dataChannelResponse, nil
}

func (s *userFlowsService) GetUserDetails(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact) (UserExtractedData, error) {
	query:= map[string]interface{}{
		"whatsapp_number": contact.WhatsappNumber,	
		"whatsapp_account_id": account.ID,
	}
	fmt.Println("GetUserDetails--------- ",query)
	contact,err:=s.contact_Repository.FindOne(env.GlobalEnv["POSTGRES_CREDENTIAL"],query)
	if err != nil {
		return UserExtractedData{}, errors.New("error from middleware")
	}

	var userDetailsResponse UserExtractedData
	helpers.JsonMarshaller(contact, &userDetailsResponse)
	
	var notesData map[string]interface{}
	if err := json.Unmarshal(contact.Notes, &notesData); err != nil {
		return UserExtractedData{}, fmt.Errorf("failed to unmarshal notes: %v", err)
	}

	userDetailsResponse.Mobile = contact.WhatsappNumber
	fullName := helpers.GetStringValue(notesData["name"])
    nameParts := strings.Fields(fullName)
    if len(nameParts) > 0 {
        userDetailsResponse.First_Name = nameParts[0]
        if len(nameParts) > 1 {
            userDetailsResponse.Last_Name = strings.Join(nameParts[1:], " ")
        }
    }
	// userDetailsResponse.First_Name = helpers.GetStringValue(notesData["name"])
	userDetailsResponse.Country = helpers.GetStringValue(notesData["country"])
	userDetailsResponse.State = helpers.GetStringValue(notesData["state"])
	userDetailsResponse.City = helpers.GetStringValue(notesData["city"])
	userDetailsResponse.Zip_Code = helpers.GetStringValue(notesData["zip_code"])
	userDetailsResponse.Building = helpers.GetStringValue(notesData["address"])
	// userDetailsResponse.Birth_Date = helpers.GetStringValue(notesData["dob"])
    dobStr := helpers.GetStringValue(notesData["dob"])
	if dobStr != "" {
	    // Parse DD.MM.YYYY format
	    parts := strings.Split(dobStr, ".")
	    if len(parts) == 3 {
	        userDetailsResponse.Birth_Date = fmt.Sprintf("%s-%s-%s", parts[2], parts[1], parts[0])
	    } else {
	        userDetailsResponse.Birth_Date = dobStr // Keep original if format doesn't match
	    }
	}
	userDetailsResponse.Gender = helpers.GetStringValue(notesData["gender"])
	

	return userDetailsResponse, nil
}

func (s *userFlowsService) UserScreenBacicDetails(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact, decryptedData flows.DataChannelDecrypted) (flows.DataChannelResponse, error) {
	fmt.Println("SellerScreenBusinessDetails--------- ")

err1 := s.UserBasicDetailsData(account, contact, decryptedData)
if err1 != nil {
	return flows.DataChannelResponse{}, errors.New("error from middleware")
}

	

	dataChannelResponse := flows.DataChannelResponse{
		Version: decryptedData.Version,
		Screen:  "SUCCESS",
		Data: map[string]interface{}{
			"extension_message_response": map[string]interface{}{
				"params": map[string]interface{}{
					"flow_token": decryptedData.FlowToken,
				},
			},
		},
	}

	return dataChannelResponse, nil
}
func (s *userFlowsService) UserBasicDetailsData(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact, decryptedData flows.DataChannelDecrypted) (error) {

	// Extract user data from decryptedData
	var userData map[string]interface{}
	helpers.JsonMarshaller(decryptedData.Data, &userData)

	// Create filename using contact number
	// Get current file path
	_, filename, _, _ := runtime.Caller(0)
	currentDir := filepath.Dir(filename)
	userDataDir := filepath.Join(currentDir, "data")
	filename = filepath.Join(userDataDir, fmt.Sprintf("user_a%d_c%d.go", account.ID, contact.ID))

	// Create the data directory if it doesn't exist
	os.MkdirAll(userDataDir, os.ModePerm)

	// Read existing file content and parse it
	var otherContent string

	if _, err := os.Stat(filename); err == nil {
	    contentBytes, err := os.ReadFile(filename)
	    if err == nil {
	        content := string(contentBytes)
	      
	        // Extract existing UserData if present
	        userDataStart := strings.Index(content, fmt.Sprintf("var UserData_a%d_c%d", account.ID, contact.ID))
	        if userDataStart != -1 {
	            // Keep content before UserData
	            otherContent = strings.TrimSpace(content[:userDataStart])
	        } else {
	            // Keep all content including package declaration
	            otherContent = strings.TrimSpace(content)
	        }
	    }
	}

	// Generate file content with package declaration if needed
	

	fileContent := fmt.Sprintf(`%s

var UserData_a%d_c%d = map[string]interface{}{
    "Birth_Date":      "%v",
    "Building":        "%v",
    "City":           "%v",
    "Country":        "%v",
    "Enquiry_Rating": "%v",
    "First_Name":     "%v",
    "Gender":         "%v",
    "KCB_Account_No": "%v",
    "KRA_PIN":        "%v",
    "Last_Name":      "%v",
    "Latitude":       "%v",
    "Lead_Source":    "%v",
    "Longitude":      "%v",
    "Mobile":         "%v",
    "National_ID":    "%v",
    "Rating_Reason":  "%v",
    "Sacco_Name":     "%v",
    "State":          "%v",
    "Street":         "%v",
    "Zip_Code":       "%v",
}`,

        otherContent,
        account.ID, contact.ID,
        userData["Birth_Date"],
        userData["Building"],
        userData["City"],
        userData["Country"],
        userData["Enquiry_Rating"],
        userData["First_Name"],
        userData["Gender"],
        userData["KCB_Account_No"],
        userData["KRA_PIN"],
        userData["Last_Name"],
        userData["Latitude"],
        userData["Lead_Source"],
        userData["Longitude"],
        userData["Mobile"],
        userData["National_ID"],
        userData["Rating_Reason"],
        userData["Sacco_Name"],
        userData["State"],
        userData["Street"],
        userData["Zip_Code"])

	// Write to file
	err := os.WriteFile(filename, []byte(fileContent), 0644)
	if err != nil {
		return  fmt.Errorf("failed to write user data file: %v", err)
	}
	return nil
}

func (s *userFlowsService) UserImageData(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact, ImageData string) (error) {
    _, filename, _, _ := runtime.Caller(0)
    currentDir := filepath.Dir(filename)
    userDataDir := filepath.Join(currentDir, "data")
    filename = filepath.Join(userDataDir, fmt.Sprintf("user_a%d_c%d.go", account.ID, contact.ID))

    // Create the data directory if it doesn't exist
    os.MkdirAll(userDataDir, os.ModePerm)

    // Read existing content if file exists
    var existingContent string
    hasPackage := false
    if _, err := os.Stat(filename); err == nil {
        contentBytes, err := os.ReadFile(filename)
        if err == nil {
            existingContent = string(contentBytes)
            hasPackage = strings.Contains(existingContent, "package users")
        }
    }

    // Prepare package declaration
    packageDecl := ""
    if !hasPackage {
        packageDecl = "package users\n\n"
    }

    // Check if UserImageData exists and prepare new content
    userImageDataKey := fmt.Sprintf("UserImageData_a%d_c%d", account.ID, contact.ID)
    newUserImageData := fmt.Sprintf(`var %s = map[string]interface{}{
    "ImageBase64": "%v",
	}`, userImageDataKey, ImageData)

      // ... existing code ...
	  var finalContent string
	  if existingContent == "" {
		  // New file
		  finalContent = packageDecl + newUserImageData
	  } else {
		  // Existing file
		  if strings.Contains(existingContent, userImageDataKey) {
			  // Replace existing UserImageData
			  lines := strings.Split(existingContent, "\n")
			  var newLines []string
			  inUserImageData := false
			  for _, line := range lines {
				  if strings.Contains(line, userImageDataKey) {
					  inUserImageData = true
					  newLines = append(newLines, newUserImageData)
				  } else if inUserImageData {
					  if strings.Contains(line, "}") {
						  inUserImageData = false
					  }
				  } else {
					  newLines = append(newLines, line)
				  }
			  }
			  finalContent = strings.Join(newLines, "\n")
		  } else {
			  // Add new UserImageData while preserving existing content
			  if hasPackage {
				  finalContent = existingContent + "\n\n" + newUserImageData
			  } else {
				  finalContent = packageDecl + existingContent + "\n\n" + newUserImageData
			  }
		  }
	  }
  
	  // Write the final content to file
	  err := os.WriteFile(filename, []byte(finalContent), 0644)
	  if err != nil {
		  return nil
	  }
	  // ... rest of the code ...

	return nil
}