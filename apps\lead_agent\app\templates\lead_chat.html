<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>S<PERSON>ro EV Sales Agent</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-black: #0a0a0a;
            --secondary-black: #1a1a1a;
            --tertiary-black: #2a2a2a;
            --primary-white: #ffffff;
            --secondary-white: #f8f9fa;
            --tertiary-white: #e9ecef;
            --accent-gray: #6c757d;
            --border-gray: #dee2e6;
            --success-green: #28a745;
            --warning-orange: #fd7e14;
            --danger-red: #dc3545;
            --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
            --shadow-medium: 0 4px 20px rgba(0,0,0,0.15);
            --shadow-heavy: 0 8px 30px rgba(0,0,0,0.2);
            --gradient-primary: linear-gradient(135deg, #0a0a0a 0%, #2a2a2a 100%);
            --gradient-secondary: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-primary);
            min-height: 100vh;
            color: var(--primary-white);
            overflow-x: hidden;
        }

        .main-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .background-pattern {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.03;
            background-image:
                radial-gradient(circle at 25% 25%, white 2px, transparent 2px),
                radial-gradient(circle at 75% 75%, white 2px, transparent 2px);
            background-size: 50px 50px;
            z-index: 0;
        }

        .header {
            position: relative;
            z-index: 10;
            padding: 2rem 1rem;
            text-align: center;
            background: rgba(255, 255, 255, 0.02);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, #ffffff 0%, #e9ecef 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 2px 10px rgba(255,255,255,0.1);
        }

        .header .subtitle {
            font-size: 1.2rem;
            font-weight: 400;
            color: var(--tertiary-white);
            margin-bottom: 1rem;
        }

        .header .tagline {
            font-size: 0.9rem;
            color: var(--accent-gray);
            font-weight: 300;
        }

        .chat-container {
            position: relative;
            z-index: 10;
            max-width: 1000px;
            margin: 2rem auto;
            background: var(--primary-white);
            border-radius: 20px;
            box-shadow: var(--shadow-heavy);
            overflow: hidden;
            border: 1px solid var(--border-gray);
        }

        .chat-header {
            background: var(--gradient-primary);
            color: var(--primary-white);
            padding: 1.5rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .chat-header-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .chat-header-left i {
            font-size: 1.5rem;
            color: var(--primary-white);
        }

        .chat-header-info h3 {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.2rem;
        }

        .chat-header-info p {
            font-size: 0.85rem;
            color: var(--tertiary-white);
            font-weight: 300;
        }

        .session-controls {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .session-selector {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .session-selector label {
            font-size: 0.85rem;
            font-weight: 500;
            color: var(--tertiary-white);
        }

        .session-dropdown {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 0.5rem 1rem;
            color: var(--primary-white);
            font-size: 0.85rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .session-dropdown:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .session-dropdown option {
            background: var(--secondary-black);
            color: var(--primary-white);
        }

        .btn-clear-session {
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid rgba(220, 53, 69, 0.3);
            border-radius: 8px;
            padding: 0.5rem;
            color: #dc3545;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
        }

        .btn-clear-session:hover {
            background: rgba(220, 53, 69, 0.2);
            border-color: rgba(220, 53, 69, 0.5);
            transform: translateY(-1px);
        }

        .btn-clear-session:active {
            transform: translateY(0);
        }

        .btn-clear-session i {
            font-size: 0.9rem;
        }

        .chat-messages {
            height: 600px;
            overflow-y: auto;
            padding: 2rem;
            background: var(--gradient-secondary);
            position: relative;
        }

        .chat-messages::-webkit-scrollbar {
            width: 6px;
        }

        .chat-messages::-webkit-scrollbar-track {
            background: var(--tertiary-white);
            border-radius: 3px;
        }

        .chat-messages::-webkit-scrollbar-thumb {
            background: var(--accent-gray);
            border-radius: 3px;
        }

        .chat-messages::-webkit-scrollbar-thumb:hover {
            background: var(--tertiary-black);
        }

        .message {
            margin-bottom: 1.5rem;
            display: flex;
            align-items: flex-start;
            animation: fadeInUp 0.3s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0 0.75rem;
            flex-shrink: 0;
        }

        .message.bot .message-avatar {
            background: var(--gradient-primary);
            color: var(--primary-white);
            order: 1;
        }

        .message.user .message-avatar {
            background: var(--tertiary-black);
            color: var(--primary-white);
            order: 2;
        }

        .message-content {
            max-width: 65%;
            padding: 1rem 1.25rem;
            border-radius: 18px;
            font-size: 0.95rem;
            line-height: 1.5;
            word-wrap: break-word;
            position: relative;
            box-shadow: var(--shadow-light);
        }

        .message.bot .message-content {
            background: var(--primary-white);
            color: var(--primary-black);
            border-bottom-left-radius: 6px;
            border: 1px solid var(--border-gray);
            order: 2;
        }

        .message.user .message-content {
            background: var(--primary-black);
            color: var(--primary-white);
            border-bottom-right-radius: 6px;
            order: 1;
        }

        .message-time {
            font-size: 0.75rem;
            color: var(--accent-gray);
            margin-top: 0.5rem;
            opacity: 0.7;
        }

        .typing-indicator {
            display: none;
            padding: 1rem 1.25rem;
            background: var(--primary-white);
            border-radius: 18px;
            border-bottom-left-radius: 6px;
            margin-left: 3.5rem;
            border: 1px solid var(--border-gray);
            box-shadow: var(--shadow-light);
        }

        .typing-indicator.show {
            display: block;
            animation: fadeInUp 0.3s ease-out;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--accent-gray);
            animation: typingAnimation 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typingAnimation {
            0%, 80%, 100% {
                transform: scale(0.8);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .chat-footer {
            padding: 1.5rem 2rem;
            background: var(--primary-white);
            border-top: 1px solid var(--border-gray);
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding: 0.75rem 1rem;
            background: var(--secondary-white);
            border-radius: 10px;
            border: 1px solid var(--border-gray);
        }

        .status-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.85rem;
            color: var(--accent-gray);
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--success-green);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .status-indicator.error {
            background: var(--danger-red);
        }

        .status-indicator.warning {
            background: var(--warning-orange);
        }

        .input-container {
            display: flex;
            gap: 1rem;
            align-items: flex-end;
        }

        .input-wrapper {
            flex: 1;
            position: relative;
        }

        .message-input {
            width: 100%;
            padding: 1rem 1.25rem;
            border: 2px solid var(--border-gray);
            border-radius: 25px;
            font-size: 0.95rem;
            font-family: inherit;
            background: var(--primary-white);
            color: var(--primary-black);
            resize: none;
            min-height: 50px;
            max-height: 120px;
            transition: all 0.3s ease;
            outline: none;
        }

        .message-input:focus {
            border-color: var(--primary-black);
            box-shadow: 0 0 0 3px rgba(10, 10, 10, 0.1);
        }

        .message-input::placeholder {
            color: var(--accent-gray);
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            outline: none;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: var(--primary-black);
            color: var(--primary-white);
            box-shadow: var(--shadow-medium);
        }

        .btn-primary:hover {
            background: var(--secondary-black);
            transform: translateY(-2px);
            box-shadow: var(--shadow-heavy);
        }

        .btn-primary:active {
            transform: translateY(0);
        }

        .btn-secondary {
            background: var(--secondary-white);
            color: var(--primary-black);
            border: 2px solid var(--border-gray);
        }

        .btn-secondary:hover {
            background: var(--tertiary-white);
            border-color: var(--accent-gray);
            transform: translateY(-1px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .btn:disabled:hover {
            transform: none !important;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.2rem;
            }

            .header .subtitle {
                font-size: 1rem;
            }

            .chat-container {
                margin: 1rem;
                border-radius: 15px;
            }

            .chat-header {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }

            .chat-messages {
                height: 400px;
                padding: 1rem;
            }

            .message-content {
                max-width: 80%;
            }

            .chat-footer {
                padding: 1rem;
            }

            .input-container {
                gap: 0.75rem;
            }

            .session-controls {
                flex-direction: column;
                gap: 0.75rem;
                align-items: stretch;
            }

            .session-selector {
                justify-content: space-between;
            }
        }

        @media (max-width: 480px) {
            .header {
                padding: 1rem;
            }

            .header h1 {
                font-size: 1.8rem;
            }

            .chat-messages {
                height: 350px;
            }

            .message-avatar {
                width: 32px;
                height: 32px;
                font-size: 1rem;
            }

            .message-content {
                max-width: 85%;
                padding: 0.75rem 1rem;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="background-pattern"></div>

        <div class="header">
            <h1><i class="fas fa-bolt"></i> Spiro EV Sales Agent</h1>
            <p class="subtitle">Sales Ninja - Your Electric Vehicle Assistant</p>
            <p class="tagline">Discover the perfect electric vehicle solution for your needs</p>
        </div>

        <div class="chat-container">
            <div class="chat-header">
                <div class="chat-header-left">
                    <i class="fas fa-robot"></i>
                    <div class="chat-header-info">
                        <h3>Sales Ninja</h3>
                        <p>AI-Powered EV Consultant</p>
                    </div>
                </div>
                <div class="session-controls">
                    <div class="session-selector">
                        <label for="sessionSelect">Session:</label>
                        <select id="sessionSelect" class="session-dropdown">
                            <option value="254700000001">+254 700 000 001</option>
                            <option value="254700000002">+254 700 000 002</option>
                            <option value="254700000003">+254 700 000 003</option>
                            <option value="254700000004">+254 700 000 004</option>
                            <option value="254700000005">+254 700 000 005</option>
                            <option value="254711111111">+254 711 111 111</option>
                            <option value="254722222222">+254 722 222 222</option>
                            <option value="254733333333">+254 733 333 333</option>
                            <option value="254744444444">+254 744 444 444</option>
                            <option value="254755555555">+254 755 555 555</option>
                        </select>
                    </div>
                    <button class="btn-clear-session" id="clearSessionButton" title="Clear Session">
                        <i class="fas fa-trash-alt"></i>
                    </button>
                </div>
            </div>

            <div class="chat-messages" id="chatMessages">
                <div class="message bot">
                    <div class="message-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="message-content">
                        Welcome! I'm Sales Ninja from Spiro. Select a session number above to begin exploring our electric vehicle options and find the perfect EV solution for you.
                        <div class="message-time" id="welcomeTime"></div>
                    </div>
                </div>
            </div>

            <div class="typing-indicator" id="typingIndicator">
                <div class="typing-dots">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            </div>

            <div class="chat-footer">
                <div class="status-bar">
                    <div class="status-info">
                        <div class="status-indicator" id="statusIndicator"></div>
                        <span id="statusText">Ready to start conversation</span>
                    </div>
                    <div class="status-info">
                        <i class="fas fa-clock"></i>
                        <span id="sessionTime">Session: Not started</span>
                    </div>
                </div>

                <div class="input-container">
                    <div class="input-wrapper">
                        <textarea
                            id="messageInput"
                            class="message-input"
                            placeholder="Type your message here..."
                            disabled
                            rows="1"
                        ></textarea>
                    </div>
                    <button class="btn btn-primary" id="sendButton" disabled>
                        <i class="fas fa-paper-plane"></i>
                        Send
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        class SpiroSalesChat {
            constructor() {
                this.conversationStarted = false;
                this.phoneNumber = "254700000001"; // Default phone number
                this.sessionStartTime = null;

                this.initializeElements();
                this.bindEvents();
                this.updateWelcomeTime();
                this.startSessionTimer();
            }

            initializeElements() {
                this.chatMessages = document.getElementById('chatMessages');
                this.messageInput = document.getElementById('messageInput');
                this.sendButton = document.getElementById('sendButton');
                this.clearSessionButton = document.getElementById('clearSessionButton');
                this.statusText = document.getElementById('statusText');
                this.statusIndicator = document.getElementById('statusIndicator');
                this.sessionTime = document.getElementById('sessionTime');
                this.typingIndicator = document.getElementById('typingIndicator');
                this.sessionSelect = document.getElementById('sessionSelect');
                this.welcomeTime = document.getElementById('welcomeTime');
            }

            bindEvents() {
                this.sendButton?.addEventListener('click', () => this.sendMessage());
                this.clearSessionButton?.addEventListener('click', () => this.clearSession());
                this.sessionSelect?.addEventListener('change', (e) => this.changeSession(e.target.value));

                this.messageInput?.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });

                this.messageInput?.addEventListener('input', () => this.autoResizeTextarea());

                // Auto-load session on page load
                this.loadSession(this.phoneNumber);
            }

            updateWelcomeTime() {
                if (this.welcomeTime) {
                    this.welcomeTime.textContent = new Date().toLocaleTimeString();
                }
            }

            startSessionTimer() {
                setInterval(() => {
                    if (this.sessionStartTime) {
                        const elapsed = Math.floor((Date.now() - this.sessionStartTime) / 1000);
                        const minutes = Math.floor(elapsed / 60);
                        const seconds = elapsed % 60;
                        this.sessionTime.textContent = `Session: ${minutes}:${seconds.toString().padStart(2, '0')}`;
                    }
                }, 1000);
            }

            changeSession(phoneNumber) {
                this.phoneNumber = phoneNumber;
                this.conversationStarted = false;
                this.sessionStartTime = null;
                this.clearMessages();
                this.resetInterface();
                this.updateStatus('Loading session...', 'warning');
                this.loadSession(phoneNumber);
            }

            async loadSession(phoneNumber) {
                try {
                    // Try to start/continue conversation - this will load existing session or start new one
                    const response = await fetch('/api/lead/start', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            from: phoneNumber,
                            force_restart: false // Don't force restart to load existing session
                        })
                    });

                    const data = await response.json();

                    if (data.success) {
                        this.addMessage(data.message, false, data.message_type);
                        this.conversationStarted = true;
                        this.sessionStartTime = Date.now();
                        this.enableInput();
                        this.updateStatus(`Active - ${data.flow_name || 'Flow'}: ${data.current_step || 'In Progress'}`, 'active');
                    } else {
                        this.updateStatus('Error loading session', 'error');
                    }
                } catch (error) {
                    this.updateStatus('Connection error', 'error');
                    console.error('Error loading session:', error);
                }
            }

            async clearSession() {
                if (!confirm('Are you sure you want to clear this session? All conversation history will be lost.')) {
                    return;
                }

                this.updateStatus('Clearing session...', 'warning');
                this.disableInput();

                try {
                    const response = await fetch(`/api/lead/clear/${this.phoneNumber}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    });

                    const data = await response.json();

                    if (data.success) {
                        this.conversationStarted = false;
                        this.sessionStartTime = null;
                        this.clearMessages();
                        this.resetInterface();
                        this.updateStatus('Session cleared - Loading new session...', 'warning');

                        // Start a new conversation
                        setTimeout(() => {
                            this.loadSession(this.phoneNumber);
                        }, 500);
                    } else {
                        this.updateStatus('Error clearing session', 'error');
                        this.enableInput();
                    }
                } catch (error) {
                    this.updateStatus('Connection error', 'error');
                    this.enableInput();
                    console.error('Error clearing session:', error);
                }
            }

            clearMessages() {
                if (this.chatMessages) {
                    // Keep only the welcome message
                    const welcomeMessage = this.chatMessages.querySelector('.message.bot');
                    this.chatMessages.innerHTML = '';
                    if (welcomeMessage) {
                        this.chatMessages.appendChild(welcomeMessage);
                    }
                }
            }

            resetInterface() {
                this.disableInput();
                this.sessionTime.textContent = 'Session: Not started';
            }

            addMessage(message, isUser = false, messageType = 'text') {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${isUser ? 'user' : 'bot'}`;

                const avatarDiv = document.createElement('div');
                avatarDiv.className = 'message-avatar';
                avatarDiv.innerHTML = isUser ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';

                const contentDiv = document.createElement('div');
                contentDiv.className = 'message-content';
                contentDiv.textContent = message;

                const timeDiv = document.createElement('div');
                timeDiv.className = 'message-time';
                timeDiv.textContent = new Date().toLocaleTimeString();

                contentDiv.appendChild(timeDiv);
                messageDiv.appendChild(avatarDiv);
                messageDiv.appendChild(contentDiv);

                if (this.chatMessages) {
                    this.chatMessages.appendChild(messageDiv);
                    this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
                }
            }

            updateStatus(status, type = 'active') {
                if (this.statusText) {
                    this.statusText.textContent = status;
                }

                if (this.statusIndicator) {
                    this.statusIndicator.className = 'status-indicator';
                    if (type === 'error') {
                        this.statusIndicator.classList.add('error');
                    } else if (type === 'warning') {
                        this.statusIndicator.classList.add('warning');
                    }
                }
            }

            showTyping() {
                if (this.typingIndicator) {
                    this.typingIndicator.classList.add('show');
                }
            }

            hideTyping() {
                if (this.typingIndicator) {
                    this.typingIndicator.classList.remove('show');
                }
            }

            enableInput() {
                if (this.messageInput) {
                    this.messageInput.disabled = false;
                    this.messageInput.focus();
                }
                if (this.sendButton) {
                    this.sendButton.disabled = false;
                }
            }

            disableInput() {
                if (this.messageInput) {
                    this.messageInput.disabled = true;
                }
                if (this.sendButton) {
                    this.sendButton.disabled = true;
                }
            }



            autoResizeTextarea() {
                if (this.messageInput) {
                    this.messageInput.style.height = 'auto';
                    this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 120) + 'px';
                }
            }



            async sendMessage() {
                if (!this.messageInput || !this.conversationStarted) return;

                const message = this.messageInput.value.trim();
                if (!message) return;

                // Add user message to chat
                this.addMessage(message, true);
                this.messageInput.value = '';
                this.autoResizeTextarea();

                // Show typing indicator and update status
                this.showTyping();
                this.updateStatus('Processing...', 'warning');
                this.disableInput();

                try {
                    const response = await fetch('/api/lead/continue', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            from: this.phoneNumber,
                            message: message
                        })
                    });

                    const data = await response.json();

                    this.hideTyping();

                    if (data.success) {
                        this.addMessage(data.message, false, data.message_type);
                        this.updateStatus(`Active - ${data.flow_name || 'Flow'}: ${data.current_step || 'In Progress'}`, 'active');

                        // Check if conversation is completed
                        if (data.current_step === 'completed') {
                            this.updateStatus('Conversation completed - Thank you!', 'active');
                            this.disableInput();
                        }
                    } else {
                        this.addMessage('Error: ' + data.message, false);
                        this.updateStatus('Error - Please try again', 'error');
                    }
                } catch (error) {
                    this.hideTyping();
                    this.addMessage('Error connecting to server. Please check your connection.', false);
                    this.updateStatus('Connection error', 'error');
                    console.error('Error:', error);
                } finally {
                    if (this.conversationStarted) {
                        this.enableInput();
                    }
                }
            }
        }

        // Initialize chat interface when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            window.spiroChat = new SpiroSalesChat();
        });
    </script>
</body>
</html>
