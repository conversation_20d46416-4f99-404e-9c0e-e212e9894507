<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Log Viewer</title>
</head>
<body>
    <h1>WebSocket Log Viewer</h1>
    <pre id="log"></pre>

    <script>
        const logElement = document.getElementById('log');
        const ws = new WebSocket("ws://localhost:4002/ws");

        ws.onmessage = function(event) {
            logElement.textContent += event.data + "\n";
        };

        ws.onclose = function() {
            logElement.textContent += "WebSocket connection closed.\n";
        };
    </script>
</body>
</html>
