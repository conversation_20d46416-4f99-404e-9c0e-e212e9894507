from flask import Blueprint, render_template, request, jsonify, session
import os
import logging
import time
from app.services.agent_service import <PERSON><PERSON><PERSON><PERSON>, AgentState
from app.services.session_service import SessionManager
from app.services.function_service import FunctionCallingService

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# Create blueprint
main = Blueprint('main', __name__)
franchise = Blueprint('franchise', __name__)
whatsapp = Blueprint('whatsapp', __name__)

# Create a dictionary to store active agent managers by session ID
active_managers = {}

@main.route('/')
def index():
    """Render the main chat interface"""
    # Generate a unique session ID if not already set
    if 'session_id' not in session:
        session['session_id'] = os.urandom(16).hex()

    return render_template('franchise_chat.html')

# Helper function to extract phone number and message from different request formats
def extract_phone_and_message(data):
    """
    Extract phone number and message from either direct API call or WhatsApp webhook format
    """
    if data.get('entry') and data.get('object') == 'whatsapp_business_account':
        # WhatsApp webhook format
        try:
            # Extract from WhatsApp format
            entry = data.get('entry', [])[0]
            changes = entry.get('changes', [])[0]
            value = changes.get('value', {})
            messages = value.get('messages', [])[0]
            
            phone_number = messages.get('from')
            
            if messages.get('type') == 'text':
                user_message = messages.get('text', {}).get('body', '')
            else:
                user_message = ''
                
            return phone_number, user_message
        except (IndexError, KeyError) as e:
            logger.error(f"Error extracting data from WhatsApp webhook: {str(e)}")
            return None, None
    else:
        # Direct API call format
        phone_number = data.get('from')
        user_message = data.get('message', '')
        return phone_number, user_message

@whatsapp.route('/webhook', methods=['POST'])
def whatsapp_webhook():
    """Handle incoming WhatsApp webhook messages"""
    try:
        # Get the payload data
        data = request.json if request.is_json else {}

        # Log the payload
        logger.info(f"WHATSAPP WEBHOOK PAYLOAD: {data}")

        # Extract phone number and message from the request
        phone_number, user_message = extract_phone_and_message(data)

        if not phone_number:
            return jsonify({
                "success": False,
                "error": "No phone number provided or invalid webhook format",
                "message": "A valid WhatsApp webhook payload is required"
            }), 400

        # Get existing session or create new one
        existing_session = SessionManager.get_session_by_phone(phone_number)
        
        # Initialize state
        if existing_session:
            _, state = existing_session
            logger.info(f"Retrieved existing session for phone {phone_number}")
        else:
            # Create new state if none exists
            state = AgentState()
            logger.info(f"Created new state for phone {phone_number}")
            
            # For new users, we'll need to create an initial welcome message
            # The rest of the flow will use the same logic as continuing a conversation

        # Create agent manager
        agent_manager = AgentManager()

        # Process user message
        message, state_updates, should_progress = agent_manager.process_message(user_message, state)

        # Save the session
        SessionManager.create_or_update_session(phone_number, state)

        timestamp = str(int(time.time()))
        message_id = f"MSG_{timestamp}_{phone_number[-6:]}"

        # Return response in the combined format
        return jsonify({
            "success": True,
            "message": message,
            "message_type": "text",
            "current_agent": state.get("current_agent"),
            "current_node": state.get("current_node", "Conversation"),
            "should_progress": should_progress,
            "timestamp": timestamp,
            "whatsapp_from": phone_number,
            "whatsapp_message_id": message_id,
            "flow_type": state.get("flow_type"),
            "state_updates": [update.model_dump() for update in state_updates]
        }), 200

    except Exception as e:
        logger.error(f"Error processing WhatsApp webhook: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "Error processing WhatsApp webhook"
        }), 500

@franchise.route('/api/franchise/start', methods=['POST'])
def start_conversation():
    """Initialize a new conversation with the franchise agent"""
    try:
        # Get the payload data
        data = request.json if request.is_json else {}

        # Log the payload
        logger.info(f"START CONVERSATION PAYLOAD: {data}")

        # Extract phone number and message from the request
        phone_number, _ = extract_phone_and_message(data)
        
        # Get force_restart flag (only in direct API format)
        force_restart = data.get('force_restart', False)

        if not phone_number:
            return jsonify({
                "success": False,
                "error": "No phone number provided",
                "message": "A phone number is required to start the conversation"
            }), 400

        # Initialize state
        state = None

        # Try to get existing session if not forcing restart
        if not force_restart:
            existing_session = SessionManager.get_session_by_phone(phone_number)
            if existing_session:
                _, state = existing_session
                logger.info(f"Retrieved existing session for phone {phone_number}")

        # Create new state if none exists or forcing restart
        if not state:
            state = AgentState()
            logger.info(f"Created new state for phone {phone_number}")

        # Create agent manager
        agent_manager = AgentManager()

        # Process initial message (empty input)
        message, state_updates, should_progress = agent_manager.process_message("", state)

        # Save the session
        SessionManager.create_or_update_session(phone_number, state)

        timestamp = str(int(time.time()))
        message_id = f"MSG_START_{timestamp}_{phone_number[-6:]}"

        # Return response in the combined format
        return jsonify({
            "success": True,
            "message": message,
            "message_type": "text",
            "current_agent": state.get("current_agent"),
            "current_node": state.get("current_node", "Initiate Conversation"),
            "should_progress": should_progress,
            "timestamp": timestamp,
            "whatsapp_from": phone_number,
            "whatsapp_message_id": message_id,
            "flow_type": state.get("flow_type"),
            "state_updates": [update.model_dump() for update in state_updates]
        }), 200

    except Exception as e:
        logger.error(f"Error starting conversation: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "Sorry, I encountered an error starting our conversation."
        }), 500

@franchise.route('/api/franchise/continue', methods=['POST'])
def continue_conversation():
    """Continue an existing conversation with the franchise agent"""
    try:
        # Get the payload data
        data = request.json if request.is_json else {}

        # Log the payload
        logger.info(f"CONTINUE CONVERSATION PAYLOAD: {data}")

        # Extract phone number and message from the request
        phone_number, user_message = extract_phone_and_message(data)

        if not phone_number:
            return jsonify({
                "success": False,
                "error": "No phone number provided",
                "message": "A phone number is required to continue the conversation"
            }), 400

        # Get existing session
        existing_session = SessionManager.get_session_by_phone(phone_number)

        # Initialize state
        if existing_session:
            _, state = existing_session
            logger.info(f"Retrieved existing session for phone {phone_number}")
        else:
            # Create new state if none exists
            state = AgentState()
            logger.info(f"Created new state for phone {phone_number}")

        # Create agent manager
        agent_manager = AgentManager()

        # Process user message
        message, state_updates, should_progress = agent_manager.process_message(user_message, state)

        # Save the session
        SessionManager.create_or_update_session(phone_number, state)

        timestamp = str(int(time.time()))
        message_id = f"MSG_{timestamp}_{phone_number[-6:]}"

        # Return response in the combined format
        return jsonify({
            "success": True,
            "message": message,
            "message_type": "text",
            "current_agent": state.get("current_agent"),
            "current_node": state.get("current_node", "Conversation"),
            "should_progress": should_progress,
            "timestamp": timestamp,
            "whatsapp_from": phone_number,
            "whatsapp_message_id": message_id,
            "flow_type": state.get("flow_type"),
            "state_updates": [update.model_dump() for update in state_updates]
        }), 200

    except Exception as e:
        logger.error(f"Error continuing conversation: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "Sorry, I encountered an error processing your message."
        }), 500

@franchise.route('/api/franchise/clear/<phone_number>', methods=['POST'])
def clear_session(phone_number):
    """Clear a session by phone number"""
    try:
        success = SessionManager.clear_session(phone_number)

        if success:
            return jsonify({
                "success": True,
                "message": f"Session cleared for {phone_number}"
            }), 200
        else:
            return jsonify({
                "success": False,
                "message": f"No active session found for {phone_number}"
            }), 404

    except Exception as e:
        logger.error(f"Error clearing session: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "Sorry, I encountered an error clearing the session."
        }), 500

@franchise.route('/api/franchise/sessions', methods=['GET'])
def list_sessions():
    """List all active sessions"""
    try:
        sessions = SessionManager.list_active_sessions()

        return jsonify({
            "success": True,
            "sessions": sessions
        }), 200

    except Exception as e:
        logger.error(f"Error listing sessions: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "Sorry, I encountered an error listing the sessions."
        }), 500
