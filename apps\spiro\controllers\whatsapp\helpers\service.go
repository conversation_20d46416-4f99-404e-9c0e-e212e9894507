package helpers

import (
	whatsapp "libs/shared/db_connectors/model"
	// whatsapp_repo "code_develop_go_0.0/backend_core/internal/repository/whatsapp"
	cache "libs/shared/utils/cache"
	shared "libs/shared"
    env "apps/spiro/config"
)

type Service interface {
	ExitCurrentConversation(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact) string
}

type service struct {
	// contactRepository     whatsapp_repo.WhatsappContact
	// chatHistoryRepository whatsapp_repo.WhatsappChatHistory
	db shared.PostgresRepositoryFunctions
}

var serviceObj *service

func NewService() *service {
	if serviceObj != nil {
		return serviceObj
	}
	serviceObj = &service{
		// contactRepository:     whatsapp_repo.NewWhatsappContactRepository(),
		// chatHistoryRepository: whatsapp_repo.NewWhatsappChatHistoryRepository(),
	}

	return serviceObj
}

func (s *service) ExitCurrentConversation(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact) string {
	cache.Delete("LLM:" + account.WhatsappNumber + ":" + contact.WhatsappNumber)
	query := map[string]interface{}{
		"id": contact.ID,
	}
	contact.ChatHistoryForSummary = "cleared"

	contactCollectionName := shared.PostgresCollectionNames["WHATSAPP_CONTACT"]
	var contactMessageUpdateDataMap map[string]interface{}
    shared.JsonMarshaller(contact, &contactMessageUpdateDataMap)
	// s.contactRepository.Update(query, &contact)
	s.db.UpdateOne(env.GlobalEnv["POSTGRES_CREDENTIAL"], contactCollectionName, contactMessageUpdateDataMap, query)
	chatHistoryCollectionName := shared.PostgresCollectionNames["WHATSAPP_CHAT_HISTORY"]
	s.db.ClearChatHistory(env.GlobalEnv["POSTGRES_CREDENTIAL"], chatHistoryCollectionName,account.WhatsappNumber + ":" + contact.WhatsappNumber)

	return "The current conversation has ended. Please feel free to retype your query to continue."
}
