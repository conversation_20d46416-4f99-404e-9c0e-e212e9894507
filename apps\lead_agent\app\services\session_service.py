import logging
import json
import datetime
from typing import Optional, Dict, Any, List, Tu<PERSON>
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
import os

from app.models.session_model import Base, LeadSession
from app.services.agent_service import AgentState

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# Get database URL from environment or use SQLite as fallback
DATABASE_URL = os.environ.get('DATABASE_URL', 'sqlite:///lead_sessions.db')

# Create SQLAlchemy engine and session
engine = create_engine(DATABASE_URL)
Base.metadata.create_all(engine)  # Create tables if they don't exist
db_session = scoped_session(sessionmaker(autocommit=False, autoflush=False, bind=engine))

class SessionManager:
    """Manages persistent sessions for lead agent conversations"""
    
    @staticmethod
    def get_session_by_phone(phone_number: str) -> Optional[Tuple[LeadSession, AgentState]]:
        """
        Retrieve a session by phone number
        
        Args:
            phone_number: The phone number
            
        Returns:
            Tuple of (session object, agent state) or None if not found
        """
        try:
            session = db_session.query(LeadSession).filter_by(
                phone_number=phone_number,
                is_active=True
            ).first()
            
            if not session:
                logger.info(f"No active session found for phone {phone_number}")
                return None
                
            # Update last active timestamp
            session.last_active = datetime.datetime.utcnow()
            db_session.commit()
            
            # Deserialize the state using from_dict method
            state_data = json.loads(session.state_data) if session.state_data else {}
            state = AgentState.from_dict(state_data)
            
            # Log the state
            logger.info(f"Retrieved state for {phone_number}: {state.to_dict()}")
            logger.info(f"Retrieved sales_info for {phone_number}: {state.sales_info.to_dict()}")
            
            return session, state
            
        except Exception as e:
            logger.error(f"Error retrieving session for {phone_number}: {str(e)}")
            db_session.rollback()
            return None
    
    @staticmethod
    def create_or_update_session(phone_number: str, state: AgentState) -> bool:
        """
        Create a new session or update an existing one
        
        Args:
            phone_number: The phone number
            state: The agent state
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Check if session exists
            session = db_session.query(LeadSession).filter_by(
                phone_number=phone_number
            ).first()
            
            # Serialize state data
            state_data = json.dumps(state.to_dict())
            
            if not session:
                # Create new session
                session = LeadSession(
                    phone_number=phone_number,
                    state_data=state_data,
                    is_active=True
                )
                db_session.add(session)
                logger.info(f"Created new session for phone {phone_number}")
            else:
                # Update existing session
                session.state_data = state_data
                session.last_active = datetime.datetime.utcnow()
                session.is_active = True
                logger.info(f"Updated session for phone {phone_number}")
                
            db_session.commit()
            return True
            
        except Exception as e:
            logger.error(f"Error saving session for {phone_number}: {str(e)}")
            db_session.rollback()
            return False
    
    @staticmethod
    def clear_session(phone_number: str) -> bool:
        """
        Clear a session by phone number
        
        Args:
            phone_number: The phone number
            
        Returns:
            True if successful, False otherwise
        """
        try:
            session = db_session.query(LeadSession).filter_by(
                phone_number=phone_number
            ).first()
            
            if session:
                session.is_active = False
                db_session.commit()
                logger.info(f"Cleared session for phone {phone_number}")
                return True
            else:
                logger.info(f"No session found for phone {phone_number}")
                return False
                
        except Exception as e:
            logger.error(f"Error clearing session for {phone_number}: {str(e)}")
            db_session.rollback()
            return False
    
    @staticmethod
    def list_active_sessions() -> List[Dict[str, Any]]:
        """
        List all active sessions
        
        Returns:
            List of session dictionaries
        """
        try:
            sessions = db_session.query(LeadSession).filter_by(
                is_active=True
            ).all()
            
            return [session.to_dict() for session in sessions]
            
        except Exception as e:
            logger.error(f"Error listing active sessions: {str(e)}")
            return []
    
    @staticmethod
    def cleanup_expired_sessions(days: int = 30) -> int:
        """
        Clean up expired sessions
        
        Args:
            days: Number of days after which a session is considered expired
            
        Returns:
            Number of sessions cleaned up
        """
        try:
            expiry_date = datetime.datetime.utcnow() - datetime.timedelta(days=days)
            
            expired_sessions = db_session.query(LeadSession).filter(
                LeadSession.last_active < expiry_date,
                LeadSession.is_active == True
            ).all()
            
            for session in expired_sessions:
                session.is_active = False
            
            db_session.commit()
            
            logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")
            return len(expired_sessions)
            
        except Exception as e:
            logger.error(f"Error cleaning up expired sessions: {str(e)}")
            db_session.rollback()
            return 0
