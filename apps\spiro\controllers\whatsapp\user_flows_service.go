package whatsapp

import (
	"encoding/json"
	"fmt"
	"errors"
	"strings"

	whatsapp "libs/shared/db_connectors/model"
product_management "apps/spiro/controllers/whatsapp/users/product_management"
// flows "apps/spiro/controllers/whatsapp/flows"
helpers "libs/shared/utils/helpers"
	// cache "libs/shared/utils/cache"
)

type UserFlowService interface {
	UserProcessWhatsappFlow(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact, message MessageFromWhatsapp) ([]RasaResponse, error)
	EV_MODELS(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact) ([]RasaResponse, error)
	OnboardingFlow(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact) ([]RasaResponse, error)

}

type userFlowService struct {
	// webhook Webhook
	// sellerHelpers SellerHelpers
}

var sellerFlowServiceObj *userFlowService

func NewUserFlowService() *userFlowService {
	if sellerFlowServiceObj != nil {
		return sellerFlowServiceObj
	}
	sellerFlowServiceObj = &userFlowService{
		// webhook: NewWebhook(),
		// sellerHelpers: NewSellerHelpers(),
	}
	return sellerFlowServiceObj
}

func (s *userFlowService) UserProcessWhatsappFlow(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact, message MessageFromWhatsapp) ([]RasaResponse, error) {

	var flowResponse map[string]interface{}
	json.Unmarshal([]byte(message.Interactive.NfmReply.ResponseJson), &flowResponse)

	if flowResponse["flow_token"] != nil {

		if strings.Contains(flowResponse["flow_token"].(string), "ProductManagement") {
			if(flowResponse["product_name"]!= nil && flowResponse["product_name"].(string)!="") {
				return []RasaResponse{{
					RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
					// Text:        "Product_Details: " + product_management.ProductsResponse{flowResponse["product_name"]},
					Text:        "Product_Details: " + flowResponse["product_name"].(string),
				}}, nil
			}else{
				return []RasaResponse{{
					RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
					Text:        "Exited from Product Management",
				}}, nil
			}
			
			// return s.ProductDetails(account, contact, product_management.ProductsResponse{ProductId: flowResponse["product_id"].(string)})
			
		}

		if strings.Contains(flowResponse["flow_token"].(string), "FeadAcquisitionOnboarding") {

			return []RasaResponse{{
				RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
				Text:        "You've onboarded successfully!",
			}}, nil
		}

	}

	return []RasaResponse{{
		RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
		Text:        "I apologize, but I'm having trouble understanding your request. Could you please rephrase or provide more information?",
	}}, nil
}

func (s *userFlowService) ProductDetails(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact, response product_management.ProductsResponse) ([]RasaResponse, error) {
	fmt.Println("ProductDetails--------- ")

	responseInterface, err := helpers.UrlRequest(
		"POST",
		account.MiddlewareUrl+"/products/view",
		map[string]interface{}{
			"phone_number": contact.WhatsappNumber,
			"product_id":   response.ProductId,
		},
		map[string]string{"Authorization": "Bearer " + account.MiddlewareToken},
	)
	if err != nil {
		return nil, err
	}

	var middlewareResponse product_management.SellerMiddlewareProductViewResponse
	helpers.JsonMarshaller(responseInterface, &middlewareResponse)
	if !middlewareResponse.Success {
		return nil, errors.New("error from middleware")
	}

    return []RasaResponse{{
        RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
        Text:        "Product_Details: " + middlewareResponse.Data.ProductName,
    }}, nil
    

}


func (s *userFlowService) EV_MODELS(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact) ([]RasaResponse, error) {
    fmt.Println("Flow MessageResponseProcessing")
	flowFormat := FlowFormat{
		Type: "interactive",
		Interactive: &FlowInteractive{
			Type: "flow",
			Body: &InteractiveBody{
				Text: "To access Spiro's catalogue, please click the option below", 
			},
			Action: &FlowAction{
				Name: "flow",
				Parameters: &FlowParameters{
					Mode:               "published",
					FlowMessageVersion: "3",
					FlowToken:          fmt.Sprintf("%v:%v:ProductManagement", account.WhatsappNumber, contact.WhatsappNumber),
					FlowId:             "****************",
					FlowCta:            "Spiro's EV Catalogue",
					FlowAction:         "data_exchange",
				},
			},
		},
	}

	rasaResponses := []RasaResponse{{
		RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
		Flow:        &flowFormat,
	}}

	return rasaResponses, nil
}

func (s *userFlowService) OnboardingFlow(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact) ([]RasaResponse, error) {

	flowFormat := FlowFormat{
		Type: "interactive",
		Interactive: &FlowInteractive{
			Type: "flow",
			Body: &InteractiveBody{
				Text: "Let's get you onboarded quickly!",
			},
			Action: &FlowAction{
				Name: "flow",
				Parameters: &FlowParameters{
					Mode:               "published",
					FlowMessageVersion: "3",
					FlowToken:          fmt.Sprintf("%v:%v:FeadAcquisitionOnboarding", account.WhatsappNumber, contact.WhatsappNumber),
					FlowId:             "***************",
					FlowCta:            "Onboard",
					FlowAction:         "data_exchange",
				},
			},
		},
	}

	rasaResponses := []RasaResponse{{
		RecipientId: account.WhatsappNumber + ":" + contact.WhatsappNumber,
		Flow:        &flowFormat,
	}}

	return rasaResponses, nil
}