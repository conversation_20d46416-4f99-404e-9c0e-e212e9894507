package users

import (
	// "encoding/json"
	"errors"
	"fmt"
	"strings"

	// "code_develop_go_0.0/backend_core/controllers/whatsapp/whatsapp/flows"
	product_management "apps/spiro/controllers/whatsapp/users/product_management"
	flows "apps/spiro/controllers/whatsapp/flows"

	whatsapp "libs/shared/db_connectors/model"
		whatsapp_repository "libs/shared/db_connectors/repository/whatsapp"
	// whatsapp_repo "code_develop_go_0.0/backend_core/internal/repository/whatsapp"
	// "code_develop_go_0.0/backend_core/pkg/util/cache"
	
	// helpers "libs/shared/utils/helpers"
)

type UserFlowsService interface {
	UserScreen(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact, decryptedData flows.DataChannelDecrypted) (flows.DataChannelResponse, error)
	UserImageData(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact, ImageData string) (error)
}

type userFlowsService struct {
	productManagement     product_management.Service
	contact_Repository             whatsapp_repository.WhatsappContact
}

var userFlowsServiceObj *userFlowsService

func NewUserFlowsService() *userFlowsService {
	if userFlowsServiceObj != nil {
		return userFlowsServiceObj
	}
	userFlowsServiceObj = &userFlowsService{
		
		productManagement:     product_management.NewService(),
		contact_Repository:             whatsapp_repository.NewWhatsappContactRepository(),
		
	}
	return userFlowsServiceObj
}

func (s *userFlowsService) UserScreen(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact, decryptedData flows.DataChannelDecrypted) (flows.DataChannelResponse, error) {

	flowToken := strings.Split(decryptedData.FlowToken, ":")
	flow := flowToken[2]

	fmt.Println("###### Seller decryptedData ##########", decryptedData)



	if flow == "ProductManagement" {
		return s.productManagement.Route(account, contact, decryptedData)
	}

	if flow == "FeadAcquisitionOnboarding" {
		if decryptedData.Action == "INIT" {
			return s.UserScreenOnboardingInit(account, contact, decryptedData)
		}
		if decryptedData.Screen == "BASIC_DETAILS" {
			return s.UserScreenBacicDetails(account, contact, decryptedData)
		}
	}

	return flows.DataChannelResponse{}, errors.New("screen not found")
}
