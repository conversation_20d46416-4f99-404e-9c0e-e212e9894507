package whatsapp

import "gorm.io/datatypes"

/*

	ONLY USE THESE FORMAT STRUCTS
	- TextFormat
	- MediaFormat
	- ButtonFormat
	- ListFormat
	- AddressFormat
	- FlowFormat
	- PaymentFormat
	- PaymentStatusFormat

*/

type TextFormat struct {
	Type string `json:"type,omitempty"`
	Text *Text  `json:"text,omitempty"`
}

type Text struct {
	PreviewURL bool   `json:"preview_url,omitempty"`
	Body       string `json:"body,omitempty"`
}

type TemplateFormat struct {
	Type string `json:"type,omitempty"`
	Template *Template `json:"template,omitempty"`
	
}
type Template struct {
    Name      string                    `json:"name,omitempty"`
	Language  *TemplateLanguage      `json:"language,omitempty"`
	Components []datatypes.JSON       `json:"components"`
}

type TemplateLanguage struct {
	Code       string `json:"code,omitempty"`
}

type MediaFormat struct {
	Type     string     `json:"type,omitempty"`
	Image    *MediaLink `json:"image,omitempty"`
	Video    *MediaLink `json:"video,omitempty"`
	Document *MediaLink `json:"document,omitempty"`
}

type MediaLink struct {
	Link     string `json:"link,omitempty"`
	Caption  string `json:"caption,omitempty"`
	Filename string `json:"filename,omitempty"` //only for document
}

type ButtonFormat struct {
	Type        string             `json:"type,omitempty"`
	Interactive *ButtonInteractive `json:"interactive,omitempty"`
}

type ButtonInteractive struct {
	Type   string             `json:"type,omitempty"`
	Body   *InteractiveBody   `json:"body,omitempty"`
	Action *ButtonAction      `json:"action,omitempty"`
	Header *InteractiveHeader `json:"header,omitempty"`
}

type InteractiveBody struct {
	Text string `json:"text,omitempty"`
}

type InteractiveHeader struct {
	Image    *InteractiveLink `json:"image,omitempty"`
	Video    *InteractiveLink `json:"video,omitempty"`
	Document *InteractiveLink `json:"document,omitempty"`
	Text     string           `json:"text,omitempty"`
	Type     string           `json:"type,omitempty"`
}

type InteractiveLink struct {
	Link     string `json:"link,omitempty"`
	Filename string `json:"filename,omitempty"` //only for document
}

type ButtonAction struct {
	Buttons []ButtonButtons `json:"buttons,omitempty"`
}

type ButtonButtons struct {
	Type  string       `json:"type,omitempty"`
	Reply *ButtonReply `json:"reply,omitempty"`
}

type ButtonReply struct {
	ID    string `json:"id,omitempty"`
	Title string `json:"title,omitempty"`
}

type ListFormat struct {
	Type        string           `json:"type,omitempty"`
	Interactive *ListInteractive `json:"interactive,omitempty"`
}

type ListInteractive struct {
	Type   string             `json:"type,omitempty"`
	Body   *InteractiveBody   `json:"body,omitempty"`
	Footer *InteractiveFooter `json:"footer,omitempty"`
	Action *ListAction        `json:"action,omitempty"`
	Header *InteractiveHeader `json:"header,omitempty"`
}

type InteractiveFooter struct {
	Text string `json:"text,omitempty"`
}

type ListAction struct {
	Button   string        `json:"button,omitempty"`
	Sections []ListSection `json:"sections,omitempty"`
}

type ListSection struct {
	Rows []ListRow `json:"rows,omitempty"`
}

type ListRow struct {
	ID    string `json:"id,omitempty"`
	Title string `json:"title,omitempty"`
}

type AddressFormat struct {
	Type        string              `json:"type,omitempty"`
	Interactive *AddressInteractive `json:"interactive,omitempty"`
}

type AddressInteractive struct {
	Type   string           `json:"type,omitempty"`
	Body   *InteractiveBody `json:"body,omitempty"`
	Action *AddressAction   `json:"action,omitempty"`
}

type AddressAction struct {
	Name       string           `json:"name,omitempty"`
	Parameters AddressParameter `json:"parameters,omitempty"`
}

type AddressParameter struct {
	Country string `json:"country,omitempty"`
}

//--------------------------------WHATSAPP FLOWs---------------------------------------
type FlowFormat struct {
	Type        string           `json:"type,omitempty"`
	Interactive *FlowInteractive `json:"interactive,omitempty"`
}

type FlowInteractive struct {
	Type   string             `json:"type,omitempty"`
	Header *InteractiveHeader `json:"header,omitempty"`
	Body   *InteractiveBody   `json:"body,omitempty"`
	Footer *InteractiveFooter `json:"footer,omitempty"`
	Action *FlowAction        `json:"action,omitempty"`
}

type FlowAction struct {
	Name       string          `json:"name,omitempty"`
	Parameters *FlowParameters `json:"parameters,omitempty"`
}

type FlowParameters struct {
	Mode               string             `json:"mode,omitempty"`
	FlowMessageVersion string             `json:"flow_message_version,omitempty"`
	FlowToken          string             `json:"flow_token,omitempty"`
	FlowId             string             `json:"flow_id,omitempty"`
	FlowCta            string             `json:"flow_cta,omitempty"`
	FlowAction         string             `json:"flow_action,omitempty"`
	FlowActionPayload  *FlowActionPayload `json:"flow_action_payload,omitempty"`
}

type FlowActionPayload struct {
	Screen string      `json:"screen,omitempty"`
	Data   interface{} `json:"data,omitempty"`
}

//-----------------------------WHATSAPP PAYMENTS------------------------------
type PaymentFormat struct {
	Type        string             `json:"type,omitempty"`
	Interactive PaymentInteractive `json:"interactive,omitempty"`
}

type PaymentInteractive struct {
	Type   string         `json:"type,omitempty"`
	Header PaymentHeader  `json:"header,omitempty"`
	Body   PaymentBody    `json:"body,omitempty"`
	Footer PaymentFooter  `json:"footer,omitempty"`
	Action PayamentAction `json:"action,omitempty"`
}

type PaymentHeader struct {
	Type  string       `json:"type,omitempty"`
	Image PaymentImage `json:"image,omitempty"`
}

type PaymentImage struct {
	Link string `json:"link,omitempty"`
}

type PaymentBody struct {
	Text string `json:"text,omitempty"`
}

type PaymentFooter struct {
	Text string `json:"text,omitempty"`
}

type PayamentAction struct {
	Name       string            `json:"name,omitempty"`
	Parameters PaymentParameters `json:"parameters,omitempty"`
}

type PaymentParameters struct {
	ReferenceId     string               `json:"reference_id,omitempty"`
	Type            string               `json:"type,omitempty"`
	Beneficiaries   []PaymentBeneficiary `json:"beneficiaries,omitempty"`
	PaymentSettings []PaymentSetting     `json:"payment_settings,omitempty"`
	Currency        string               `json:"currency,omitempty"`
	TotalAmount     PaymentTotalAmount   `json:"total_amount,omitempty"`
	Order           PaymentOrder         `json:"order,omitempty"`
}

type PaymentBeneficiary struct {
	Name         string `json:"name,omitempty"`
	AddressLine1 string `json:"address_line1,omitempty"`
	AddressLine2 string `json:"address_line2,omitempty"`
	City         string `json:"city,omitempty"`
	State        string `json:"state,omitempty"`
	Country      string `json:"country,omitempty"`
	PostalCode   string `json:"postal_code,omitempty"`
}

type PaymentSetting struct {
	Type           string         `json:"type,omitempty"`
	PaymentGateway PaymentGateway `json:"payment_gateway,omitempty"`
}

type PaymentGateway struct {
	Type              string          `json:"type,omitempty"`
	ConfigurationName string          `json:"configuration_name,omitempty"`
	Razorpay          PaymentRazorpay `json:"razorpay,omitempty"`
}

type PaymentRazorpay struct {
	Receipt string                 `json:"receipt,omitempty"`
	Notes   map[string]interface{} `json:"notes,omitempty"`
}

type PaymentTotalAmount struct {
	Value  int `json:"value"`
	Offset int `json:"offset"`
}

type PaymentOrder struct {
	Status     string             `json:"status,omitempty"`
	Expiration PaymentExpiration  `json:"expiration,omitempty"`
	Items      []PaymentItem      `json:"items,omitempty"`
	Subtotal   PaymentValueOffset `json:"subtotal,omitempty"`
	Tax        PaymentValueOffset `json:"tax,omitempty"`
	Shipping   PaymentValueOffset `json:"shipping,omitempty"`
	Discount   PaymentValueOffset `json:"discount,omitempty"`
}

type PaymentExpiration struct {
	Timestamp   string `json:"timestamp,omitempty"`
	Description string `json:"description,omitempty"`
}

type PaymentValueOffset struct {
	Value       int    `json:"value"`
	Offset      int    `json:"offset"`
	Description string `json:"description,omitempty"`
}

type PaymentItem struct {
	RetailerID      string             `json:"retailer_id,omitempty"`
	Name            string             `json:"name,omitempty"`
	Amount          PaymentValueOffset `json:"amount,omitempty"`
	Quantity        int                `json:"quantity,omitempty"`
	SaleAmount      PaymentValueOffset `json:"sale_amount,omitempty"`
	CountryOfOrigin string             `json:"country_of_origin,omitempty"`
	ImporterName    string             `json:"importer_name,omitempty"`
	ImporterAddress PaymentAddress     `json:"importer_address,omitempty"`
}

type PaymentAddress struct {
	AddressLine1 string `json:"address_line1,omitempty"`
	AddressLine2 string `json:"address_line2,omitempty"`
	City         string `json:"city,omitempty"`
	PostalCode   string `json:"postal_code,omitempty"`
	CountryCode  string `json:"country_code,omitempty"`
}

//-------------------------PAYMENT STATUS-------------------------
type PaymentStatusFormat struct {
	Type        string                   `json:"type,omitempty"`
	Interactive PaymentStatusInteractive `json:"interactive,omitempty"`
}

type PaymentStatusInteractive struct {
	Type   string              `json:"type,omitempty"`
	Body   PaymentBody         `json:"body"`
	Action PaymentStatusAction `json:"action"`
}

type PaymentStatusAction struct {
	Name       string                  `json:"name"`
	Parameters PaymentStatusParameters `json:"parameters"`
}

type PaymentStatusParameters struct {
	ReferenceId string             `json:"reference_id"`
	Order       PaymentStatusOrder `json:"order"`
}

type PaymentStatusOrder struct {
	Status      string `json:"status"`
	Description string `json:"description"`
}

type Transcription struct {
	DetectedText []struct {
		Transcript           string  `json:"transcript"`
		Confidence           float64 `json:"confidence"`
		DetectedLanguageCode string  `json:"detected_language_code"`
	} `json:"detected_text"`
}
