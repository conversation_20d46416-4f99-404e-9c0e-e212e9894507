package onboarding

import (
	"errors"
	"fmt"
	// "strconv"
	// "time"

	// "code_develop_go_0.0/backend_core/pkg/util/cache"
	 helpers "libs/shared/utils/helpers"
)

var scheme = "https"
var ONDC_HOST = "preprod.ondc.adya.ai"

type Service interface {
	
	GetUserDetails(data LoginRequest) (UserDetailApiResponse, error)
	UpdateUserDetails(data UserDetailApiRequest) error

}

type service struct {
}

var sellerService *service

func NewService() *service {
	if sellerService != nil {
		return sellerService
	}
	sellerService = &service{}
	return sellerService
}

var user_details = UserDetailApiResponse{
	Id:                                1,
	Mobile:                            "2547********",
	Salutation:                        "Mr",
	First_Name:                        "<PERSON>",
	Last_Name:                         "Doe",
	Identification_Document_Available:  "Yes",
	Gender:                            "Male",
	Country:                           "Kenya",
	State:                             "Nairobi",
	City:                              "Nairobi",
	SMS_Language:                      "English",
	Lead_Source:                       "Website",
	Enquiry_Rating:                    5,
	Rating_Reason:                     "High Value Customer",
	Sacco_Name:                        "ABC Sacco",
	Building:                          "Landmark Plaza",
	Street:                            "Main Street",
	Zip_Code:                          "00100",
	Latitude:                          "-1.2921",
	Longitude:                         "36.8219",
	Verbal_Language:                   "English",
	Birth_Date:                        "1990-01-01",
	National_ID:                       "********",
	KCB_Account_No:                    "**********",
	Kinga_Reference_ID:                "KNG123456",
	KRA_PIN:                           "A0********Z",
}

func (s *service) GetUserDetails(data LoginRequest) (UserDetailApiResponse, error) {

	// user, err := GetUser(data.PhoneNumber)
	// if err != nil {
	// 	return UserDetails{}, err
	// }

	// bodyInterface, err := helpers.MakeRequest(helpers.Request{
	// 	Method: "GET",
	// 	Scheme: "https",
	// 	Host:   ONDC_HOST,
	// 	Path:   fmt.Sprintf("/api/v1/auth/get/%v", user.ID),
	// 	Header: map[string]string{
	// 		"Content-Type":  "application/json",
	// 		"Authorization": "Bearer " + user.JWTToken,
	// 	},
	// })
	// if err != nil {
	// 	return UserDetails{}, err
	// }

	// helpers.PrettyPrint("GetUserDetails", bodyInterface)

	var body UserDetailApiResponse
	helpers.JsonMarshaller(user_details, &body)
	// if !body.Meta.Status {
	// 	return UserDetails{}, errors.New("error from api")
	// }

	// helpers.PrettyPrint("new data==>", body)

	response :=body

	return response, nil
}


func (s *service) UpdateUserDetails(data UserDetailApiRequest) error {

	// user, err := GetUser(data.PhoneNumber)
	// if err != nil {
	// 	return err
	// }

	payload := UserDetailApiRequest{
		Mobile:data.Mobile,
		Salutation:data.Salutation,
		First_Name:data.First_Name,
		Last_Name:data.Last_Name,
		Identification_Document_Available:"No",
		Gender:data.Gender,
		Country:data.Country,
		State:data.State,
		City:data.City,
		SMS_Language:"English",
		Lead_Source:data.Lead_Source,
		Enquiry_Rating:data.Enquiry_Rating,
		Rating_Reason:data.Rating_Reason,
		Sacco_Name:data.Sacco_Name,
		Building:data.Building,
		Street:data.Street,
		Zip_Code:data.Zip_Code,
		Latitude:data.Latitude,
		Longitude:data.Longitude,
		Verbal_Language:"English",
		Birth_Date:data.Birth_Date,
		National_ID:data.National_ID,
		KCB_Account_No:data.KCB_Account_No,
		Kinga_Reference_ID:data.Kinga_Reference_ID,
		KRA_PIN:data.KRA_PIN,

		//default values
		Customer_Type: "B2C",
		Entity_Type: "Boda Boda Rider",


	}

	helpers.PrettyPrint("UpdateUserDetails Request", payload)

	bodyInterface, err := helpers.MakeRequest(helpers.Request{
		Method: "POST",
		Scheme: "https",
		Host:   ONDC_HOST,
		Path:   fmt.Sprintf("/api/v1/auth/%v/update", "user.ID"),
		Header: map[string]string{
			"Content-Type":  "application/json",
			"Authorization": "Bearer " + "",
		},
		Body: payload,
	})
	if err != nil {
		return err
	}

	helpers.PrettyPrint("UpdateUserDetails Response", bodyInterface)

	var body Response
	helpers.JsonMarshaller(bodyInterface, &body)
	if !body.Meta.Status {
		return errors.New("error from api")
	}

	return nil
}

