package whatsapp

import (
	env "apps/spiro/config"
	"encoding/json"
	"errors"
	"fmt"
	shared "libs/shared"
	"libs/shared/db_connectors/model"
	whatsapp "libs/shared/db_connectors/model"
	whatsapp_repository "libs/shared/db_connectors/repository/whatsapp"
	helpers "libs/shared/utils/helpers"
	// "os"
	"strings"
	"time"
	pagination "libs/shared/utils/pagination"

	"github.com/xuri/excelize/v2"
	"gorm.io/datatypes"
)

// "fmt"
// whatsapp "libs/shared/db_connectors/model"

type Service interface {
	Webhook(data WebhookRequest) error
	SaveWebhookData(requestBody interface{}) error
	VerifyWebhook(data VerifyWebhookRequest) error
	IntialTemplate(data IntialTemplateRequest) error
	GetMessages(data MessageRequest) ([]whatsapp.WhatsappMessage, error)
	ListMessages(metaData shared.ApiMetaData, query map[string]interface{}, sortings interface{}, perPage int, pageNo int) (map[string]interface{}, error)
    ListNationalIds(metaData shared.ApiMetaData, query map[string]interface{}, sortings interface{}, perPage int, pageNo int) (map[string]interface{}, error)
}

type service struct {
	db      shared.PostgresRepositoryFunctions
	webhook Webhook
	accountRepository whatsapp_repository.WhatsappRepository[whatsapp.WhatsappAccount]
	contactRepository whatsapp_repository.WhatsappRepository[whatsapp.WhatsappContact]
	contact_Repository             whatsapp_repository.WhatsappContact
	messageRepository whatsapp_repository.WhatsappRepository[whatsapp.WhatsappMessage]
}

var newServiceObj *service //singleton object

// singleton function
func NewService() *service {
	if newServiceObj != nil {
		return newServiceObj
	}
	
	newServiceObj = &service{
		db:      shared.PostgresRepository(),
		webhook: NewWebhook(),
		accountRepository:  whatsapp_repository.NewRepository(whatsapp.WhatsappAccount{}),
		contactRepository: whatsapp_repository.NewRepository(whatsapp.WhatsappContact{}),
		contact_Repository:             whatsapp_repository.NewWhatsappContactRepository(),
		messageRepository:  whatsapp_repository.NewRepository(whatsapp.WhatsappMessage{}),
	}
	return newServiceObj
}

func (s *service) Webhook(data WebhookRequest) error {

	return s.webhook.Webhook(data)
}

func (s *service) SaveWebhookData(requestBody interface{}) error {

	data := model.WhatsappWebhook{}
	data.Data, _ = json.Marshal(requestBody)
	data.CompanyId=1;
	createdByID := uint(1)
	data.CreatedByID = &createdByID
	fmt.Println("data", data)
	webhookCollection := shared.PostgresCollectionNames["WHATSAPP_WEBHOOK"]
	var webhookDataMap map[string]interface{}
	shared.JsonMarshaller(data, &webhookDataMap)
	fmt.Println("webhookDataMap", webhookDataMap)
	// err := s.webhookRepository.Save(&data)
	_, err := s.db.CreateOne(env.GlobalEnv["POSTGRES_CREDENTIAL"], webhookCollection, webhookDataMap)
	if err != nil {
		return err
	}

	return nil
}

func (s *service) VerifyWebhook(data VerifyWebhookRequest) error {
	if data.Mode != "subscribe" || data.VerifyToken != "adya" {
		return errors.New("invalid request")
	}
	return nil
}

func (s *service) GetMessages(data MessageRequest) ([]whatsapp.WhatsappMessage, error) {
	account, _ := s.accountRepository.FindOne(env.GlobalEnv["POSTGRES_CREDENTIAL"], map[string]interface{}{
		"whatsapp_number": data.WhatsappAccountNumber,
	})
	

	contact, _ := s.contact_Repository.FindOne(env.GlobalEnv["POSTGRES_CREDENTIAL"], map[string]interface{}{
		"whatsapp_number":     data.ContactNumber,
		"whatsapp_account_id": account.ID,
	})

	messages, _ := s.messageRepository.FindAll(env.GlobalEnv["POSTGRES_CREDENTIAL"], map[string]interface{}{
        "whatsapp_contact_id": contact.ID,
    }, &pagination.Paginatevalue{
        Page_no:    1,
        Per_page: -1,
    })


	return messages,nil
}
func (s *service) ListMessages(metaData shared.ApiMetaData, query map[string]interface{}, sortings interface{}, perPage int, pageNo int) (map[string]interface{}, error) {
    // Get account
    account, err := s.accountRepository.FindOne(env.GlobalEnv["POSTGRES_CREDENTIAL"], map[string]interface{}{
        "whatsapp_number": query["whatsapp_account_number"],
    })
    if err != nil {
        return nil, err
    }

    // Get contacts with pagination
    contacts, err := s.contactRepository.FindAllWithPagination(env.GlobalEnv["POSTGRES_CREDENTIAL"], map[string]interface{}{
        "whatsapp_account_id": account.ID,
    }, &pagination.Paginatevalue{
        Page_no:  pageNo,
        Per_page: perPage,
    })
    if err != nil {
        return nil, err
    }

    // Extract contact IDs for bulk message fetch
    var contactResp []ContactResponse
    if err := shared.JsonMarshaller(contacts["data"], &contactResp); err != nil {
        return nil, err
    }

    contactIDs := make([]uint, len(contactResp))
    for i, contact := range contactResp {
        contactIDs[i] = contact.ID
    }

    // Fetch all messages for all contacts in one query
    messages, err := s.messageRepository.FindAll(env.GlobalEnv["POSTGRES_CREDENTIAL"], map[string]interface{}{
        "whatsapp_contact_id": contactIDs,
    }, &pagination.Paginatevalue{
        Page_no:  1,
        Per_page: -1,
    })
    if err != nil {
        return nil, err
    }

    // Group messages by contact ID
    messagesByContact := make(map[uint][]MessageResponses)
    var allMessages []MessageResponses
    if err := shared.JsonMarshaller(messages, &allMessages); err != nil {
        return nil, err
    }
    for _, msg := range allMessages {
        messagesByContact[msg.WhatsappContactId] = append(messagesByContact[msg.WhatsappContactId], msg)
    }

    // Build final response
    final_response := make([]map[string]interface{}, 0, len(contactResp))
    for _, contact := range contactResp {
        final_response = append(final_response, map[string]interface{}{
            "id":                      contact.ID,
            "whatsapp_account_number": query["whatsapp_account_number"],
            "whatsapp_contact_number": contact.WhatsappNumber,
            "messages":                messagesByContact[contact.ID],
        })
    }

    return map[string]interface{}{
        "pagination": contacts["pagination"],
        "data":      final_response,
    }, nil
}
func (s *service) ListNationalIds(metaData shared.ApiMetaData, query map[string]interface{}, sortings interface{}, perPage int, pageNo int) (map[string]interface{}, error) {
    // Get account
    account, err := s.accountRepository.FindOne(env.GlobalEnv["POSTGRES_CREDENTIAL"], map[string]interface{}{
        "whatsapp_number": query["whatsapp_account_number"],
    })
    if err != nil {
        return nil,errors.New("Account Number Not Found")
    }

    contacts, err := s.contactRepository.FindAllWithPagination(env.GlobalEnv["POSTGRES_CREDENTIAL"], map[string]interface{}{
        "whatsapp_account_id": account.ID,
       "__raw__": "national_id <> ''",
    }, &pagination.Paginatevalue{
        Page_no:  pageNo,
        Per_page: perPage,
    })
    if err != nil {
        return nil, err
    }

    // Extract contact IDs for bulk message fetch
    var contactResp []NationalIdsResponse
    if err := shared.JsonMarshaller(contacts["data"], &contactResp); err != nil {
        return nil, err
    }

    return map[string]interface{}{
        "pagination": contacts["pagination"],
        "data":      contactResp,
    }, nil
}
func (s *service) IntialTemplate(data IntialTemplateRequest) error {
    // Open Excel file
	fmt.Println("entered template service")
	// currentDir, err := os.Getwd()
	// keyPath := currentDir + "/controllers/whatsapp/28.04.25.xlsx"
	keyPath := data.FilePath
    f, err := excelize.OpenFile(keyPath)
    if err != nil {
		fmt.Println("Error opening file:", err)
        return err
    }
    defer f.Close()

    // Get all rows from the first sheet
    rows, err := f.GetRows(f.GetSheetName(0))
    if err != nil {
        return err
    }
    fmt.Printf("Total rows in excel: %d\n", len(rows))
	if(len(rows) < 2){
		return errors.New("no data found in file")
	}
    // Process each row (skip header row)
    batchSize := 100
        totalRows := len(rows)
        
        for batchStart := 0; batchStart < totalRows; batchStart += batchSize {
            batchEnd := batchStart + batchSize
            if batchEnd > totalRows {
                batchEnd = totalRows
            }
            
            fmt.Printf("Processing batch %d to %d of %d records\n", batchStart, batchEnd-1, totalRows)
            
            for i := batchStart; i < batchEnd; i++ {
                row := rows[i]
                if i == 0 || len(row) < 4 { // Skip header and invalid rows
                    fmt.Println("Invalid Data", row)
                    continue
                }
        
                account, err := s.accountRepository.FindOne(env.GlobalEnv["POSTGRES_CREDENTIAL"], map[string]interface{}{
                    "whatsapp_number": data.WhatsappAccountNumber,
                })
                if err != nil {
                    return err
                }
        
                mobileNumber := strings.TrimSpace(strings.ReplaceAll(strings.ReplaceAll(row[3], "+", ""), " ", ""))
                firstName := strings.TrimSpace(row[2])
                lastName := strings.TrimSpace(row[1])
                
                fullName := strings.TrimSpace(firstName + " " + lastName)
                fmt.Printf("Mobile: %s, Name: %s\n", mobileNumber, fullName)
                
                formattedResponses := []interface{}{
                    TemplateFormat{
                        Type: "template",
                        Template: &Template{
                            Name: "initial_message",
                            Components: []datatypes.JSON{},
                            Language: &TemplateLanguage{
                                Code: "en",
                            },
                        },
                    },
                }
                
                err = s.SendToWhatsapp(account, mobileNumber, formattedResponses)
                if err != nil {
                    fmt.Printf("Error processing record %d: %v\n", i, err)
                    continue
                }
            }
            
            fmt.Printf("Completed batch %d to %d. Waiting before next batch...\n", batchStart, batchEnd-1)
            time.Sleep(5 * time.Second) // Wait between batches
        }

    return nil
}


func (s *service) SendToWhatsapp(account whatsapp.WhatsappAccount,contactNumber string,  responses []interface{}) error {
	fmt.Println("Checkpoint : SendToWhatsapp")
		
		request := helpers.Request{
			Method: "POST",
			Scheme: "https",
			Path:   "/v18.0/" + account.PhoneId + "/messages",
			Header: map[string]string{
				"Content-Type":  "application/json",
				"Authorization": "Bearer " + account.WhatsappToken,
			},
		}
		Host := ""
		if account.BusinessAccountName == "Spiro" {
			Host = "crmapi.com.bot"
			request.Path = "/api/meta" + request.Path
		}
		request.Host = Host


	
		for _, response := range responses {
			fmt.Println("Checkpoint : SendToWhatsapp Response")
			
	
			var message map[string]interface{}
			helpers.JsonMarshaller(response, &message)
			fmt.Println("Whatsapp Message-------------",message)
			message["messaging_product"] = "whatsapp"
			message["recipient_type"] = "individual"
			message["to"] =  contactNumber
			request.Body = message
			fmt.Println("Checkpoint : SendToWhatsapp Request",request)
			metaResponse, err := helpers.MakeRequest(request)
			if err != nil {
				return err
			}
			fmt.Println(`Meta Response`, metaResponse)
		
	
	
			time.Sleep(1 * time.Second)
		}

		return nil
	}