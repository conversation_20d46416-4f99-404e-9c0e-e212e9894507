# Spiro EV Sales Lead Agent - Implementation Plan

## Overview
Single agent chatbot for Spiro electric vehicle sales with dynamic LLM-based responses and function calling architecture.

## Architecture

### Single Agent Design
- **Agent**: `SpiroSalesAgent` 
- **Two Tools**:
  1. **`progress_sales_flow`** - Main tool, always used to advance the sales script
  2. **`answer_from_knowledge`** - Used only when user asks questions requiring knowledge base search

### Agent Logic
1. **Primary Goal**: Always progress through the sales flow using `progress_sales_flow` tool
2. **Secondary Function**: If user asks questions, use `answer_from_knowledge` tool when knowledge base search is needed
3. **Flow Priority**: After answering any question, return to the main sales flow progression

## Sales Script Sequence

The agent follows this exact sequence dynamically through LLM:

1. **Profile Selection**: "Hi! I'm Sales Ninja from Spiro. Let me help you choose most suitable Spiro electric vehicle model and available financing options. Please select what fits most with your current profile?"
   - Options: University student / Salaried / Boda Boda rider / Fleet operator

2. **Petrol Bike Ownership**: "Do you currently own a petrol bike?"
   - Options: Yes/No

3. **Ownership Duration** (if Yes): "How many years have you owned it?"
   - Options: < 3 years / > 3 years

4. **Model Selection**: "Awesome. Based on your profile, below are the most suitable Spiro EV products for you. Which one would you choose?"
   - Options: Model A / Model B / Model C

5. **Loan Inquiry**: "Great choice! The price of [Selected Model] is KES [XX,XXX]. Would you require loan assistance?"
   - Options: Yes/No

6. **Existing Loan Check** (if Yes): "Before sharing financing options may I know do you have any existing loan?"
   - Options: Yes/No

7. **Financing Options**: Present KCB, Mogo, Watu, Mkopa options with details

8. **Financing Selection**: "Which loan option do you prefer?"

9. **Document Collection**: Request National ID, KRA pin, KCB account (if applicable)

10. **Experience Rating**: "Please rate your experience so we can improve"

## Tool Definitions

### Tool 1: Sales Flow Progression (Primary)
```python
sales_flow_tool = {
    "type": "function",
    "function": {
        "name": "progress_sales_flow",
        "description": "Main tool to progress through Spiro EV sales script. Always use this to advance the conversation through the sales process.",
        "parameters": {
            "type": "object",
            "properties": {
                "current_step": {
                    "type": "string",
                    "description": "Current step in sales process"
                },
                "user_response": {
                    "type": "string",
                    "description": "User's response to process"
                },
                "extracted_info": {
                    "type": "object",
                    "description": "Information extracted from user response"
                },
                "next_message": {
                    "type": "string",
                    "description": "Next message to send to user to continue sales flow"
                },
                "should_progress": {
                    "type": "boolean",
                    "description": "Whether to advance to next step in sales process"
                }
            },
            "required": ["current_step", "next_message"]
        }
    }
}
```

### Tool 2: Knowledge Base Q&A (Secondary)
```python
knowledge_qa_tool = {
    "type": "function",
    "function": {
        "name": "answer_from_knowledge",
        "description": "Use this tool ONLY when user asks questions that require knowledge base search about Spiro EV products, financing, or company information.",
        "parameters": {
            "type": "object",
            "properties": {
                "question": {
                    "type": "string",
                    "description": "User's question that needs knowledge base search"
                },
                "answer": {
                    "type": "string",
                    "description": "Answer based on knowledge base search"
                },
                "return_to_flow": {
                    "type": "boolean",
                    "description": "Always true - return to main sales flow after answering"
                }
            },
            "required": ["question", "answer", "return_to_flow"]
        }
    }
}
```

## State Management

### Sales Information Model
```python
class SalesInformation(BaseModel):
    user_profile: Optional[str] = None  # University student/Salaried/Boda Boda rider/Fleet operator
    owns_petrol_bike: Optional[str] = None  # Yes/No
    ownership_years: Optional[str] = None  # < 3 years / > 3 years
    selected_model: Optional[str] = None  # Model A/B/C
    needs_loan: Optional[str] = None  # Yes/No
    has_existing_loan: Optional[str] = None  # Yes/No
    selected_financing: Optional[str] = None  # KCB/Mogo/Watu/Mkopa
    documents_uploaded: Optional[bool] = None  # True when user uploads
    experience_rating: Optional[int] = None  # 1-5 rating
    
    def get_current_step(self) -> str:
        """Determine current step in sales process"""
        if not self.user_profile:
            return "profile_selection"
        elif not self.owns_petrol_bike:
            return "petrol_bike_inquiry"
        elif self.owns_petrol_bike == "Yes" and not self.ownership_years:
            return "ownership_duration"
        elif not self.selected_model:
            return "model_selection"
        elif not self.needs_loan:
            return "loan_inquiry"
        elif self.needs_loan == "Yes" and not self.has_existing_loan:
            return "existing_loan_check"
        elif self.needs_loan == "Yes" and not self.selected_financing:
            return "financing_options"
        elif self.needs_loan == "Yes" and not self.documents_uploaded:
            return "document_collection"
        elif not self.experience_rating:
            return "experience_rating"
        else:
            return "completed"
```

## System Prompt

```python
SPIRO_SALES_AGENT_PROMPT = """
You are Sales Ninja from Spiro, a single agent with TWO main functions:

PRIMARY FUNCTION (Main Goal): Progress through the Spiro EV sales script using the 'progress_sales_flow' tool
SECONDARY FUNCTION: Answer user questions using 'answer_from_knowledge' tool when knowledge base search is needed

SALES SCRIPT SEQUENCE (Your main goal):
1. Profile Selection → 2. Petrol Bike Ownership → 3. Ownership Duration → 4. Model Selection → 
5. Loan Inquiry → 6. Existing Loan Check → 7. Financing Options → 8. Financing Selection → 
9. Document Collection → 10. Experience Rating

TOOL USAGE STRATEGY:
1. ALWAYS try to use 'progress_sales_flow' tool first - this is your primary goal
2. ONLY use 'answer_from_knowledge' tool when:
   - User asks specific questions about Spiro products, features, pricing
   - User asks about financing details, loan terms, requirements  
   - User asks about company information, policies, support
   - The question requires searching knowledge base information

3. After using 'answer_from_knowledge' tool, IMMEDIATELY return to 'progress_sales_flow' tool

DECISION LOGIC:
- If user provides information for current sales step → Use 'progress_sales_flow'
- If user asks question needing knowledge base → Use 'answer_from_knowledge' then return to flow
- If user gives general response → Use 'progress_sales_flow' to guide them
- ALWAYS prioritize advancing the sales script

CRITICAL: Your main mission is to complete the sales script. Questions are secondary.
"""
```

## Response Format

```python
{
    "success": True,
    "message": "LLM generated message",
    "message_type": "flow" | "knowledge",
    "flow_name": "current_step",  # if message_type = "flow"
    "current_step": "profile_selection",
    "should_progress": True/False
}
```

## Project Structure

```
apps/lead_agent/
├── app/
│   ├── __init__.py
│   ├── routes.py
│   ├── models/
│   │   ├── __init__.py
│   │   └── session_model.py
│   ├── services/
│   │   ├── __init__.py
│   │   ├── agent_service.py
│   │   ├── session_service.py
│   │   └── knowledge_service.py
│   ├── static/
│   │   ├── css/
│   │   ├── js/
│   │   └── img/
│   └── templates/
│       └── lead_chat.html
├── requirements.txt
├── run.py
├── .env
└── IMPLEMENTATION_PLAN.md
```

## Key Features

1. **Dynamic LLM Responses**: No hardcoded messages, everything generated by Gemini
2. **Function Calling**: Two tools for flow progression and knowledge Q&A
3. **State Persistence**: Same database pattern as franchise_agent
4. **Flow Priority**: Always prioritize completing the sales script
5. **Knowledge Base Ready**: Placeholder for future knowledge base integration
6. **WhatsApp Integration**: Same webhook pattern as franchise_agent

## Environment Variables

- `GEMINI_API_KEY`: Gemini API key for LLM functionality
- `SECRET_KEY`: Flask secret key
- `DATABASE_URL`: Database connection string (defaults to SQLite)

## Implementation Notes

- Use same database structure as franchise_agent
- Gemini API key from .env file
- No static flows or hardcoded messages
- Everything dynamic through LLM function calling
- Knowledge base placeholder (to be added later)
- Document upload acknowledgment only (no validation)
