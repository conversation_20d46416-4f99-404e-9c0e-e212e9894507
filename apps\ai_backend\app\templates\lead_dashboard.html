<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spiro - Lead Qualification Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom@2.0.1"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    
    <style>
        :root {
            --primary-color: #3a36db;
            --primary-light: #5b58e2;
            --primary-dark: #2a27aa;
            --secondary-color: #6c757d;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --info-color: #3b82f6;
            --body-bg: #f9fafb;
            --card-bg: #ffffff;
            --header-bg: #1a1938;
            --card-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.05), 0 4px 6px -2px rgba(0, 0, 0, 0.025);
            --hover-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --transition-speed: 0.3s;
            --border-radius: 16px;
            --hot-color: #ef4444;
            --warm-color: #f59e0b;
            --cold-color: #3b82f6;
            --blur-effect: blur(10px);
            --spacing-sm: 0.75rem;
            --spacing-md: 1.5rem;
            --spacing-lg: 2.5rem;
            --spacing-xl: 3.5rem;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--body-bg);
            color: #111827;
            line-height: 1.6;
            overflow-x: hidden;
            background-image: radial-gradient(circle at 30% 70%, rgba(58, 54, 219, 0.03) 0%, transparent 25%),
                             radial-gradient(circle at 70% 30%, rgba(239, 68, 68, 0.03) 0%, transparent 25%);
            background-attachment: fixed;
        }

        .container {
            max-width: 1320px;
            padding: 0 var(--spacing-md);
        }

        .dashboard-header {
            background: linear-gradient(135deg, var(--header-bg) 0%, #332f6a 100%);
            color: white;
            padding: var(--spacing-lg) 0;
            margin-bottom: var(--spacing-lg);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
            backdrop-filter: var(--blur-effect);
            -webkit-backdrop-filter: var(--blur-effect);
        }

        .dashboard-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            opacity: 0.2;
        }

        .dashboard-header h1 {
            font-weight: 700;
            font-size: 2rem;
            margin: 0;
            letter-spacing: 0.5px;
            position: relative;
        }

        .card {
            background: var(--card-bg);
            border-radius: var(--border-radius);
            border: none;
            box-shadow: var(--card-shadow);
            transition: all var(--transition-speed) ease;
            margin-bottom: var(--spacing-lg);
            overflow: hidden;
        }

        .card:hover {
            box-shadow: var(--hover-shadow);
            transform: translateY(-2px);
        }

        .card-header {
            background-color: var(--card-bg);
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            padding: 1.25rem 1.5rem;
            font-weight: 600;
            color: var(--primary-dark);
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .card-header i {
            margin-right: 0.5rem;
            opacity: 0.8;
        }
        
        .card-header .card-actions {
            display: flex;
            gap: 0.5rem;
        }

        .stat-container {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .stat-card {
            border-radius: var(--border-radius);
            background: var(--card-bg);
            box-shadow: var(--card-shadow);
            padding: 1.5rem;
            transition: all var(--transition-speed) ease;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
            backdrop-filter: var(--blur-effect);
            -webkit-backdrop-filter: var(--blur-effect);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .stat-card:hover {
            box-shadow: var(--hover-shadow);
            transform: translateY(-3px);
        }

        .stat-card .icon {
            position: absolute;
            top: 1rem;
            right: 1rem;
            font-size: 1.5rem;
            opacity: 0.8;
            filter: drop-shadow(0 0 8px rgba(0, 0, 0, 0.1));
        }

        .stat-card h3 {
            font-size: 1rem;
            color: var(--secondary-color);
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .stat-card h2 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.75rem;
            background: linear-gradient(to right, var(--primary-color), var(--primary-light));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            letter-spacing: -1px;
        }

        .stat-card.hot-leads h2 {
            background: linear-gradient(to right, #ef4444, #f87171);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .stat-card.warm-leads h2 {
            background: linear-gradient(to right, #f59e0b, #fbbf24);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .stat-card.cold-leads h2 {
            background: linear-gradient(to right, #3b82f6, #60a5fa);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .stat-card .progress-container {
            height: 6px;
            background-color: rgba(0, 0, 0, 0.05);
            border-radius: 3px;
            overflow: hidden;
            margin-top: auto;
        }

        .stat-card .progress-bar {
            height: 100%;
            border-radius: 3px;
            transition: width 1s ease;
        }

        .stat-card.hot-leads .progress-bar {
            background: linear-gradient(to right, #ef4444, #f87171);
        }

        .stat-card.warm-leads .progress-bar {
            background: linear-gradient(to right, #f59e0b, #fbbf24);
        }

        .stat-card.cold-leads .progress-bar {
            background: linear-gradient(to right, #3b82f6, #60a5fa);
        }

        .chart-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .lead-hot {
            background-color: rgba(239, 68, 68, 0.05);
            border-left: 4px solid var(--hot-color);
        }

        .lead-warm {
            background-color: rgba(245, 158, 11, 0.05);
            border-left: 4px solid var(--warm-color);
        }

        .lead-cold {
            background-color: rgba(59, 130, 246, 0.05);
            border-left: 4px solid var(--cold-color);
        }

        /* Chart Containers */
        .chart-container {
            position: relative;
            height: 350px;
            margin: 0.5rem 0;
            padding: 1rem;
        }

        .chart-options {
            display: flex;
            justify-content: flex-end;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .chart-options button {
            padding: 0.25rem 0.5rem;
            font-size: 0.85rem;
            background-color: rgba(0, 0, 0, 0.05);
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .chart-options button:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }

        .chart-options button.active {
            background-color: var(--primary-color);
            color: white;
        }

        .table {
            margin-bottom: 0;
            border-collapse: separate;
            border-spacing: 0;
        }

        .table thead th {
            background-color: rgba(58, 54, 219, 0.03);
            border-bottom: none;
            color: var(--primary-dark);
            font-weight: 600;
            padding: 1.25rem 1rem;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .table tbody td {
            padding: 1.25rem 1rem;
            vertical-align: middle;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            font-size: 0.95rem;
        }

        .table tbody tr {
            transition: background-color var(--transition-speed) ease;
        }

        .table tbody tr:hover {
            background-color: rgba(0, 0, 0, 0.02);
        }

        .badge {
            padding: 0.5em 0.8em;
            font-weight: 500;
            letter-spacing: 0.3px;
            border-radius: 6px;
            font-size: 0.85rem;
        }

        .btn {
            border-radius: 8px;
            padding: 0.5rem 1rem;
            font-weight: 500;
            transition: all var(--transition-speed) ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: var(--primary-light);
            border-color: var(--primary-light);
        }

        .btn-light {
            background: rgba(255, 255, 255, 0.9);
            border-color: transparent;
            backdrop-filter: blur(5px);
        }

        .btn-light:hover {
            background: rgba(255, 255, 255, 1);
        }

        .btn-sm {
            font-size: 0.85rem;
            padding: 0.4rem 0.8rem;
        }

        .modal-content {
            border-radius: var(--border-radius);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            border: none;
        }

        .modal-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
            color: white;
            border-bottom: none;
            border-radius: calc(var(--border-radius) - 1px) calc(var(--border-radius) - 1px) 0 0;
            padding: 1.5rem;
        }

        .modal-header .btn-close {
            filter: brightness(0) invert(1);
            opacity: 0.8;
        }

        .modal-body {
            padding: 1.5rem;
        }

        .list-group-item {
            border: 1px solid rgba(0, 0, 0, 0.05);
            margin-bottom: 0.5rem;
            border-radius: 8px;
            transition: all var(--transition-speed) ease;
        }

        .list-group-item:hover {
            background-color: rgba(0, 0, 0, 0.02);
        }

        .badge.bg-primary {
            background-color: var(--primary-color) !important;
        }

        .badge.bg-success {
            background-color: var(--success-color) !important;
        }

        .badge.bg-danger {
            background-color: var(--danger-color) !important;
        }

        .badge.bg-warning {
            background-color: var(--warning-color) !important;
            color: #fff !important;
        }

        /* Markdown Content Styles */
        .markdown-content {
            line-height: 1.7;
            color: #111827;
            padding: var(--spacing-md);
            max-height: none; /* Remove any height restriction */
            overflow: visible; /* Ensure content is not truncated */
        }

        /* Markdown table styles - ensuring all styles with !important to override any default styles */
        .markdown-content table, 
        #renderedReportView table {
            width: 100% !important;
            border-collapse: collapse !important;
            margin: 1.5rem 0 !important;
            border: 1px solid #e5e7eb !important;
            background-color: white !important;
        }

        .markdown-content table th, 
        .markdown-content table td,
        #renderedReportView table th,
        #renderedReportView table td {
            border: 1px solid #e5e7eb !important;
            padding: 8px !important;
            text-align: left !important;
            vertical-align: top !important;
            line-height: 1.5 !important;
        }

        .markdown-content table th,
        #renderedReportView table th {
            background-color: #f8f9fa !important;
            font-weight: 600 !important;
            color: #333 !important;
        }

        .markdown-content table tr:nth-child(even),
        #renderedReportView table tr:nth-child(even) {
            background-color: #f9fafb !important;
        }

        .markdown-content table tr:hover,
        #renderedReportView table tr:hover {
            background-color: #f3f4f6 !important;
        }

        /* Make sure table wrapper allows scrolling on small screens */
        .table-responsive {
            display: block !important;
            width: 100% !important;
            overflow-x: auto !important;
            -webkit-overflow-scrolling: touch !important;
        }

        /* Better styles for markdown headings */
        #renderedReportView h1 {
            font-size: 1.8rem !important;
            margin-bottom: 1rem !important;
            color: #111 !important;
        }

        #renderedReportView h2 {
            font-size: 1.5rem !important;
            margin-top: 1.5rem !important;
            margin-bottom: 1rem !important;
            color: #333 !important;
            border-bottom: 1px solid #eee !important;
            padding-bottom: 0.5rem !important;
        }

        #renderedReportView h3 {
            font-size: 1.25rem !important;
            margin-top: 1.25rem !important;
            margin-bottom: 0.75rem !important;
            color: #444 !important;
        }

        /* Raw report view styling */
        #rawReportView {
            font-family: 'Consolas', 'Monaco', monospace !important;
            white-space: pre-wrap !important;
            background-color: #f8f9fa !important;
            border: 1px solid #e9ecef !important;
            border-radius: 4px !important;
            padding: 1rem !important;
            color: #333 !important;
            font-size: 0.9rem !important;
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes scaleIn {
            from { transform: scale(0.9); opacity: 0; }
            to { transform: scale(1); opacity: 1; }
        }

        @keyframes slideInRight {
            from { transform: translateX(30px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        .card {
            animation: fadeIn 0.5s ease-out;
        }

        .stat-card {
            animation: scaleIn 0.5s ease-out;
        }

        .stat-card:nth-child(1) { animation-delay: 0.1s; }
        .stat-card:nth-child(2) { animation-delay: 0.2s; }
        .stat-card:nth-child(3) { animation-delay: 0.3s; }

        .table tbody tr {
            animation: fadeIn 0.3s ease-out backwards;
        }

        .dashboard-actions {
            display: flex;
            gap: 0.75rem;
        }

        .date-filter {
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 0.25rem 0.5rem;
            color: white;
            font-size: 0.9rem;
        }

        .date-filter i {
            margin-right: 0.5rem;
        }

        @media (max-width: 992px) {
            .stat-container {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .chart-grid {
                grid-template-columns: 1fr;
            }
        }
        
        @media (max-width: 768px) {
            .stat-container {
                grid-template-columns: 1fr;
            }
        }

        .table-responsive {
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--card-shadow);
        }

        .score-table {
            margin-bottom: 0;
            border-collapse: separate;
            border-spacing: 0;
            width: 100%;
            background: white;
            overflow: hidden;
            border: none;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.04);
        }

        .score-table thead th {
            background-image: linear-gradient(to right, #f9fafb, #ffffff);
            color: #1e293b;
            font-weight: 600;
            padding: 1.25rem 1.5rem;
            font-size: 0.9rem;
            letter-spacing: 0.5px;
            border: none;
            text-transform: uppercase;
            position: relative;
        }

        .score-table thead th:after {
            content: '';
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            height: 1px;
            background: linear-gradient(to right, rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.02));
        }

        .score-table tbody td {
            padding: 1.25rem 1.5rem;
            border: none;
            font-size: 0.95rem;
            transition: all var(--transition-speed) ease;
            position: relative;
        }

        .score-table tbody tr:not(:last-child) td:after {
            content: '';
            position: absolute;
            left: 1.5rem;
            right: 1.5rem;
            bottom: 0;
            height: 1px;
            background: linear-gradient(to right, rgba(0, 0, 0, 0.03), rgba(0, 0, 0, 0.01));
        }

        .score-table tbody tr:hover td {
            background-color: rgba(249, 250, 251, 0.5);
        }

        .score-table tfoot th {
            background: #f9fafb;
            color: #1e293b;
            font-weight: 700;
            padding: 1.25rem 1.5rem;
            font-size: 0.95rem;
            border: none;
            position: relative;
        }

        .score-table tfoot th:before {
            content: '';
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            height: 1px;
            background: linear-gradient(to right, rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.02));
        }

        .score-value {
            text-align: center;
            font-weight: 700;
            border-radius: 100px;
            padding: 0.5rem 1rem;
            min-width: 3rem;
            display: inline-block;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.04);
        }

        .score-value.excellent {
            background: linear-gradient(to right, rgba(16, 185, 129, 0.12), rgba(16, 185, 129, 0.05));
            color: #065f46;
        }

        .score-value.good {
            background: linear-gradient(to right, rgba(245, 158, 11, 0.12), rgba(245, 158, 11, 0.05));
            color: #92400e;
        }

        .score-value.needs-improvement {
            background: linear-gradient(to right, rgba(239, 68, 68, 0.12), rgba(239, 68, 68, 0.05));
            color: #b91c1c;
        }

        .score-criteria {
            display: flex;
            align-items: center;
        }

        .score-criteria-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
            min-width: 36px;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.05));
            border-radius: 8px;
            margin-right: 1rem;
        }

        .score-criteria-icon i {
            font-size: 1rem;
            color: #3b82f6;
        }

        .score-criteria-label {
            font-weight: 500;
            color: #334155;
        }

        .score-max {
            font-size: 0.85rem;
            color: #64748b;
            font-weight: 500;
            margin-left: 0.5rem;
        }

        .score-progress {
            height: 8px;
            background-color: #f1f5f9;
            border-radius: 100px;
            overflow: hidden;
        }

        .score-progress-bar {
            height: 100%;
            border-radius: 100px;
            transition: width 1s ease-in-out;
        }

        .score-total-wrapper {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .score-total {
            font-size: 1.25rem;
            padding: 0.375rem 1rem;
            font-weight: 700;
            color: white;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-radius: 100px;
            box-shadow: 0 3px 10px rgba(58, 54, 219, 0.2);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="dashboard-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col">
                    <h1><i class="bi bi-lightning-charge-fill"></i> Spiro Lead Qualification</h1>
                </div>
                <div class="col-auto">
                    <div class="dashboard-actions">
                        <div class="date-filter">
                            <i class="bi bi-calendar3"></i>
                            <span id="dateRangeText">Last 30 days</span>
                        </div>
                    <button class="btn btn-light" id="refreshBtn">
                            <i class="bi bi-arrow-clockwise"></i> Refresh
                    </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Dashboard -->
    <div class="container">
        <!-- Stats Section -->
        <div class="stat-container">
            <div class="stat-card hot-leads">
                <i class="bi bi-fire icon"></i>
                <h3>Hot Leads</h3>
                    <h2 id="hotLeadCount">0</h2>
                <div class="progress-container">
                    <div class="progress-bar" id="hotLeadProgress" style="width: 0%"></div>
                </div>
            </div>
            <div class="stat-card warm-leads">
                <i class="bi bi-sun icon"></i>
                <h3>Warm Leads</h3>
                    <h2 id="warmLeadCount">0</h2>
                <div class="progress-container">
                    <div class="progress-bar" id="warmLeadProgress" style="width: 0%"></div>
                </div>
            </div>
            <div class="stat-card cold-leads">
                <i class="bi bi-snow2 icon"></i>
                <h3>Cold Leads</h3>
                    <h2 id="coldLeadCount">0</h2>
                <div class="progress-container">
                    <div class="progress-bar" id="coldLeadProgress" style="width: 0%"></div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="chart-grid">
                <div class="card">
                    <div class="card-header">
                    <div><i class="bi bi-pie-chart-fill"></i> Lead Distribution</div>
                    <div class="card-actions">
                        <div class="chart-options" id="distributionChartOptions">
                            <button data-type="donut" class="active">Donut</button>
                            <button data-type="bar">Bar</button>
                        </div>
                        <button class="btn btn-sm btn-outline-primary ms-2" id="downloadDistributionBtn">
                            <i class="bi bi-download"></i>
                        </button>
                    </div>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                        <div id="leadDistributionChart"></div>
                        </div>
                    </div>
                </div>
                <div class="card">
                    <div class="card-header">
                    <div><i class="bi bi-bar-chart-line-fill"></i> Score Analysis</div>
                    <div class="card-actions">
                        <div class="chart-options" id="scoreChartOptions">
                            <button data-type="bar" class="active">Bar</button>
                            <button data-type="radar">Radar</button>
                        </div>
                        <button class="btn btn-sm btn-outline-primary ms-2" id="downloadScoreBtn">
                            <i class="bi bi-download"></i>
                        </button>
                    </div>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                        <div id="scoreComparisonChart"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Lead List -->
        <div class="card">
            <div class="card-header">
                <div><i class="bi bi-list-check"></i> Lead Qualification Results</div>
                <div class="card-actions">
                    <div class="input-group">
                        <input type="text" class="form-control form-control-sm" placeholder="Search leads..." id="leadSearchInput">
                        <button class="btn btn-sm btn-primary" id="searchLeadsBtn">
                            <i class="bi bi-search"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Phone Number</th>
                                <th>Category</th>
                                <th>Score</th>
                                <th>Primary Use</th>
                                <th>Timeline</th>
                                <th>Date/Time</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody id="leadTableBody">
                            <!-- Lead data will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Lead Detail Modal -->
    <div class="modal fade" id="leadDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-person-lines-fill"></i> Lead Qualification Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="leadModalContent">
                        <div class="row mb-4">
                            <div class="col-md-6">
                        <div class="mb-3">
                                    <h6 class="text-muted">Phone Number</h6>
                                    <div id="modalPhoneNumber" class="fs-5 fw-bold"></div>
                        </div>
                        </div>
                            <div class="col-md-6">
                        <div class="mb-3">
                                    <h6 class="text-muted">Lead Category</h6>
                                    <div id="modalCategory" class="fs-5 fw-bold"></div>
                        </div>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <h6 class="text-muted mb-3">Qualification Scores</h6>
                            <div class="table-responsive">
                                <table class="table score-table">
                                    <thead>
                                        <tr>
                                            <th style="width: 45%">Criteria</th>
                                            <th class="text-center" style="width: 25%">Score</th>
                                            <th class="text-center" style="width: 30%">Progress</th>
                                        </tr>
                                    </thead>
                                    <tbody id="scoreTableBody">
                                        <!-- Score data will be populated here -->
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <th>Total Assessment</th>
                                            <th class="text-center">
                                                <div class="score-total-wrapper">
                                                    <span class="score-total" id="modalTotalScore">0</span>
                                                    <span class="score-max">out of 50</span>
                                                </div>
                                            </th>
                                            <th></th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <h6 class="text-muted mb-3">Qualification Summary</h6>
                            <div class="card">
                                <div class="card-body markdown-content" id="qualificationDetails" style="min-height: 200px; max-height: none;">
                                    <!-- Report will be shown here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Global variables
        let leadData = [];
        let distributionChart = null;
        let scoreComparisonChart = null;
        let scoreBreakdownChart = null;
        let currentDistributionChartType = 'donut';
        let currentScoreChartType = 'bar';
        
        // Colors
        const chartColors = {
            hot: {
                primary: '#ef4444',
                secondary: '#f87171',
                background: 'rgba(239, 68, 68, 0.1)',
                gradient: ['#ef4444', '#f87171']
            },
            warm: {
                primary: '#f59e0b',
                secondary: '#fbbf24',
                background: 'rgba(245, 158, 11, 0.1)',
                gradient: ['#f59e0b', '#fbbf24']
            },
            cold: {
                primary: '#3b82f6',
                secondary: '#60a5fa',
                background: 'rgba(59, 130, 246, 0.1)',
                gradient: ['#3b82f6', '#60a5fa']
            }
        };
        
        // Initialize marked.js options
        marked.setOptions({
            breaks: true,        // Preserve line breaks
            gfm: true,           // GitHub Flavored Markdown
            tables: true,        // Support tables
            smartLists: true,    // Better lists
            smartypants: true,   // Smart typographic punctuation
            headerIds: false,    // Don't add IDs to headers
            mangle: false,       // Don't mangle email addresses
            sanitize: false      // Don't sanitize - modern browsers handle this
        });
        
        // Initialize the dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeEventListeners();
            fetchLeadData();
            // Register ApexCharts globally if used
            if (typeof ApexCharts !== 'undefined') {
                Chart.register(ApexCharts);
            }
        });
            
        // Initialize event listeners
        function initializeEventListeners() {
            // Refresh button
            document.getElementById('refreshBtn').addEventListener('click', fetchLeadData);
            
            // Chart type toggles
            document.getElementById('distributionChartOptions').addEventListener('click', function(e) {
                if (e.target.tagName === 'BUTTON') {
                    const chartType = e.target.getAttribute('data-type');
                    changeDistributionChartType(chartType);
                    
                    // Update active state
                    this.querySelectorAll('button').forEach(btn => btn.classList.remove('active'));
                    e.target.classList.add('active');
                }
            });
            
            document.getElementById('scoreChartOptions').addEventListener('click', function(e) {
                if (e.target.tagName === 'BUTTON') {
                    const chartType = e.target.getAttribute('data-type');
                    changeScoreChartType(chartType);
                    
                    // Update active state
                    this.querySelectorAll('button').forEach(btn => btn.classList.remove('active'));
                    e.target.classList.add('active');
                }
            });
            
            // Download chart buttons
            document.getElementById('downloadDistributionBtn').addEventListener('click', function() {
                if (distributionChart) {
                    distributionChart.dataURI().then(({ imgURI, blob }) => {
                        const downloadLink = document.createElement('a');
                        downloadLink.href = imgURI;
                        downloadLink.download = 'lead-distribution-chart.png';
                        downloadLink.click();
                    });
                }
            });
            
            document.getElementById('downloadScoreBtn').addEventListener('click', function() {
                if (scoreComparisonChart) {
                    scoreComparisonChart.dataURI().then(({ imgURI, blob }) => {
                        const downloadLink = document.createElement('a');
                        downloadLink.href = imgURI;
                        downloadLink.download = 'score-analysis-chart.png';
                        downloadLink.click();
                    });
                }
            });
            
            // Lead search
            document.getElementById('searchLeadsBtn').addEventListener('click', searchLeads);
            document.getElementById('leadSearchInput').addEventListener('keyup', function(e) {
                if (e.key === 'Enter') {
                    searchLeads();
                }
            });
        }
        
        // Fetch lead qualification data
        function fetchLeadData() {
            // Show loading state
            showLoading(true);
            
            fetch('/api/lead-qualifications')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        leadData = data.qualifications;
                        updateDashboard();
                    } else {
                        console.error('Error fetching lead data:', data.error);
                        showNotification('Error loading lead data', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error fetching lead data:', error);
                    showNotification('Error connecting to the server', 'error');
                })
                .finally(() => {
                    showLoading(false);
                });
        }
        
        // Show loading state
        function showLoading(isLoading) {
            const refreshBtn = document.getElementById('refreshBtn');
            
            if (isLoading) {
                refreshBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Loading...';
                refreshBtn.disabled = true;
            } else {
                refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise"></i> Refresh';
                refreshBtn.disabled = false;
            }
        }
        
        // Show notification
        function showNotification(message, type = 'info') {
            // Create toast container if it doesn't exist
            let toastContainer = document.querySelector('.toast-container');
            
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
                document.body.appendChild(toastContainer);
            }
            
            // Create toast
            const toastId = 'toast-' + Date.now();
            const toastEl = document.createElement('div');
            toastEl.className = `toast align-items-center text-white bg-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'primary'}`;
            toastEl.setAttribute('role', 'alert');
            toastEl.setAttribute('aria-live', 'assertive');
            toastEl.setAttribute('aria-atomic', 'true');
            toastEl.setAttribute('id', toastId);
            
            toastEl.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            `;
            
            toastContainer.appendChild(toastEl);
            
            // Initialize and show toast
            const toast = new bootstrap.Toast(toastEl, { delay: 3000 });
            toast.show();
            
            // Remove toast after it's hidden
            toastEl.addEventListener('hidden.bs.toast', function() {
                toastEl.remove();
                });
        }
        
        // Update the dashboard with the fetched data
        function updateDashboard() {
            updateLeadCounts();
            updateLeadTable();
            updateDistributionChart(currentDistributionChartType);
            updateScoreComparisonChart(currentScoreChartType);
        }
        
        // Update the lead counts and progress bars
        function updateLeadCounts() {
            const hotLeads = leadData.filter(lead => lead.category === 'Hot Lead');
            const warmLeads = leadData.filter(lead => lead.category === 'Warm Lead');
            const coldLeads = leadData.filter(lead => lead.category === 'Cold Lead');
            const totalLeads = leadData.length;
            
            // Update count numbers with animation
            animateCounter('hotLeadCount', 0, hotLeads.length);
            animateCounter('warmLeadCount', 0, warmLeads.length);
            animateCounter('coldLeadCount', 0, coldLeads.length);
            
            // Update progress bars
            if (totalLeads > 0) {
                document.getElementById('hotLeadProgress').style.width = `${(hotLeads.length / totalLeads) * 100}%`;
                document.getElementById('warmLeadProgress').style.width = `${(warmLeads.length / totalLeads) * 100}%`;
                document.getElementById('coldLeadProgress').style.width = `${(coldLeads.length / totalLeads) * 100}%`;
            }
        }
        
        // Animate counter from start to end
        function animateCounter(elementId, start, end) {
            const duration = 1000;
            const element = document.getElementById(elementId);
            const startTime = performance.now();
            
            function updateCounter(currentTime) {
                const elapsedTime = currentTime - startTime;
                const progress = Math.min(elapsedTime / duration, 1);
                const value = Math.floor(start + (end - start) * progress);
                
                element.textContent = value;
                
                if (progress < 1) {
                    requestAnimationFrame(updateCounter);
                }
            }
            
            requestAnimationFrame(updateCounter);
        }
        
        // Update the distribution chart
        function updateDistributionChart(chartType) {
            currentDistributionChartType = chartType;
            
            // Count leads by category
            const hotLeads = leadData.filter(lead => lead.category === 'Hot Lead').length;
            const warmLeads = leadData.filter(lead => lead.category === 'Warm Lead').length;
            const coldLeads = leadData.filter(lead => lead.category === 'Cold Lead').length;
            
            // Destroy existing chart if it exists
            if (distributionChart) {
                distributionChart.destroy();
            }
            
            // Configure chart options based on type
            if (chartType === 'bar') {
                const barOptions = {
                    chart: {
                        type: 'bar',
                        height: 350,
                        fontFamily: 'Inter, sans-serif',
                        toolbar: {
                            show: false
                        },
                        animations: {
                            enabled: true,
                            easing: 'easeinout',
                            speed: 800,
                            dynamicAnimation: {
                                enabled: true
                            }
                        },
                        dropShadow: {
                            enabled: true,
                            top: 3,
                            left: 0,
                            blur: 4,
                            opacity: 0.1
                        }
                    },
                    colors: [chartColors.hot.primary, chartColors.warm.primary, chartColors.cold.primary],
                    plotOptions: {
                        bar: {
                            horizontal: false,
                            columnWidth: '65%',
                            borderRadius: 6,
                            distributed: true,
                            dataLabels: {
                                position: 'top'
                            }
                        }
                    },
                    dataLabels: {
                        enabled: true,
                        offsetY: -20,
                        style: {
                            fontSize: '12px',
                            fontWeight: 'bold',
                            fontFamily: 'Inter, sans-serif',
                            colors: ["#111827"]
                        },
                        formatter: function(val) {
                            return val;
                        }
                    },
                    series: [{
                        name: 'Leads',
                        data: [hotLeads, warmLeads, coldLeads]
                    }],
                    stroke: {
                        show: true,
                        width: 2,
                        colors: ['transparent']
                    },
                    xaxis: {
                        categories: ['Hot Leads', 'Warm Leads', 'Cold Leads'],
                        labels: {
                            style: {
                                fontSize: '13px',
                                fontFamily: 'Inter, sans-serif'
                            }
                        },
                        axisBorder: {
                            show: false
                        },
                        axisTicks: {
                            show: false
                        }
                    },
                    yaxis: {
                        title: {
                            text: 'Number of Leads',
                            style: {
                                fontSize: '14px',
                                fontFamily: 'Inter, sans-serif'
                            }
                        },
                        labels: {
                            style: {
                                fontSize: '12px',
                                fontFamily: 'Inter, sans-serif'
                            }
                        }
                    },
                    grid: {
                        borderColor: '#f1f1f1',
                        row: {
                            colors: ['transparent', 'transparent']
                        }
                    },
                    fill: {
                        type: 'gradient',
                        gradient: {
                            shade: 'light',
                            type: "vertical",
                            shadeIntensity: 0.2,
                            inverseColors: false,
                            opacityFrom: 1,
                            opacityTo: 0.9
                        }
                    },
                    tooltip: {
                        theme: 'light',
                        y: {
                            formatter: function(val) {
                                return val + " leads";
                            }
                        }
                    },
                    legend: {
                        show: false
                    }
                };
                
                distributionChart = new ApexCharts(document.getElementById("leadDistributionChart"), barOptions);
                distributionChart.render();
                
            } else {
                // Donut chart options
                const donutOptions = {
                    chart: {
                        type: 'donut',
                        height: 350,
                        fontFamily: 'Inter, sans-serif',
                        toolbar: {
                            show: false
                        },
                        animations: {
                            enabled: true,
                            easing: 'easeinout',
                            speed: 800,
                            animateGradually: {
                                enabled: true,
                                delay: 150
                            },
                            dynamicAnimation: {
                                enabled: true,
                                speed: 350
                            }
                        },
                        dropShadow: {
                            enabled: true,
                            top: 0,
                            left: 0,
                            blur: 3,
                            opacity: 0.1
                        }
                    },
                    colors: [chartColors.hot.primary, chartColors.warm.primary, chartColors.cold.primary],
                    labels: ['Hot Leads', 'Warm Leads', 'Cold Leads'],
                    series: [hotLeads, warmLeads, coldLeads],
                    stroke: {
                        width: 0
                    },
                    plotOptions: {
                        pie: {
                            expandOnClick: true,
                            donut: {
                                size: '60%',
                                labels: {
                                    show: true,
                                    name: {
                                        show: true,
                                        offsetY: -10,
                                        formatter: function(val) {
                                            return val;
                                        }
                                    },
                                    value: {
                                        show: true,
                                        fontSize: '24px',
                                        fontWeight: 600,
                                        offsetY: 6,
                                        formatter: function(val) {
                                            return val;
                                        }
                                    },
                                    total: {
                                        show: true,
                                        label: 'Total Leads',
                                        fontSize: '16px',
                                        fontWeight: 600,
                                        formatter: function(w) {
                                            return w.globals.seriesTotals.reduce((a, b) => a + b, 0);
                                        }
                                    }
                                }
                            }
                        }
                    },
                    legend: {
                        show: true,
                        position: 'bottom',
                        horizontalAlign: 'center',
                        fontSize: '14px',
                        fontFamily: 'Inter, sans-serif',
                        itemMargin: {
                            horizontal: 10,
                            vertical: 5
                        },
                        markers: {
                            width: 12,
                            height: 12,
                            radius: 6
                        }
                    },
                    tooltip: {
                        enabled: true,
                        fillSeriesColor: false,
                        theme: 'light',
                        style: {
                            fontSize: '13px',
                            fontFamily: 'Inter, sans-serif'
                        },
                        y: {
                            formatter: function(val) {
                                return val + " leads";
                            }
                        }
                    },
                    responsive: [{
                        breakpoint: 480,
                options: {
                            chart: {
                                height: 280
                            },
                        legend: {
                            position: 'bottom'
                        }
                    }
                    }]
                };
                
                distributionChart = new ApexCharts(document.getElementById("leadDistributionChart"), donutOptions);
                distributionChart.render();
            }
        }
        
        // Change distribution chart type
        function changeDistributionChartType(chartType) {
            if (currentDistributionChartType !== chartType) {
                updateDistributionChart(chartType);
            }
        }
        
        // Update the score comparison chart
        function updateScoreComparisonChart(chartType) {
            currentScoreChartType = chartType;
            
            // Group leads by category
            const hotLeads = leadData.filter(lead => lead.category === 'Hot Lead');
            const warmLeads = leadData.filter(lead => lead.category === 'Warm Lead');
            const coldLeads = leadData.filter(lead => lead.category === 'Cold Lead');
            
            // Calculate average scores for each category
            const calcAverage = (leads, scoreName) => {
                if (leads.length === 0) return 0;
                return leads.reduce((sum, lead) => sum + (lead.scores[scoreName] || 0), 0) / leads.length;
            };
            
            const dimensions = ['primary_use', 'model_selection', 'purchase_timeline', 'budget_range', 'interest_level'];
            const dimensionLabels = ['Primary Use', 'Model Selection', 'Purchase Timeline', 'Budget Range', 'Interest Level'];
            
            const hotScores = dimensions.map(dim => Math.round(calcAverage(hotLeads, dim) * 10) / 10);
            const warmScores = dimensions.map(dim => Math.round(calcAverage(warmLeads, dim) * 10) / 10);
            const coldScores = dimensions.map(dim => Math.round(calcAverage(coldLeads, dim) * 10) / 10);
            
            // Destroy existing chart if it exists
            if (scoreComparisonChart) {
                scoreComparisonChart.destroy();
            }
            
            if (chartType === 'bar') {
                // Bar chart options
                const barOptions = {
                    chart: {
                        type: 'bar',
                        height: 350,
                        fontFamily: 'Inter, sans-serif',
                        stacked: false,
                        toolbar: {
                            show: false
                        },
                        animations: {
                            enabled: true,
                            easing: 'easeinout',
                            speed: 800
                        },
                        dropShadow: {
                            enabled: true,
                            top: 3,
                            left: 0,
                            blur: 4,
                            opacity: 0.1
                        }
                    },
                    colors: [chartColors.hot.primary, chartColors.warm.primary, chartColors.cold.primary],
                    plotOptions: {
                        bar: {
                            horizontal: false,
                            columnWidth: '70%',
                            borderRadius: 6,
                            dataLabels: {
                                position: 'top'
                            }
                        }
                    },
                    dataLabels: {
                        enabled: true,
                        offsetY: -20,
                        style: {
                            fontSize: '11px',
                            fontWeight: 'bold',
                            colors: ["#111827"]
                        },
                        formatter: function(val) {
                            return val.toFixed(1);
                        }
                    },
                    series: [
                        {
                            name: 'Hot Leads',
                            data: hotScores
                        },
                        {
                            name: 'Warm Leads',
                            data: warmScores
                        },
                        {
                            name: 'Cold Leads',
                            data: coldScores
                        }
                    ],
                    stroke: {
                        show: true,
                        width: 2,
                        colors: ['transparent']
                    },
                    xaxis: {
                        categories: dimensionLabels,
                        labels: {
                            style: {
                                fontSize: '13px',
                                fontFamily: 'Inter, sans-serif'
                            }
                        },
                        axisBorder: {
                            show: false
                        },
                        axisTicks: {
                            show: false
                        }
                    },
                    yaxis: {
                        title: {
                            text: 'Average Score',
                            style: {
                                fontSize: '13px',
                                fontFamily: 'Inter, sans-serif'
                            }
                        },
                            min: 0,
                            max: 10,
                        forceNiceScale: true,
                        labels: {
                            style: {
                                fontSize: '12px',
                                fontFamily: 'Inter, sans-serif'
                            }
                        }
                    },
                    grid: {
                        borderColor: '#f1f1f1',
                        row: {
                            colors: ['transparent', 'transparent']
                        }
                    },
                    fill: {
                        type: 'gradient',
                        gradient: {
                            shade: 'light',
                            type: "vertical",
                            shadeIntensity: 0.2,
                            inverseColors: false,
                            opacityFrom: 1,
                            opacityTo: 0.9
                        }
                    },
                    tooltip: {
                        shared: true,
                        intersect: false,
                        y: {
                            formatter: function(val) {
                                return val.toFixed(1) + " / 10";
                            }
                        }
                    },
                    legend: {
                        position: 'top',
                        horizontalAlign: 'right',
                        fontSize: '13px',
                        fontFamily: 'Inter, sans-serif',
                        markers: {
                            width: 12,
                            height: 12,
                            radius: 6
                        }
                    }
                };
                
                scoreComparisonChart = new ApexCharts(document.getElementById("scoreComparisonChart"), barOptions);
                scoreComparisonChart.render();
            } else {
                // Radar chart options
                const radarOptions = {
                    chart: {
                        type: 'radar',
                        height: 350,
                        fontFamily: 'Inter, sans-serif',
                        toolbar: {
                            show: false
                        },
                        animations: {
                            enabled: true,
                            easing: 'easeinout',
                            speed: 800
                        },
                        dropShadow: {
                            enabled: true,
                            blur: 3,
                            opacity: 0.1
                        }
                    },
                    series: [
                        {
                            name: 'Hot Leads',
                            data: hotScores
                        },
                        {
                            name: 'Warm Leads',
                            data: warmScores
                        },
                        {
                            name: 'Cold Leads',
                            data: coldScores
                        }
                    ],
                    stroke: {
                        width: 2
                    },
                    fill: {
                        opacity: 0.15
                    },
                    markers: {
                        size: 4,
                        hover: {
                            size: 6
                        }
                    },
                    colors: [chartColors.hot.primary, chartColors.warm.primary, chartColors.cold.primary],
                    xaxis: {
                        categories: dimensionLabels,
                        labels: {
                            style: {
                                fontSize: '13px',
                                fontFamily: 'Inter, sans-serif'
                            }
                        }
                    },
                    yaxis: {
                        show: false,
                        min: 0,
                        max: 10
                    },
                    plotOptions: {
                        radar: {
                            polygons: {
                                strokeWidth: 1,
                                strokeColors: '#e9e9e9',
                                connectorColors: '#e9e9e9',
                                fill: {
                                    colors: ['#f8f8f8', '#fff']
                                }
                            }
                        }
                    },
                    tooltip: {
                        shared: true,
                        intersect: false,
                        y: {
                            formatter: function(val) {
                                return val.toFixed(1) + " / 10";
                            }
                        }
                    },
                    legend: {
                        position: 'top',
                        horizontalAlign: 'right',
                        fontSize: '13px',
                        fontFamily: 'Inter, sans-serif',
                        markers: {
                            width: 12,
                            height: 12,
                            radius: 6
                        }
                    }
                };
                
                scoreComparisonChart = new ApexCharts(document.getElementById("scoreComparisonChart"), radarOptions);
                scoreComparisonChart.render();
            }
        }
        
        // Change score chart type
        function changeScoreChartType(chartType) {
            if (currentScoreChartType !== chartType) {
                updateScoreComparisonChart(chartType);
            }
        }
        
        // Search leads
        function searchLeads() {
            const searchTerm = document.getElementById('leadSearchInput').value.toLowerCase();
            
            if (searchTerm.trim() === '') {
                updateLeadTable(leadData);
                return;
            }
            
            const filteredLeads = leadData.filter(lead => {
                return (
                    lead.phone_number.toLowerCase().includes(searchTerm) ||
                    lead.category.toLowerCase().includes(searchTerm) ||
                    (lead.lead_details.primary_use && lead.lead_details.primary_use.toLowerCase().includes(searchTerm)) ||
                    (lead.lead_details.purchase_timeline && lead.lead_details.purchase_timeline.toLowerCase().includes(searchTerm))
                );
            });
            
            updateLeadTable(filteredLeads);
        }
        
        // Update the lead table
        function updateLeadTable(data = leadData) {
            const tableBody = document.getElementById('leadTableBody');
            tableBody.innerHTML = '';
            
            // Sort leads by created_at in descending order
            const sortedLeads = [...data].sort((a, b) => {
                return new Date(b.created_at) - new Date(a.created_at);
            });
            
            if (sortedLeads.length === 0) {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td colspan="7" class="text-center py-4">No leads found</td>
                `;
                tableBody.appendChild(row);
                return;
            }
            
            // Create table rows
            sortedLeads.forEach((lead, index) => {
                const row = document.createElement('tr');
                
                // Add a class based on lead category
                if (lead.category === 'Hot Lead') {
                    row.classList.add('lead-hot');
                } else if (lead.category === 'Warm Lead') {
                    row.classList.add('lead-warm');
                } else {
                    row.classList.add('lead-cold');
                }
                
                // Format date
                const createdDate = new Date(lead.created_at);
                const formattedDate = createdDate.toLocaleDateString() + ' ' + 
                                    createdDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                
                // Animation delay based on index
                row.style.animationDelay = `${index * 0.05}s`;
                
                // Create table cells
                row.innerHTML = `
                    <td>${lead.phone_number}</td>
                    <td><span class="badge ${getCategoryBadgeClass(lead.category)}">${lead.category}</span></td>
                    <td class="lead-score">${lead.total_score}/50</td>
                    <td>${lead.lead_details.primary_use || 'Unknown'}</td>
                    <td>${lead.lead_details.purchase_timeline || 'Unknown'}</td>
                    <td>${formattedDate}</td>
                    <td>
                        <button class="btn btn-sm btn-primary view-details" data-lead-id="${lead.id}">
                            <i class="bi bi-eye"></i> Details
                        </button>
                    </td>
                `;
                
                tableBody.appendChild(row);
            });
            
            // Add event listeners to the view details buttons
            document.querySelectorAll('.view-details').forEach(button => {
                button.addEventListener('click', () => {
                    const leadId = button.getAttribute('data-lead-id');
                    showLeadDetails(leadId);
                });
            });
        }
        
        // Show lead details in a modal
        function showLeadDetails(leadId) {
            const lead = leadData.find(lead => lead.id == leadId);
            if (!lead) return;
            
            // Show loading indicator for modal content
            document.getElementById('qualificationDetails').innerHTML = '<div class="text-center py-4"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div><p class="mt-2">Loading report...</p></div>';
            
            // Show the modal first, then populate content (improves perceived performance)
            const modal = new bootstrap.Modal(document.getElementById('leadDetailModal'));
            modal.show();
            
            // Populate basic data immediately
            document.getElementById('modalPhoneNumber').textContent = lead.phone_number;
            
            // Set category with appropriate styling
            const categoryElement = document.getElementById('modalCategory');
            categoryElement.textContent = lead.category;
            
            if (lead.category === 'Hot Lead') {
                categoryElement.style.color = chartColors.hot.primary;
            } else if (lead.category === 'Warm Lead') {
                categoryElement.style.color = chartColors.warm.primary;
            } else {
                categoryElement.style.color = chartColors.cold.primary;
            }
            
            // Use setTimeout to render the rest of the content after modal is shown
            // This prevents UI blocking and improves responsiveness
            setTimeout(() => {
                // Populate score table
                populateScoreTable(lead);
                
                // Render markdown content
                renderLeadQualificationReport(lead.qualification_report || '');
            }, 50);
        }
        
        // Specialized function to render lead qualification report markdown
        function renderLeadQualificationReport(reportMarkdown) {
            const qualificationElement = document.getElementById('qualificationDetails');
            
            // Clear existing content
            qualificationElement.innerHTML = '';
            
            // Debug logging
            console.log("Raw report:", reportMarkdown);
            
            // Create toggle for raw/rendered view
            const toggleContainer = document.createElement('div');
            toggleContainer.className = 'mb-3 d-flex justify-content-end';
            toggleContainer.innerHTML = `
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="reportViewToggle">
                    <label class="form-check-label" for="reportViewToggle">View Raw Report</label>
                </div>
            `;
            qualificationElement.appendChild(toggleContainer);
            
            // Create containers for rendered and raw views
            const renderedContainer = document.createElement('div');
            renderedContainer.id = 'renderedReportView';
            
            const rawContainer = document.createElement('div');
            rawContainer.id = 'rawReportView';
            rawContainer.className = 'd-none';
            rawContainer.style.whiteSpace = 'pre-wrap';
            rawContainer.style.fontFamily = 'monospace';
            rawContainer.style.fontSize = '0.9rem';
            rawContainer.style.padding = '1rem';
            rawContainer.style.background = '#f8f9fa';
            rawContainer.style.border = '1px solid #dee2e6';
            rawContainer.style.borderRadius = '0.25rem';
            
            qualificationElement.appendChild(renderedContainer);
            qualificationElement.appendChild(rawContainer);
            
            // Process the markdown content
            let processedMarkdown = reportMarkdown || 'No report available';
            
            // Improved backtick handling logic
            // First, remove any language hints that might be after the backticks (e.g., ```markdown)
            processedMarkdown = processedMarkdown.replace(/```\w+/g, '```');
            
            // If the entire content is wrapped in triple backticks, remove them
            if (processedMarkdown.trim().startsWith('```') && processedMarkdown.trim().endsWith('```')) {
                processedMarkdown = processedMarkdown.trim();
                // Remove the first ``` and the last ```
                processedMarkdown = processedMarkdown.substring(3, processedMarkdown.length - 3).trim();
            } else {
                // If there are multiple code blocks, just remove all backticks entirely
                // This ensures we don't lose content by incorrect substring extraction
                processedMarkdown = processedMarkdown.replace(/```/g, '').trim();
            }
            
            // Special handling for markdown tables with line breaks
            // First, identify table sections
            const tableRegex = /\|[\s\S]*?\|[\s\S]*?\|/g;
            const tableSections = processedMarkdown.match(tableRegex);
            
            if (tableSections) {
                // For each table section
                tableSections.forEach(tableSection => {
                    // Split into lines
                    const tableLines = tableSection.split('\n');
                    const cleanedLines = [];
                    
                    // Process each line
                    tableLines.forEach(line => {
                        // Keep only non-empty lines with pipe symbols
                        if (line.trim() && line.includes('|')) {
                            cleanedLines.push(line.trim());
                        }
                    });
                    
                    // Rejoin the cleaned table
                    const cleanedTable = cleanedLines.join('\n');
                    
                    // Replace the original messy table with the cleaned one
                    processedMarkdown = processedMarkdown.replace(tableSection, cleanedTable);
                });
            }
            
            // Ensure proper spacing for table rows
            processedMarkdown = processedMarkdown.replace(/\|\s*\n\s*\|/g, '|\n|');
            
            console.log("Processed markdown:", processedMarkdown);
            
            // Store raw content
            rawContainer.textContent = processedMarkdown;
            
            try {
                // Configure marked options for proper tables
                marked.setOptions({
                    breaks: true,
                    gfm: true,
                    tables: true,
                    headerIds: false,
                    silent: true,
                    smartLists: true,
                    smartypants: true,
                    mangle: false
                });
                
                // Render markdown
                renderedContainer.innerHTML = marked.parse(processedMarkdown);
                console.log("Rendered HTML:", renderedContainer.innerHTML);
                
                // Enhance tables with Bootstrap styling
                const tables = renderedContainer.querySelectorAll('table');
                console.log("Found tables:", tables.length);
                tables.forEach(table => {
                    table.classList.add('table', 'table-bordered', 'table-hover', 'mb-4');
                    table.style.width = '100%';
                    
                    // Wrap table in responsive container
                    const wrapper = document.createElement('div');
                    wrapper.className = 'table-responsive mb-4';
                    table.parentNode.insertBefore(wrapper, table);
                    wrapper.appendChild(table);
                    
                    // Style headers
                    const headers = table.querySelectorAll('th');
                    headers.forEach(header => {
                        header.style.backgroundColor = '#f8f9fa';
                        header.style.fontWeight = 'bold';
                    });
                    
                    // Style table cells for better readability
                    const cells = table.querySelectorAll('td');
                    cells.forEach(cell => {
                        cell.style.verticalAlign = 'middle';
                    });
                });
                
                // Style headings
                const headings = renderedContainer.querySelectorAll('h1, h2, h3, h4, h5, h6');
                headings.forEach(heading => {
                    heading.style.color = '#222';
                    heading.style.marginTop = '1.5rem';
                    heading.style.marginBottom = '1rem';
                    heading.style.fontWeight = 'bold';
                    
                    if (heading.tagName === 'H1') {
                        heading.style.fontSize = '1.8rem';
                        heading.style.borderBottom = '2px solid #eee';
                        heading.style.paddingBottom = '0.5rem';
                    } else if (heading.tagName === 'H2') {
                        heading.style.fontSize = '1.5rem';
                        heading.style.borderBottom = '1px solid #eee';
                        heading.style.paddingBottom = '0.5rem';
                    }
                });
                
                // Style lists
                const lists = renderedContainer.querySelectorAll('ul, ol');
                lists.forEach(list => {
                    list.style.marginBottom = '1rem';
                    list.style.paddingLeft = '2rem';
                });
                
                // Style list items
                const listItems = renderedContainer.querySelectorAll('li');
                listItems.forEach(item => {
                    item.style.marginBottom = '0.5rem';
                });
                
            } catch (error) {
                console.error('Error rendering markdown:', error);
                renderedContainer.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        Error rendering report. Please check the raw report view.
                    </div>
                    <pre class="p-3 bg-light">${processedMarkdown}</pre>
                `;
            }
            
            // Add toggle event listener
            document.getElementById('reportViewToggle').addEventListener('change', function() {
                if (this.checked) {
                    renderedContainer.classList.add('d-none');
                    rawContainer.classList.remove('d-none');
                } else {
                    renderedContainer.classList.remove('d-none');
                    rawContainer.classList.add('d-none');
                }
            });
        }
        
        // Populate the score table
        function populateScoreTable(lead) {
            const scoreTableBody = document.getElementById('scoreTableBody');
            scoreTableBody.innerHTML = '';
            
            // Set the total score
            document.getElementById('modalTotalScore').textContent = lead.total_score || 0;
            
            // Define score categories and labels with icons
            const scoreCategories = [
                { key: 'primary_use', label: 'Primary Use', maxScore: 10, icon: 'bi-bullseye' },
                { key: 'model_selection', label: 'Model Selection', maxScore: 10, icon: 'bi-grid-3x3-gap' },
                { key: 'purchase_timeline', label: 'Purchase Timeline', maxScore: 10, icon: 'bi-calendar-range' },
                { key: 'budget_range', label: 'Budget Range', maxScore: 10, icon: 'bi-cash-stack' },
                { key: 'interest_level', label: 'Interest Level', maxScore: 10, icon: 'bi-graph-up-arrow' }
            ];
            
            // Get score class based on score
            const getScoreClass = (score, maxScore) => {
                const percentage = (score / maxScore) * 100;
                if (percentage >= 80) return 'excellent';
                if (percentage >= 50) return 'good';
                return 'needs-improvement';
            };
            
            // Get progress bar gradient based on score
            const getProgressGradient = (score, maxScore) => {
                const percentage = (score / maxScore) * 100;
                if (percentage >= 80) return 'linear-gradient(to right, #10b981, #34d399)';
                if (percentage >= 50) return 'linear-gradient(to right, #f59e0b, #fbbf24)';
                return 'linear-gradient(to right, #ef4444, #f87171)';
            };
            
            // Add rows to the table
            scoreCategories.forEach(category => {
                const score = lead.scores[category.key] || 0;
                const scoreClass = getScoreClass(score, category.maxScore);
                const progressGradient = getProgressGradient(score, category.maxScore);
                const progressWidth = (score / category.maxScore) * 100;
                
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <div class="score-criteria">
                            <div class="score-criteria-icon">
                                <i class="bi ${category.icon}"></i>
                            </div>
                            <div class="score-criteria-label">${category.label}</div>
                        </div>
                    </td>
                    <td class="text-center">
                        <span class="score-value ${scoreClass}">${score}</span>
                        <span class="score-max">/ ${category.maxScore}</span>
                    </td>
                    <td>
                        <div class="score-progress">
                            <div class="score-progress-bar" style="width: ${progressWidth}%; background: ${progressGradient}"></div>
                        </div>
                    </td>
                `;
                
                scoreTableBody.appendChild(row);
            });
        }
        
        // Helper function to parse markdown for PDF
        function parseMarkdown(markdown) {
            // Remove markdown formatting for PDF
            return markdown
                .replace(/\*\*(.*?)\*\*/g, '$1') // Bold
                .replace(/\*(.*?)\*/g, '$1')     // Italic
                .replace(/#{1,6}\s+(.+)/g, '$1') // Headers
                .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '$1 ($2)') // Links
                .replace(/^\s*[-*+]\s+(.+)/gm, '• $1') // List items
                .replace(/^\s*\d+\.\s+(.+)/gm, '• $1') // Numbered lists
                .replace(/`([^`]+)`/g, '$1'); // Code
        }
        
        // Helper function to get badge class based on category
        function getCategoryBadgeClass(category) {
            switch (category) {
                case 'Hot Lead': return 'bg-danger';
                case 'Warm Lead': return 'bg-warning';
                case 'Cold Lead': return 'bg-primary';
                default: return 'bg-secondary';
            }
        }
    </script>
</body>
</html> 