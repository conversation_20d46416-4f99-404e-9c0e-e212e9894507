package sample

import (
	// schedular "apps/schedulars/controller/schedular"
	"fmt"
	shared "libs/shared"
)

type Service interface {
	InitiateSchedular(schedularName string, metaData shared.ApiMetaData) error
}

type service struct {
	// db shared.MongoRepositoryFunctions
}

var newServiceObj *service //singleton object

// singleton function
func NewService() *service {
	if newServiceObj != nil {
		return newServiceObj
	}
	// new_db := shared.MongoRepository()

	// newServiceObj = &service{new_db}
	return newServiceObj
}

func (s *service) InitiateSchedular(schedularName string, metaData shared.ApiMetaData) error {
	fmt.Println("=======>>> Manual Schedular initiated <<<=========", schedularName)

	if schedularName == "INFRA_REGION_UPDATE" {
		fmt.Printf("INFRA_REGION_UPDATE started\n")
	} 
	return nil
}
