import os
import logging
import sqlite3
import sys

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

def run_migration():
    """
    Run a database migration to add the is_active column to the lead_qualification_results table
    if it doesn't already exist.
    """
    try:
        # Get database path from environment variable or use default
        database_path = os.environ.get('DATABASE_URL', 'sqlite:///persistent_sessions.db')
        
        # Extract the SQLite database path from the URL
        if database_path.startswith('sqlite:///'):
            database_path = database_path[len('sqlite:///'):]
        
        logger.info(f"Running migration for database: {database_path}")
        
        # Check if the file exists
        if not os.path.exists(database_path):
            logger.error(f"Database file does not exist: {database_path}")
            return False
        
        # Connect to the database
        conn = sqlite3.connect(database_path)
        cursor = conn.cursor()
        
        # Check if the table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='lead_qualification_results'")
        table_exists = cursor.fetchone() is not None
        
        if not table_exists:
            logger.info("Table lead_qualification_results does not exist yet, no migration needed")
            conn.close()
            return True
        
        # Check if the column already exists
        cursor.execute(f"PRAGMA table_info(lead_qualification_results)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        if 'is_active' in column_names:
            logger.info("Column is_active already exists, no migration needed")
            conn.close()
            return True
        
        # Add the is_active column to the table
        logger.info("Adding is_active column to lead_qualification_results table")
        cursor.execute("ALTER TABLE lead_qualification_results ADD COLUMN is_active BOOLEAN DEFAULT 1")
        
        # Set all existing records to active
        cursor.execute("UPDATE lead_qualification_results SET is_active = 1")
        
        # Commit the changes
        conn.commit()
        logger.info("Migration completed successfully")
        
        # Close the connection
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"Error running migration: {str(e)}", exc_info=True)
        return False

if __name__ == "__main__":
    success = run_migration()
    sys.exit(0 if success else 1) 