package whatsapp

import (
	Lookupcode "apps/spiro/controllers/lookup_type"
	"time"

	"gorm.io/datatypes"
)

type BasicModelDto struct {
	ID          uint      `json:"id"`
	IsEnabled   *bool     `json:"is_enabled"`
	IsActive    *bool     `json:"is_active"`
	CreatedByID *uint     `json:"created_by"`
	UpdatedDate time.Time `json:"updated_date"`
	UpdatedByID *uint     `json:"updated_by"`
	CreatedDate time.Time `json:"created_date"`
	CompanyId   uint      `json:"company_id"`
}
type SuccessResponseWithPagination struct {
	Pagination struct {
		PerPage    int `json:"per_page"`
		PageNo     int `json:"page_no"`
		TotalRows  int `json:"total_rows"`
		TotalPages int `json:"total_pages"`
	} `json:"pagination"`
	Data  []map[string]interface{} `json:"data"`
}


type SampleCreatePayloadDto struct {
	LookupType string `json:"lookup_type"`
	// LookupCode  string `json:"lookup_code"`
	DisplayName string `json:"display_name"`
	// IsActive    *bool  `json:"is_active"`
}

type SampleCreateorUpdateRespDto struct {
	Id uint `json:"id"`
}

type SampleUpdatePayloadDto struct {
	LookupType string `json:"lookup_type,omitempty"`
	// LookupCode  string `json:"lookup_code,omitempty"`
	DisplayName string `json:"display_name,omitempty"`
	// IsActive    *bool  `json:"is_active,omitempty"`
}
type SampleGetRespDto struct {
	Id uint `json:"id"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type RasaFlow struct {
	Id               uint           `json:"id"`
	Name             string         `json:"name"`
	Code             string         `json:"code" gorm:"unique;"`
	Description      string         `json:"description"`
	Features         datatypes.JSON `json:"features"`
	Icon             string         `json:"icon"`
	TrainingDataPath string         `json:"training_data_path"`
}

type AccountResponse struct {
	BasicModelDto
	BusinessAccountName string                   `json:"business_account_name"`
	TimeZone            string                   `json:"time_zone"`
	WhatsappNumber      string                   `json:"whatsapp_number"`
	WhatsappToken       string                   `json:"whatsapp_token"`
	PhoneId             string                   `json:"phone_id"`
	WabaId              string                   `json:"waba_id"`
	WabaAppId           string                   `json:"waba_app_id"`
	RasaUrl             string                   `json:"rasa_url"`
	MiddlewareUrl       string                   `json:"middleware_url"`
	MiddlewareToken     string                   `json:"middleware_token"`
	RasaFlowId          *uint                    `json:"rasa_flow_id"`
	RasaFlow            RasaFlow                 `json:"rasa_flow"`
	RasaToken           string                   `json:"rasa_token"`
	ModelPath           string                   `json:"model_path"`
	TrainingStatus      string                   `json:"training_status"`
	WhatsappAppId       *uint                    `json:"whatsapp_app_id"`
	WhatsappApp         Lookupcode.LookupCodeDTO `json:"whatsapp_app"`
	BusinessSignupCode  string                   `json:"business_signup_code"`
}

type ContactResponse struct {
	BasicModelDto
	WhatsappAccountId uint           `json:"whatsapp_account_id"`
	WhatsappNumber    string         `json:"whatsapp_number"`
	Name              string         `json:"name"`
	Context           string         `json:"context"`
	Broadcast         *bool          `json:"broadcast"`
	SmsEnable         *bool          `json:"sms_enable"`
	WhatsappEnable    *bool          `json:"whatsapp_enable"`
	EmailEnable       *bool          `json:"email_enable"`
	Notes             datatypes.JSON `json:"notes"`
	CustomParams      datatypes.JSON `json:"custom_params"`
	Email             string         `json:"email"`
	Tags              []struct {
		ID   uint   `json:"id"`
		Name string `json:"name"`
	} `json:"tags"`
	Messages         []MessageResponse        `json:"messages"`
	Summaries        []SummaryResponse        `json:"summaries"`
	StatusId         *uint                    `json:"status_id"`
	Status           Lookupcode.LookupCodeDTO `json:"status"`
	MiddlewareUserId string                   `json:"middleware_user_id"`
	ActiveUser       *bool                    `json:"active_user"`
}

type MessageResponse struct {
	BasicModelDto
	WhatsappContactId     uint           `json:"whatsapp_contact_id"`
	MessageId             string         `json:"message_id"`
	Message               datatypes.JSON `json:"message"`
	MessageAdditionalInfo datatypes.JSON `json:"message_additional_info"`
	// Type                  string         `json:"type"`
	FromUser *bool `json:"from_user"`
}

type SummaryResponse struct {
	BasicModelDto
	WhatsappContactId uint   `json:"whatsapp_contact_id"`
	Summary           string `json:"summary"`
}

type SampleListRespDto struct {
	Id         uint   `json:"id"`
	LookupType string `json:"lookup_type"`
	// LookupCode  string `json:"lookup_code"`
	DisplayName string `json:"display_name"`
}

type WebhookRequest struct {
	Object string `json:"object"`
	Entry  []struct {
		ID      string `json:"id"`
		Changes []struct {
			Value struct {
				MessagingProduct string `json:"messaging_product"`
				Metadata         struct {
					DisplayPhoneNumber string `json:"display_phone_number"`
					PhoneNumberID      string `json:"phone_number_id"`
				} `json:"metadata"`
				Contacts []struct {
					Profile struct {
						Name string `json:"name"`
					} `json:"profile"`
					WaID string `json:"wa_id"`
				} `json:"contacts"`
				Messages []MessageFromWhatsapp `json:"messages"`
				Statuses []MessageStatus       `json:"statuses"`
			} `json:"value"`
			Field string `json:"field"`
		} `json:"changes"`
	} `json:"entry"`
}

type MessageStatus struct {
	Id          string                 `json:"id"`
	Type        string                 `json:"type"`
	Status      string                 `json:"status"`
	RecipientId string                 `json:"recipient_id"`
	Payment     map[string]interface{} `json:"payment"`
}

type MessageFromWhatsapp struct {
	From        string                          `json:"from,omitempty"`
	Id          string                          `json:"id,omitempty"`
	Timestamp   string                          `json:"timestamp,omitempty"`
	Type        string                          `json:"type,omitempty"`
	Text        *MessageFromWhatsappText        `json:"text,omitempty"`
	Interactive *MessageFromWhatsappInteractive `json:"interactive,omitempty"`
	Location    *MessageFromWhatsappLocation    `json:"location,omitempty"`
	Image       *MessageFromWhatsappMedia       `json:"image,omitempty"`
	Audio       *MessageFromWhatsappMedia       `json:"audio,omitempty"`
	Document    *MessageFromWhatsappDocument    `json:"document,omitempty"`
}
type MessageFromWhatsappText struct {
	Body string `json:"body,omitempty"`
}
type MessageFromWhatsappInteractive struct {
	Type        string                          `json:"type,omitempty"`
	ListReply   *MessageFromWhatsappListReply   `json:"list_reply,omitempty"`
	ButtonReply *MessageFromWhatsappButtonReply `json:"button_reply,omitempty"`
	NfmReply    *MessageFromWhatsappNfmReply    `json:"nfm_reply,omitempty"`
}
type MessageFromWhatsappLocation struct {
	Latitude  interface{} `json:"latitude"`
	Longitude interface{} `json:"longitude"`
	Name      string      `json:"name"`
	Address   string      `json:"address"`
}
type MessageFromWhatsappButtonReply struct {
	Id    string `json:"id,omitempty"`
	Title string `json:"title,omitempty"`
}

type MessageFromWhatsappListReply struct {
	Id          string `json:"id,omitempty"`
	Title       string `json:"title,omitempty"`
	Description string `json:"description,omitempty"`
}

type MessageFromWhatsappNfmReply struct {
	ResponseJson string `json:"response_json,omitempty"`
	Body         string `json:"body,omitempty"`
	Name         string `json:"name,omitempty"`
}

type MessageFromWhatsappMedia struct {
	Caption  string `json:"caption,omitempty"`
	MimeType string `json:"mime_type,omitempty"`
	SHA256   string `json:"sha256,omitempty"`
	ID       string `json:"id,omitempty"`
	Url      string `json:"url"`
	Base64Data string `json:"base64_data"`
}

type MessageFromWhatsappDocument struct {
	MimeType 	string `json:"mime_type,omitempty"`
	Caption  	string `json:"caption,omitempty"`
	FileName 	string `json:"file_name,omitempty"`
	SHA256   	string `json:"sha256,omitempty"`
	ID       	string `json:"id,omitempty"`
	Url      	string `json:"url"`
	Base64Data 	string `json:"base64_data"`
}

type SendMessageRequest struct {
	ContactId uint   `json:"contact_id"`
	Type      string `json:"type"`
	Text      string `json:"text"`
	Link      string `json:"link"`
}

type SendNotificationRequest struct {
	ContactNumber string                 `json:"contact_number"`
	Template      map[string]interface{} `json:"template"`
	TemplateData  map[string]interface{} `json:"template_data"`
}

type ChatbotNotificationRequest struct {
	WABANumber string `json:"waba_number"`
	UserNumber string `json:"user_number"`
	Type       string `json:"type"`
	Text       string `json:"text"`
	Link       string `json:"link"`
}

//------------------------RASA DTOs------------------------------

type RasaResponse struct {
	RecipientId   string               `json:"recipient_id"`
	Text          string               `json:"text"`
	Image         string               `json:"image"`
	Buttons       []RasaButton         `json:"buttons"`
	Custom        *CustomRasaResponse  `json:"custom"`
	Flow          *FlowFormat          `json:"flow"`
	Payment       *PaymentFormat       `json:"payment_format"`
	PaymentStatus *PaymentStatusFormat `json:"payment_status"`
	Template      string               `json:"template"`
}

type RasaButton struct {
	Title   string `json:"title"`
	Payload string `json:"payload"`
}

type CustomRasaResponse struct {
	Text    string       `json:"text"`
	Image   string       `json:"image"`
	Buttons []RasaButton `json:"buttons"`
}

//---------------------VERIFY WEBHOOK------------------------------

type VerifyWebhookRequest struct {
	Mode        string `query:"hub.mode"`
	VerifyToken string `query:"hub.verify_token"`
	Challenge   int    `query:"hub.challenge"`
}

//---------------------Logs Dtos-----------------------------------

type RasaResponseResponse struct {
	Id                uint           `json:"id"`
	WhatsappAccountId uint           `json:"whatsapp_account_id"`
	RasaUrl           string         `json:"rasa_url"`
	RecipientId       string         `json:"recipient_id"`
	Message           string         `json:"message"`
	Response          datatypes.JSON `json:"response"`
}

type LlmResponseResponse struct {
	Id                uint           `json:"id"`
	WhatsappAccountId uint           `json:"whatsapp_account_id"`
	LlmUrl            string         `json:"llm_url"`
	RecipientId       string         `json:"recipient_id"`
	Message           string         `json:"message"`
	Response          datatypes.JSON `json:"response"`
}

//---------------------LLM Attribute DTO-----------------------------------

type ProductAttributeData struct {
	ProductName string
	ImageUrl    string
	IsCompleted bool
	Attributes  []AttributeData
}

type AttributeData struct {
	AttributeName   string
	AttributeValues []string
	SelectedValue   string
}

// ---------------------GPS Location DTO-----------------------------------
type GpsLocation struct {
	Latitude  string `json:"latitude"`
	Longitude string `json:"longitude"`
	City      string `json:"city"`
	State     string `json:"state"`
	Pincode   string `json:"pincode"`
}

// --------------------------------Whatsapp Payments-----------------------------------------
type ConfirmPaymentApiResponse struct {
	Payments []struct {
		ReferenceId string `json:"reference_id"`
		Status      string `json:"status"`
	} `json:"payments"`
}

type WhatsappOpenaiPromtResponse struct {
	Name           string         `json:"name"`
	Promt          string         `json:"promt"`
	Functions      datatypes.JSON `json:"functions"`
	FunctionChoice string         `json:"function_choice"`
	Temperature    *float32       `json:"temperature"`
}

type MessageRequest struct {
	WhatsappAccountNumber      string                   `json:"whatsapp_account_number"`
	ContactNumber              string                   `json:"contact_number"`
}

type IntialTemplateRequest struct {
	WhatsappAccountNumber      string                   `json:"whatsapp_account_number"`
	FilePath                  string                   `json:"file_path"`          
}

type MessageResponses struct {
	ID          uint      `json:"id"`
	CreatedDate time.Time `json:"created_date"`
	WhatsappContactId     uint           `json:"whatsapp_contact_id"`
	Message               datatypes.JSON `json:"message"`
	FromUser *bool `json:"from_user"`
}

type NationalIdsResponse struct {
	ID          	  	uint      			  `json:"id"`
	CreatedDate 	  	time.Time 			  `json:"created_date"`
	WhatsappAccountId 	uint           		  `json:"whatsapp_account_id"`
	WhatsappNumber    	string         		  `json:"whatsapp_number"`
	Name              	string         		  `json:"name"`
	Notes             	datatypes.JSON 		  `json:"notes"`
	Email             	string         		  `json:"email"`
	ActiveUser       	*bool                 `json:"active_user"`
	NationalId       	string                `json:"national_id"`
}
