package whatsapp_contacts

import (
	"time"
	"gorm.io/datatypes"
)

type BasicModelDto struct {
	ID          uint      `json:"id"`
	IsEnabled   *bool     `json:"is_enabled"`
	IsActive    *bool     `json:"is_active"`
	CreatedByID *uint     `json:"created_by"`
	UpdatedDate time.Time `json:"updated_date"`
	UpdatedByID *uint     `json:"updated_by"`
	CreatedDate time.Time `json:"created_date"`
	CompanyId   uint      `json:"company_id"`
}

type LookupCodesDTO struct {
	Id         uint   `json:"id"`
	LookupCode string `json:"lookup_code"`
	Name       string `json:"display_name"`
	SourceCode string `json:"source_code,omitempty"`
}
type CreatePayloadDto struct {
	CompanyId             uint               `json:"company_id"`
	WhatsappAccountId     uint               `json:"whatsapp_account_id"`
	WhatsappNumber        string            `json:"whatsapp_number"`
	Name                  string            `json:"name"`
	Broadcast             *bool             `json:"broadcast"`
	SmsEnable             *bool             `json:"sms_enable"`
	WhatsappEnable        *bool             `json:"whatsapp_enable"`
	EmailEnable           *bool             `json:"email_enable"`
	Notes                 datatypes.JSON    `json:"notes"`
	CustomParams          datatypes.JSON    `json:"custom_params"`
	Email                 string            `json:"email"`
	Context               string            `json:"context"`
	StatusId              *uint             `json:"status_id"`
	MiddlewareUserId      string            `json:"middleware_user_id"`
	ActiveUser            *bool             `json:"active_user" gorm:"default:false"`
	ChatHistoryForSummary string            `json:"chat_history_for_summary"`
	UserReferenceId       string             `json:"user_reference_id"`
	NationalId            string             `json:"national_id"`
}

type CreateorUpdateRespDto struct {
	Id uint `json:"id"`
}


type UpdatePayloadDto struct {
	CompanyId             uint               `json:"company_id"`
	WhatsappAccountId     uint               `json:"whatsapp_account_id"`
	WhatsappNumber        string            `json:"whatsapp_number"`
	Name                  string            `json:"name"`
	Broadcast             *bool             `json:"broadcast"`
	SmsEnable             *bool             `json:"sms_enable"`
	WhatsappEnable        *bool             `json:"whatsapp_enable"`
	EmailEnable           *bool             `json:"email_enable"`
	Notes                 datatypes.JSON    `json:"notes"`
	CustomParams          datatypes.JSON    `json:"custom_params"`
	Email                 string            `json:"email"`
	Context               string            `json:"context"`
	StatusId              *uint             `json:"status_id"`
	MiddlewareUserId      string            `json:"middleware_user_id"`
	ActiveUser            *bool             `json:"active_user" gorm:"default:false"`
	ChatHistoryForSummary string            `json:"chat_history_for_summary"`
	UserReferenceId       string             `json:"user_reference_id"`
	NationalId            string             `json:"national_id"`
}
type GetRespDto struct {
	BasicModelDto
	WhatsappAccountId     uint               `json:"whatsapp_account_id"`
	WhatsappNumber        string            `json:"whatsapp_number"`
	Name                  string            `json:"name"`
	Broadcast             *bool             `json:"broadcast"`
	SmsEnable             *bool             `json:"sms_enable"`
	WhatsappEnable        *bool             `json:"whatsapp_enable"`
	EmailEnable           *bool             `json:"email_enable"`
	Notes                 datatypes.JSON    `json:"notes"`
	CustomParams          datatypes.JSON    `json:"custom_params"`
	Email                 string            `json:"email"`
	Context               string            `json:"context"`
	StatusId              *uint             `json:"status_id"`
	Status                LookupCodesDTO    `json:"status"`
	MiddlewareUserId      string            `json:"middleware_user_id"`
	ActiveUser            *bool             `json:"active_user" gorm:"default:false"`
	ChatHistoryForSummary string            `json:"chat_history_for_summary"`
	UserReferenceId       string             `json:"user_reference_id"`
	NationalId            string             `json:"national_id"`
}

type ListRespDto struct {
	BasicModelDto
	WhatsappAccountId     uint               `json:"whatsapp_account_id"`
	WhatsappNumber        string            `json:"whatsapp_number"`
	Name                  string            `json:"name"`
	Broadcast             *bool             `json:"broadcast"`
	SmsEnable             *bool             `json:"sms_enable"`
	WhatsappEnable        *bool             `json:"whatsapp_enable"`
	EmailEnable           *bool             `json:"email_enable"`
	Notes                 datatypes.JSON    `json:"notes"`
	CustomParams          datatypes.JSON    `json:"custom_params"`
	Email                 string            `json:"email"`
	Context               string            `json:"context"`
	StatusId              *uint             `json:"status_id"`
	MiddlewareUserId      string            `json:"middleware_user_id"`
	ActiveUser            *bool             `json:"active_user" gorm:"default:false"`
	ChatHistoryForSummary string            `json:"chat_history_for_summary"`
	UserReferenceId       string             `json:"user_reference_id"`
	NationalId            string             `json:"national_id"`
}

