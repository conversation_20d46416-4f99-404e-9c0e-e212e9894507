from flask import Blueprint, render_template, request, jsonify, session
import os
import logging
from app.services.pipeline_service import SalePipeline
from app.services.image_service import handle_image_upload
from app.services.session_service import SessionManager
from app.services.lead_qualification_service import process_lead_qualification, get_lead_qualification_by_phone, list_lead_qualifications
import base64
import time
import requests
from io import BytesIO
import re

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# Create blueprint
main = Blueprint('main', __name__)

# Create a dictionary to store active pipelines by session ID
active_pipelines = {}

def get_pipeline():
    """Get the current pipeline instance for the session"""
    session_id = session.get('session_id')
    if not session_id or session_id not in active_pipelines:
        return None
    return active_pipelines[session_id]

@main.route('/')
def index():
    """Render the main chat interface"""
    # Generate a unique session ID if not already set
    if 'session_id' not in session:
        session['session_id'] = os.urandom(16).hex()
    
    return render_template('pipeline_chat.html')

@main.route('/api/pipeline/start', methods=['POST'])
def start_conversation():
    """Initialize a new conversation pipeline or fetch an existing one"""
    try:
        # Get the payload data (if available)
        data = request.json if request.is_json else {}
        
        # Log the payload
        if data:
            logger.info(f"START CONVERSATION PAYLOAD: {data}")
        
        # Get phone number from various formats
        phone_number = None
        
        # First check direct 'from' field in the request
        if data and data.get('from'):
            phone_number = data.get('from')
        # Then check WhatsApp format 
        elif data and 'messages' in data and isinstance(data.get('messages'), dict):
            phone_number = data.get('messages', {}).get('from')
            
        # Check if we got a phone number
        if not phone_number:
            error_response = {
                "success": False,
                "error": "No phone number provided",
                "message": "A phone number is required to start the conversation"
            }
            return jsonify(error_response), 400, {'Content-Type': 'application/json'}
            
        # Get the action (fetch or start)
        action = data.get('action', 'start')
        
        # Check for force_restart flag
        force_restart = data.get('force_restart', True)  # Default to True for safety
        
        pipeline = None
        is_new_session = False
        
        # If force_restart is True, always create a new pipeline
        if force_restart:
            pipeline = SalePipeline()
            is_new_session = True
            logger.info(f"Force restarting conversation with new pipeline for {phone_number}")
        else:
            # Try to get existing session
            existing_session = SessionManager.get_session_by_phone(phone_number)
            if existing_session:
                _, pipeline = existing_session
                logger.info(f"Retrieved existing persistent session for phone number: {phone_number}")
            
            # If no existing session found, create new one
            if not pipeline:
                pipeline = SalePipeline()
                is_new_session = True
                logger.info(f"Creating new pipeline as no existing session found for {phone_number}")
        
        # Extract reference_id from the payload if available
        reference_id = data.get('reference_id')
        if reference_id and hasattr(pipeline, 'context'):
            pipeline.context.reference_id = reference_id
            logger.info(f"Setting reference_id: {reference_id} for phone number: {phone_number}")
        
        # Initialize response
        response = pipeline.start_conversation()
        
        # Save the session with the phone number
        SessionManager.create_or_update_session(phone_number, pipeline)
        
        # Add metadata to response
        if data and 'messages' in data:
            response["whatsapp_message_id"] = data.get('messages', {}).get('id')
            response["whatsapp_from"] = phone_number
            response["timestamp"] = data.get('messages', {}).get('timestamp')
        else:
            response["whatsapp_message_id"] = f"MSG_START_{int(time.time())}_{phone_number[-6:]}"
            response["whatsapp_from"] = phone_number
            response["timestamp"] = str(int(time.time()))
        
        # Add conversation history count
        if hasattr(pipeline, 'context') and hasattr(pipeline.context, 'conversation_history'):
            response["history_count"] = len(pipeline.context.conversation_history)
        
        return jsonify(response), 200, {'Content-Type': 'application/json'}
    except Exception as e:
        logger.error(f"Error starting conversation: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": str(e),
            "message": "Sorry, I encountered an error starting our conversation."
        }), 500, {'Content-Type': 'application/json'}

@main.route('/api/pipeline/continue', methods=['POST'])
def continue_conversation():
    """Continue an existing conversation with user input"""
    try:
        # Get user message from request
        data = request.json
        
        # Log the complete payload
        logger.info(f"CONTINUE CONVERSATION PAYLOAD: {data}")
        
        # Initialize variables
        user_message = ""
        message_type = ""
        message_from = ""
        message_id = ""
        timestamp = ""
        pipeline = None
        
        # First check for phone number in standard request format
        if data.get('from'):
            message_from = data.get('from')
            user_message = data.get('message', "")
        # Extract phone number and message regardless of format
        # Check new WhatsApp format
        elif data.get('messages') and isinstance(data.get('messages'), dict):
            messages = data.get('messages', {})
            message_type = messages.get('type')
            message_from = messages.get('from')
            message_id = messages.get('id')
            timestamp = messages.get('timestamp')
            
            if message_type == 'text':
                user_message = messages.get('text', {}).get('body', '')
            elif message_type == 'image':
                image_data = messages.get('image', {})
                image_url = image_data.get('url')
                image_caption = image_data.get('caption', '')
                
                if image_url:
                    try:
                        # Check if this is a data URL (base64 encoded image)
                        if image_url.startswith('data:'):
                            # Extract the base64 data directly
                            image_data_for_processing = image_url
                        else:
                            # It's an actual URL, download it
                            image_response = requests.get(image_url)
                            if image_response.status_code == 200:
                                # Convert image to base64 for processing
                                image_bytes = BytesIO(image_response.content)
                                encoded_image = base64.b64encode(image_bytes.read()).decode('utf-8')
                                
                                # Create the image data format expected by handle_image_upload
                                mime_type = image_response.headers.get('Content-Type', 'image/jpeg')
                                image_data_for_processing = f"data:{mime_type};base64,{encoded_image}"
                            else:
                                user_message = f"[Image shared]"
                                if image_caption:
                                    user_message += f" Caption: {image_caption}"
                                return
                        
                        # Determine the query based on image caption
                        enhanced_query = """
                        FIRST, determine if this image contains a valid National ID document. Look for:
                        1. Official government-issued ID format with security features
                        2. Personal information section with name, date of birth, etc.
                        3. ID number or registration number
                        4. Official seals, watermarks, or holograms (if visible)
                        5. Government authority or issuing agency information
                        
                        If the image is NOT a valid National ID document (e.g., it's a photo of a person, a scene, a different document type like a receipt or business card), your response MUST set "success" to false and return empty strings for all fields.
                        
                        ONLY IF the image contains a valid National ID document, extract the following information in JSON format:
                        - name: Full name of the person
                        - id_number: National ID number
                        - country: Country of residence or issuing country
                        - state: State/Province
                        - city: City
                        - zip_code: Postal/ZIP code
                        - dob: Date of birth (formatted as YYYY-MM-DD if possible)
                        - gender: Gender
                        - address: Full address
                        - phone: Phone number
                        
                        IMPORTANT INSTRUCTIONS:
                        1. Your response MUST be in valid JSON format with these exact field names.
                        2. If you cannot determine if the document is a National ID with high confidence, return "success": false.
                        3. If a field is not visible, unclear or not present, leave it as an empty string.
                        4. Extract information exactly as it appears on the document without making assumptions.
                        5. For partial information, include what is visible rather than returning empty strings.
                        6. If the document is in a non-Latin script, transliterate names and addresses if possible.
                        
                        Example response format:
                        {
                          "success": true/false,
                          "name": "John Smith",
                          "id_number": "12345678",
                          "country": "United States",
                          "state": "California",
                          "city": "Los Angeles",
                          "zip_code": "90001",
                          "dob": "1990-01-01",
                          "gender": "Male",
                          "address": "123 Main St",
                          "phone": "******-123-4567"
                        }
                        """
                        if image_caption:
                            enhanced_query += f"\nImage caption: {image_caption}"
                        
                        # Process the image using image_service
                        if message_from:
                            # Try to get existing session for this phone number
                            existing_session = SessionManager.get_session_by_phone(message_from)
                            if existing_session:
                                _, pipeline = existing_session
                            else:
                                # Create a new pipeline for this phone number
                                pipeline = SalePipeline()
                                pipeline.start_conversation()  # Initialize the pipeline
                                # Save the session
                                SessionManager.create_or_update_session(message_from, pipeline)
                            
                        # PRINT FULL CONTEXT STATE BEFORE IMAGE PROCESSING IN CONTINUE
                        print("\n" + "="*80)
                        print(f"CONTEXT STATE BEFORE IMAGE PROCESSING IN CONTINUE (Phone: {message_from}):")
                        context_dict = pipeline.context.dict() if hasattr(pipeline.context, 'dict') else vars(pipeline.context)
                        
                        # Print context attributes individually for better readability
                        for key, value in context_dict.items():
                            if key == "conversation_history":
                                print(f"  {key}: [... {len(value)} messages ...]")
                            else:
                                print(f"  {key}: {value}")
                        print("="*80 + "\n")
                        
                        result = handle_image_upload(
                            image_data=image_data_for_processing,
                            pipeline_instance=pipeline,
                            query=enhanced_query
                        )
                        
                        # EXPLICITLY set the flags again to ensure they are set
                        pipeline.context.image_uploaded = True
                        pipeline.context.id_upload_complete = True
                        
                        # Log that we are forcing these flags
                        logger.info("==== FORCING IMAGE UPLOAD FLAGS IN ROUTE HANDLER ====")
                        logger.info(f"image_uploaded flag set to: {pipeline.context.image_uploaded}")
                        logger.info(f"id_upload_complete flag set to: {pipeline.context.id_upload_complete}")
                        
                        # PRINT FULL CONTEXT STATE AFTER IMAGE PROCESSING IN CONTINUE
                        print("\n" + "="*80)
                        print(f"CONTEXT STATE AFTER IMAGE PROCESSING IN CONTINUE (Phone: {message_from}):")
                        context_dict = pipeline.context.dict() if hasattr(pipeline.context, 'dict') else vars(pipeline.context)
                        
                        # Print context attributes individually for better readability
                        for key, value in context_dict.items():
                            if key == "conversation_history":
                                print(f"  {key}: [... {len(value)} messages ...]")
                            else:
                                print(f"  {key}: {value}")
                        print("="*80 + "\n")
                        
                        # Use the result from image processing
                        if result.get("success"):
                            # Extract information from image processing
                            extracted_info = result.get("extracted_info", {})
                            extracted_text = result.get("message", "")
                            
                            # Create a response that combines image processing results
                            response = {
                                "success": True,
                                "message": extracted_text,
                                "extracted_info": extracted_info,
                                "continue_flow": result.get("continue_flow", True),
                                "whatsapp_message_id": message_id,
                                "whatsapp_from": message_from,
                                "timestamp": timestamp,
                                "is_image_verified": result.get("is_image_verified", False)
                            }
                            
                            # Debug: Print the full image response
                            print("=" * 80)
                            print("IMAGE PROCESSING RESPONSE:")
                            import json
                            print(json.dumps(response, indent=2, default=str))
                            print("=" * 80)
                            
                            return jsonify(response), 200, {'Content-Type': 'application/json'}
                        else:
                            # If image processing failed, fall back to treating as text
                            user_message = f"[Image received]"
                            if image_caption:
                                user_message += f" Caption: {image_caption}"
                    except Exception as img_err:
                        logger.error(f"Error processing image: {str(img_err)}", exc_info=True)
                        user_message = f"[Image received]"
                        if image_caption:
                            user_message += f" Caption: {image_caption}"
                else:
                    user_message = f"[Image shared]"
                    if image_caption:
                        user_message += f" Caption: {image_caption}"
            else:
                user_message = f"[{message_type} message]"
        # Old WhatsApp format        
        elif data.get('type') and data.get('from') and data.get('id'):
            message_type = data.get('type')
            message_from = data.get('from')
            message_id = data.get('id')
            timestamp = data.get('timestamp')
            
            if message_type == 'text':
                user_message = data.get('text', {}).get('body', '')
        # Simple format
        else:
            user_message = data.get('message', '')
        
        if not user_message:
            error_response = {
                "success": False,
                "error": "No message provided"
            }
            print("=" * 80)
            print("ERROR RESPONSE (No message provided):")
            import json
            print(json.dumps(error_response, indent=2))
            print("=" * 80)
            return jsonify(error_response), 400, {'Content-Type': 'application/json'}
        
        # Validate we have a phone number
        if not message_from:
            error_response = {
                "success": False,
                "error": "No phone number provided",
                "message": "A phone number is required to continue the conversation"
            }
            return jsonify(error_response), 400, {'Content-Type': 'application/json'}
            
        # Always try to get pipeline from persistent sessions by phone number
        existing_session = SessionManager.get_session_by_phone(message_from)
        if existing_session:
            _, pipeline = existing_session
            logger.info(f"Retrieved existing session for phone number: {message_from}")
        else:
            # No existing session, create a new one for this phone number
            logger.info(f"Creating new session for phone number: {message_from}")
            pipeline = SalePipeline()
            # Start a new conversation
            pipeline.start_conversation()
            # Save the new session
            SessionManager.create_or_update_session(message_from, pipeline)
        
        # Extract reference_id from the payload if available
        reference_id = data.get('reference_id')
        if reference_id and hasattr(pipeline, 'context') and not pipeline.context.reference_id:
            pipeline.context.reference_id = reference_id
            logger.info(f"Setting reference_id: {reference_id} for phone number: {message_from}")
        
        # PRINT FULL CONTEXT STATE BEFORE PROCESSING
        print("\n" + "="*80)
        print(f"CONTEXT STATE BEFORE CONTINUE (Phone: {message_from}):")
        context_dict = pipeline.context.dict() if hasattr(pipeline.context, 'dict') else vars(pipeline.context)
        
        # Print context attributes individually for better readability
        for key, value in context_dict.items():
            if key == "conversation_history":
                print(f"  {key}: [... {len(value)} messages ...]")
            else:
                print(f"  {key}: {value}")
        print("="*80 + "\n")
        
        # Continue the conversation with user message
        response = pipeline.continue_conversation(user_message)
        
        # PRINT FULL CONTEXT STATE AFTER PROCESSING
        print("\n" + "="*80)
        print(f"CONTEXT STATE AFTER CONTINUE (Phone: {message_from}):")
        context_dict = pipeline.context.dict() if hasattr(pipeline.context, 'dict') else vars(pipeline.context)
        
        # Print context attributes individually for better readability
        for key, value in context_dict.items():
            if key == "conversation_history":
                print(f"  {key}: [... {len(value)} messages ...]")
            else:
                print(f"  {key}: {value}")
        print("="*80 + "\n")
        
        # Check if we've reached the end node and it's the first time
        if hasattr(pipeline.context, 'current_node_id') and pipeline.context.current_node_id == "ask_feedback":
            # Check if we've already run lead qualification for this conversation
            if not hasattr(pipeline.context, 'lead_qualification_done') or not pipeline.context.lead_qualification_done:
                # Mark that we've run lead qualification to avoid duplicate calls
                pipeline.context.lead_qualification_done = True
                
                # Save the session with the updated flag
                SessionManager.create_or_update_session(message_from, pipeline)
                
                # Trigger lead qualification in the background (don't block the response)
                try:
                    # This could be done asynchronously in a real implementation
                    # For now, we'll just call it directly
                    lead_qualification_data = process_lead_qualification(message_from)
                    
                    # Log the lead qualification result
                    logger.info(f"Lead qualification completed for {message_from}: {lead_qualification_data.get('lead_category', 'Unknown')}")
                    
                    # Add lead qualification data to the response
                    response["lead_qualification"] = {
                        "category": lead_qualification_data.get("lead_category"),
                        "report_available": True
                    }
                except Exception as qual_err:
                    logger.error(f"Error running lead qualification: {str(qual_err)}", exc_info=True)
        
        # Print the original response from pipeline
        print("=" * 80)
        import json
        #print(json.dumps(response, indent=2))
        print("=" * 80)
        
        # Check if we're on the "Display the Models" node and modify the response
        current_node_id = None
        if hasattr(pipeline, 'context'):
            current_node_id = pipeline.context.current_node_id
            
            # Debug: print the current node ID
            print(f"CURRENT NODE ID: {current_node_id}")
            
            # Special handling for display_models node
            if current_node_id == "model_selected_decision":
                # Modify the response to indicate a special flow for EV models
                # response["message_type"] = "flow"
                # response["flow_type"] = "EV_MODELS"
                
                # Make sure the current_node field is correct
                if hasattr(pipeline, 'nodes') and current_node_id in pipeline.nodes:
                    response["current_node"] = pipeline.nodes[current_node_id].name
                
                # Keep the original message in case it's needed
                response["original_message"] = response.get("message", "")
                
                # Debug: Print the modified response
                print("=" * 80)
                print("MODIFIED RESPONSE FOR DISPLAY_MODELS NODE:")
                #print(json.dumps(response, indent=2))
                print("=" * 80)
            
            # Include reference_id in the response ONLY when we're at the ask_feedback node
            # This happens right after scheduling when the user has selected a date and time
            if current_node_id == "ask_feedback" and hasattr(pipeline.context, "reference_id") and pipeline.context.reference_id:
                response["reference_id"] = pipeline.context.reference_id
                print(f"Including reference_id in response: {pipeline.context.reference_id}")
        
        # Save the updated session with new conversation history
        SessionManager.create_or_update_session(message_from, pipeline)
            
        # Add metadata to the response
        if message_id:
            response["whatsapp_message_id"] = message_id
        else:
            response["whatsapp_message_id"] = f"MSG_{int(time.time())}_{message_from[-6:]}"
            
        response["whatsapp_from"] = message_from
        response["timestamp"] = timestamp if timestamp else str(int(time.time()))
        
        # Add data_sharing_consent to the response
        if hasattr(pipeline, 'context') and hasattr(pipeline.context, 'data_sharing_consent'):
            response["data_sharing_consent"] = pipeline.context.data_sharing_consent
        
        # Print the final response to be sent to the client
        print("=" * 80)
        print("FINAL RESPONSE TO CLIENT:")
        #print(json.dumps(response, indent=2))
        print("=" * 80)
        
        return jsonify(response), 200, {'Content-Type': 'application/json'}
    except Exception as e:
        logger.error(f"Error continuing conversation: {str(e)}", exc_info=True)
        error_response = {
            "success": False,
            "error": str(e),
            "message": "Sorry, I encountered an error processing your message."
        }
        
        # Print the error response
        print("=" * 80)
        print("ERROR RESPONSE:")
        import json
        print(json.dumps(error_response, indent=2))
        print(f"Exception: {str(e)}")
        print("=" * 80)
        
        return jsonify(error_response), 500, {'Content-Type': 'application/json'}

@main.route('/api/debug/llm-calls', methods=['GET'])
def get_llm_calls():
    """Get detailed information about LLM calls for the current session"""
    try:
        session_id = session.get('session_id')
        if not session_id or session_id not in active_pipelines:
            return jsonify({
                "success": False,
                "error": "No active session found"
            }), 404
        
        pipeline = active_pipelines[session_id]
        debug_info = pipeline.get_debug_info()
        
        return jsonify({
            "success": True,
            "llm_calls": debug_info,
            "total_calls": len(debug_info)
        }), 200, {'Content-Type': 'application/json'}
    except Exception as e:
        logger.error(f"Error getting LLM call data: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500, {'Content-Type': 'application/json'}

@main.route('/flowchart')
def flowchart():
    """Display the flowchart for reference"""
    return render_template('flowchart.html')

@main.route('/api/image_upload', methods=['POST'])
def image_upload():
    """
    Endpoint to handle image upload and processing
    """
    try:
        # Log the image upload request (without the actual image data for brevity)
        data = request.get_json()
        if data:
            payload_info = {k: "data truncated" if k == "image_data" else v for k, v in data.items()}
            logger.info(f"IMAGE UPLOAD PAYLOAD: {payload_info}")
        
        # Get phone number from request - required parameter
        phone_number = data.get('phone_number') if data else None
        
        if not phone_number:
            return jsonify({
                "success": False, 
                "message": "Phone number is required for image processing",
                "extracted_info": {},
                "is_image_verified": False
            }), 200
        
        # Get image data
        if not data or 'image_data' not in data:
            return jsonify({
                "success": False, 
                "message": "No image data provided",
                "extracted_info": {},
                "is_image_verified": False
            }), 200
        
        image_data = data['image_data']
        
        # Get pipeline instance from the phone number session
        pipeline_instance = None
        
        # Try to get pipeline from persistent sessions by phone number
        existing_session = SessionManager.get_session_by_phone(phone_number)
        if existing_session:
            _, pipeline_instance = existing_session
            logger.info(f"Retrieved existing session for phone number: {phone_number}")
        else:
            # No existing session, create a new one for this phone number
            logger.info(f"Creating new session for phone number: {phone_number}")
            pipeline_instance = SalePipeline()
            # Start a new conversation
            pipeline_instance.start_conversation()
            # Save the new session
            SessionManager.create_or_update_session(phone_number, pipeline_instance)
        
        # PRINT FULL CONTEXT STATE BEFORE IMAGE PROCESSING
        print("\n" + "="*80)
        print(f"CONTEXT STATE BEFORE IMAGE UPLOAD (Phone: {phone_number}):")
        context_dict = pipeline_instance.context.dict() if hasattr(pipeline_instance.context, 'dict') else vars(pipeline_instance.context)
        
        # Print context attributes individually for better readability
        for key, value in context_dict.items():
            if key == "conversation_history":
                print(f"  {key}: [... {len(value)} messages ...]")
            else:
                print(f"  {key}: {value}")
        print("="*80 + "\n")
        
        # Create an enhanced query with context from user's conversation if available
        enhanced_query = """
        FIRST, determine if this image contains a valid National ID document. Look for:
        1. Official government-issued ID format with security features
        2. Personal information section with name, date of birth, etc.
        3. ID number or registration number
        4. Official seals, watermarks, or holograms (if visible)
        5. Government authority or issuing agency information
        
        If the image is NOT a valid National ID document (e.g., it's a photo of a person, a scene, a different document type like a receipt or business card), your response MUST set "success" to false and return empty strings for all fields.
        
        ONLY IF the image contains a valid National ID document, extract the following information in JSON format:
        - name: Full name of the person
        - id_number: National ID number
        - country: Country of residence or issuing country
        - state: State/Province
        - city: City
        - zip_code: Postal/ZIP code
        - dob: Date of birth (formatted as YYYY-MM-DD if possible)
        - gender: Gender
        - address: Full address
        - phone: Phone number
        
        IMPORTANT INSTRUCTIONS:
        1. Your response MUST be in valid JSON format with these exact field names.
        2. If you cannot determine if the document is a National ID with high confidence, return "success": false.
        3. If a field is not visible, unclear or not present, leave it as an empty string.
        4. Extract information exactly as it appears on the document without making assumptions.
        5. For partial information, include what is visible rather than returning empty strings.
        6. If the document is in a non-Latin script, transliterate names and addresses if possible.
        
        Example response format:
        {
          "success": true/false,
          "name": "John Smith",
          "id_number": "12345678",
          "country": "United States",
          "state": "California",
          "city": "Los Angeles",
          "zip_code": "90001",
          "dob": "1990-01-01",
          "gender": "Male",
          "address": "123 Main St",
          "phone": "******-123-4567"
        }
        """
        
        # Process image upload - pass user_context to provide state information for the prompt
        result = handle_image_upload(
            image_data=image_data,
            pipeline_instance=pipeline_instance,
            query=enhanced_query
        )
        
        # EXPLICITLY set the flags again to ensure they are set
        pipeline_instance.context.image_uploaded = True
        pipeline_instance.context.id_upload_complete = True
        
        # Log that we are forcing these flags
        logger.info("==== FORCING IMAGE UPLOAD FLAGS IN ROUTE HANDLER ====")
        logger.info(f"image_uploaded flag set to: {pipeline_instance.context.image_uploaded}")
        logger.info(f"id_upload_complete flag set to: {pipeline_instance.context.id_upload_complete}")
        
        # PRINT FULL CONTEXT STATE AFTER IMAGE PROCESSING
        print("\n" + "="*80)
        print(f"CONTEXT STATE AFTER IMAGE UPLOAD (Phone: {phone_number}):")
        context_dict = pipeline_instance.context.dict() if hasattr(pipeline_instance.context, 'dict') else vars(pipeline_instance.context)
        
        # Print context attributes individually for better readability
        for key, value in context_dict.items():
            if key == "conversation_history":
                print(f"  {key}: [... {len(value)} messages ...]")
            else:
                print(f"  {key}: {value}")
        print("="*80 + "\n")
        
        # Ensure result has extracted_info
        if "extracted_info" not in result:
            result["extracted_info"] = {}
        
        # Set is_image_verified based on whether it's a valid National ID and has extracted information
        is_image_verified = False
        if result.get("is_valid_national_id", False) and result["extracted_info"] and any(value for value in result["extracted_info"].values()):
            is_image_verified = True
        
        # Add is_image_verified to the result
        result["is_image_verified"] = is_image_verified
            
        # Add UI guidance based on success type
        if result.get("success", False):
            # Success - continue flow normally
            result["ui_action"] = "continue_flow"
            result["continue_flow"] = True
            result["show_upload_again"] = False
            
            # If the extraction was successful, consider getting remaining missing fields
            missing_fields = pipeline_instance.context.get_missing_fields() if hasattr(pipeline_instance.context, 'get_missing_fields') else []
            result["missing_fields"] = missing_fields
            
            # If all required fields are collected, move to the next step in the flow
            if hasattr(pipeline_instance.context, 'has_all_required_fields') and pipeline_instance.context.has_all_required_fields():
                result["has_all_required"] = True
                pipeline_instance.context.ready_to_transition = True
            else:
                result["has_all_required"] = False
        else:
            # Failed to extract information - suggest new upload
            result["ui_action"] = "suggest_new_upload"
            result["continue_flow"] = False
            result["show_upload_again"] = True
            result["show_manual_entry"] = True
            
            # Include missing fields even on failure so UI can show manual entry form
            missing_fields = pipeline_instance.context.get_missing_fields() if hasattr(pipeline_instance.context, 'get_missing_fields') else []
            result["missing_fields"] = missing_fields
            result["has_all_required"] = False
        
        # Add message metadata - create a unique message ID for tracking
        message_id = f"MSG_IMG_UPLOAD_{int(time.time())}_{phone_number[-6:] if phone_number else ''}"
        result["whatsapp_message_id"] = message_id
        result["whatsapp_from"] = phone_number
        result["timestamp"] = str(int(time.time()))
        
        # Print complete result object
        print("\n" + "="*80)
        print("IMAGE UPLOAD RESULT:")
        import json
        print(json.dumps(result, indent=2, default=str))
        print("="*80 + "\n")
            
        # Save the session with updated context
        SessionManager.create_or_update_session(phone_number, pipeline_instance)
        
        # Always return 200 with the result in the body
        return jsonify(result), 200
        
    except Exception as e:
        logger.error(f"Error processing image upload: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "message": f"Error processing image: {str(e)}",
            "ui_action": "suggest_new_upload",
            "continue_flow": False,
            "show_upload_again": True,
            "is_image_verified": False,
            "whatsapp_message_id": f"MSG_IMG_ERR_{int(time.time())}",
            "whatsapp_from": data.get('phone_number') if data else "1122334455",
            "timestamp": str(int(time.time()))
        }), 200

@main.route('/api/pipeline/provide-missing-info', methods=['POST'])
def provide_missing_info():
    """
    Endpoint to handle manually provided missing information
    """
    try:
        # Get data from request
        data = request.json
        if data:
            logger.info(f"MISSING INFO PAYLOAD: {data}")
            
        if not data or 'fields' not in data:
            return jsonify({
                "success": False,
                "message": "No fields provided for update",
                "updated_info": {}
            }), 200
        
        # Get phone number from request - required parameter
        phone_number = data.get('phone_number')
        
        if not phone_number:
            return jsonify({
                "success": False,
                "message": "Phone number is required for updating information",
                "updated_info": {}
            }), 200
        
        # Get pipeline instance from the phone number session
        pipeline_instance = None
        
        # Try to get pipeline from persistent sessions by phone number
        existing_session = SessionManager.get_session_by_phone(phone_number)
        if existing_session:
            _, pipeline_instance = existing_session
            logger.info(f"Retrieved existing session for phone number: {phone_number}")
        else:
            # No existing session, create a new one for this phone number
            logger.info(f"Creating new session for phone number: {phone_number}")
            pipeline_instance = SalePipeline()
            # Start a new conversation
            pipeline_instance.start_conversation()
            # Save the new session - this will happen after fields are updated
        
        # Get fields to update from request
        fields = data.get('fields', {})
        
        # Update each field in the user context
        updated_fields = {}
        for field, value in fields.items():
            if hasattr(pipeline_instance.context, field):
                # Update the field in the context
                setattr(pipeline_instance.context, field, value)
                updated_fields[field] = value
                
                # Also update in extracted_info
                pipeline_instance.context.extracted_info[field] = value
                
                # Remove from missing fields list if present
                if field in pipeline_instance.context.missing_fields:
                    pipeline_instance.context.missing_fields.remove(field)
        
        # Save the session with the updated fields
        SessionManager.create_or_update_session(phone_number, pipeline_instance)
        
        # Check if we've completed all required fields
        has_all_required = pipeline_instance.context.has_all_required_fields()
        missing_fields = pipeline_instance.context.get_missing_fields()
        
        # Update session state if all fields are provided
        if has_all_required:
            pipeline_instance.context.ready_to_transition = True
        
        # Add message metadata for tracking
        message_id = f"MSG_INFO_{int(time.time())}_{phone_number[-6:] if phone_number else ''}"
        
        return jsonify({
            "success": True,
            "message": "Information updated successfully.",
            "updated_info": updated_fields,
            "has_all_required": has_all_required,
            "missing_fields": missing_fields,
            "continue_flow": has_all_required,
            "whatsapp_message_id": message_id,
            "whatsapp_from": phone_number,
            "timestamp": str(int(time.time()))
        }), 200
    
    except Exception as e:
        logger.error(f"Error updating missing info: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "message": f"Error updating information: {str(e)}",
            "updated_info": {},
            "whatsapp_message_id": f"MSG_INFO_ERR_{int(time.time())}",
            "whatsapp_from": data.get('phone_number') if data else "1122334455",
            "timestamp": str(int(time.time()))
        }), 200

# Helper function to cleanup expired sessions (can be called periodically)
def cleanup_expired_sessions():
    """Remove session data older than specified days"""
    # First clean up in-memory sessions (legacy)
    current_time = time.time()
    expired_sessions = []
    
    for session_id, pipeline in active_pipelines.items():
        # Check if pipeline has last_activity attribute
        if hasattr(pipeline, 'last_activity'):
            # Session expires after 24 hours of inactivity
            if current_time - pipeline.last_activity > 24 * 60 * 60:
                expired_sessions.append(session_id)
    
    # Remove expired sessions from memory
    for session_id in expired_sessions:
        active_pipelines.pop(session_id, None)
    
    # Now clean up persistent sessions (default is 30 days)
    cleaned_count = SessionManager.cleanup_expired_sessions(days=30)
    
    return len(expired_sessions) + cleaned_count

@main.route('/api/sessions/list', methods=['GET'])
def list_sessions():
    """List all active sessions"""
    try:
        sessions = SessionManager.list_active_sessions()
        
        return jsonify({
            "success": True,
            "sessions": sessions,
            "count": len(sessions)
        })
        
    except Exception as e:
        logger.error(f"Error listing sessions: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@main.route('/api/sessions/clear/<phone_number>', methods=['POST'])
def clear_session(phone_number):
    """Clear a session for a specific phone number"""
    try:
        if not phone_number:
            return jsonify({
                "success": False,
                "error": "No phone number provided"
            }), 400
            
        # Attempt to clear the session
        result = SessionManager.clear_session(phone_number)
        
        if result:
            logger.info(f"Successfully cleared session for phone number: {phone_number}")
            return jsonify({
                "success": True,
                "message": f"Session for {phone_number} successfully cleared"
            })
        else:
            logger.warning(f"Failed to clear session for phone number: {phone_number}")
            return jsonify({
                "success": False,
                "error": f"No active session found for {phone_number} or session could not be cleared"
            }), 404
            
    except Exception as e:
        logger.error(f"Error clearing session for {phone_number}: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@main.route('/api/sessions/cleanup', methods=['POST'])
def cleanup_sessions():
    """
    Manually trigger cleanup of expired sessions
    """
    try:
        # Run cleanup
        cleaned_count = cleanup_expired_sessions()
        
        return jsonify({
            "success": True,
            "message": f"Cleaned up {cleaned_count} expired sessions"
        }), 200
        
    except Exception as e:
        logger.error(f"Error cleaning up sessions: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@main.route('/api/sessions/history/<phone_number>', methods=['GET'])
def get_session_history(phone_number):
    """Get conversation history for a specific phone number"""
    try:
        # Check if phone number is provided
        if not phone_number:
            return jsonify({
                "success": False,
                "error": "No phone number provided",
                "exists": False
            }), 400
            
        # Try to get pipeline from persistent sessions by phone number
        existing_session = SessionManager.get_session_by_phone(phone_number)
        
        if not existing_session:
            logger.info(f"No session found for phone number: {phone_number}")
            return jsonify({
                "success": True,
                "exists": False,
                "message": f"No active session found for {phone_number}"
            })
            
        # Get session and pipeline
        session_obj, pipeline = existing_session
        
        # Extract conversation history
        conversation_history = []
        if hasattr(pipeline, 'context') and hasattr(pipeline.context, 'conversation_history'):
            raw_history = pipeline.context.conversation_history
            
            # Log the history for debugging
            logger.info(f"Found {len(raw_history)} messages in conversation history for {phone_number}")
            
            for entry in raw_history:
                # Process each message in the history
                if isinstance(entry, dict):
                    role = entry.get('role', 'system')
                    content = entry.get('content', '')
                    
                    # Skip empty messages
                    if content.strip():
                        conversation_history.append({
                            'role': role,
                            'content': content
                        })
        
        # Get current node information if available
        current_node = None
        next_node = None
        
        if hasattr(pipeline, 'current_node_id'):
            current_node = pipeline.current_node_id
            
            # Get node name from the node ID
            if hasattr(pipeline, 'nodes') and current_node in pipeline.nodes:
                current_node = pipeline.nodes[current_node].name
            
        # Try to determine next node
        if current_node and hasattr(pipeline, 'nodes'):
            current_node_id = pipeline.current_node_id
            if current_node_id in pipeline.nodes:
                node = pipeline.nodes[current_node_id]
                next_node_id = node.get_next_node("default")
                if next_node_id and next_node_id in pipeline.nodes:
                    next_node = pipeline.nodes[next_node_id].name
            
        # Get any user information that might be stored
        user_info = {}
        if hasattr(pipeline, 'context') and hasattr(pipeline.context, 'extracted_info'):
            user_info = pipeline.context.extracted_info
            
        # Create response with session data
        response = {
            "success": True,
            "exists": True,
            "conversation_history": conversation_history,
            "current_node": current_node,
            "next_node": next_node,
            "user_info": user_info,
            "phone_number": phone_number,
            "session_id": session_obj.id if hasattr(session_obj, 'id') else None
        }
        
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"Error retrieving session history for {phone_number}: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "exists": False,
            "error": str(e)
        }), 500

@main.route('/api/qualify-lead/<phone_number>', methods=['GET'])
def get_lead_qualification(phone_number):
    """
    Get lead qualification report for a specific phone number
    """
    try:
        # Process lead qualification
        qualification_data = process_lead_qualification(phone_number)
        
        if not qualification_data.get('success', False):
            return jsonify(qualification_data), 404
            
        return jsonify(qualification_data), 200
        
    except Exception as e:
        logger.error(f"Error getting lead qualification: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": str(e),
            "phone_number": phone_number
        }), 500

@main.route('/api/qualify-lead', methods=['POST'])
def qualify_lead():
    """
    Manually trigger lead qualification for a specific phone number
    """
    try:
        data = request.json
        if not data or 'phone_number' not in data:
            return jsonify({
                "success": False,
                "error": "Phone number is required"
            }), 400
            
        phone_number = data.get('phone_number')
        
        # Process lead qualification
        qualification_data = process_lead_qualification(phone_number)
        
        return jsonify(qualification_data), 200
        
    except Exception as e:
        logger.error(f"Error triggering lead qualification: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@main.route('/api/lead-qualifications', methods=['GET'])
def list_all_lead_qualifications():
    """
    List all lead qualification results
    """
    try:
        limit = request.args.get('limit', default=100, type=int)
        offset = request.args.get('offset', default=0, type=int)
        
        # Get all lead qualifications
        qualifications = list_lead_qualifications(limit, offset)
        
        # Ensure proper report formatting for all reports
        for qualification in qualifications:
            if 'qualification_report' in qualification and qualification['qualification_report']:
                report = qualification['qualification_report']
                
                # 1. Normalize line endings
                report = report.replace('\r\n', '\n')
                
                # 2. Remove any HTML tags that might be in the report
                report = re.sub(r'<div[^>]*>|</div>', '', report)
                report = re.sub(r'<[^>]*>', '', report)
                
                # 3. Improved backtick handling - first remove language hints
                report = re.sub(r'```\w+', '```', report)
                
                # 4. If entire report is wrapped in backticks, remove them
                if report.strip().startswith('```') and report.strip().endswith('```'):
                    report = report.strip()
                    report = report[3:-3].strip()
                else:
                    # If there are multiple code blocks, just remove all backticks
                    report = report.replace('```', '').strip()
                
                # 5. Fix table header and separator issues
                table_pattern = r'\|\s*Dimension\s*\|\s*Score\s*\|\s*Explanation\s*\|'
                separator_pattern = r'\|[-]+\|[-]+\|[-]+\|'
                
                if re.search(table_pattern, report):
                    header_match = re.search(table_pattern, report)
                    if header_match:
                        header_pos = header_match.start()
                        
                        # Check if there's a proper separator row
                        after_header = report[header_match.end():]
                        separator_match = re.search(separator_pattern, after_header)
                        
                        if not separator_match or separator_match.start() > 10:
                            # Insert proper separator after header
                            header_line_end = header_pos + report[header_pos:].find('\n')
                            if header_line_end > header_pos:
                                separator_row = "\n|-----------|-------|-------------|\n"
                                report = report[:header_line_end] + separator_row + report[header_line_end:]
                
                # 6. Ensure proper spacing around table and sections
                report = re.sub(r'(##[^#\n]+)\n\|', r'\1\n\n|', report)  # Add empty line after section headers
                report = re.sub(r'\|\n([^|])', r'|\n\n\1', report)  # Add empty line after table
                
                # Find table rows and ensure they are properly formatted
                table_rows = re.findall(r'\|[^\n]+\|[^\n]+\|[^\n]+\|', report)
                for row in table_rows:
                    # Check if row is broken across multiple lines or has missing column separators
                    if row.count('|') != 4:  # Should have 4 | symbols for 3 columns
                        fixed_row = row.replace('\n', ' ').strip()
                        # Ensure the row has the right number of | symbols
                        while fixed_row.count('|') < 4:
                            fixed_row += ' |'
                        report = report.replace(row, fixed_row)
                
                # Additional aggressive cleaning for tables
                # Clean up any remaining broken table formatting
                # Look for the table section
                table_section_match = re.search(r'(## 2\. Qualification Details.*?)(## 3\.)', report, re.DOTALL)
                if table_section_match:
                    table_section = table_section_match.group(1)
                    # Find the table header
                    header_match = re.search(r'\|\s*Dimension\s*\|\s*Score\s*\|\s*Explanation\s*\|', table_section)
                    if header_match:
                        # Get the table header
                        header = header_match.group(0).strip()
                        # Get the separator (or create one if missing)
                        separator = '|-----------|-------|-------------|'
                        # Get the rows - each row should be on its own line with 3 columns
                        rows_section = table_section[header_match.end():].strip()
                        # Split by newline and filter out empty lines
                        row_lines = [line.strip() for line in rows_section.split('\n') if line.strip() and '|' in line]
                        # Process each row to ensure consistent formatting
                        cleaned_rows = []
                        for row in row_lines:
                            # Skip the separator row as we'll add a clean one
                            if re.match(r'\|[\-\s]+\|[\-\s]+\|[\-\s]+\|', row):
                                continue
                            # Clean and add the row
                            cleaned_row = re.sub(r'\|\s*', '| ', row)
                            cleaned_row = re.sub(r'\s*\|', ' |', cleaned_row)
                            cleaned_rows.append(cleaned_row)
                        
                        # Build a properly formatted table
                        formatted_table = f"""
## 2. Qualification Details

{header}
{separator}
{chr(10).join(cleaned_rows)}

"""
                        # Replace the original table section
                        report = report.replace(table_section, formatted_table)
                
                # Ensure proper heading formatting with sufficient spacing
                for heading_level in range(1, 4):  # h1, h2, h3
                    heading_marker = '#' * heading_level
                    report = re.sub(f"{heading_marker}\\s+([^\n]+)", f"{heading_marker} \\1", report)
                
                # 7. Check for missing newlines at the end of the report (helpful for rendering)
                if not report.endswith('\n'):
                    report += '\n'
                
                # Update the report in the qualification data
                qualification['qualification_report'] = report
                
                # For debugging, add raw and cleaned report content
                if request.args.get('debug') == 'true':
                    qualification['raw_report'] = qualification['qualification_report']
                    qualification['has_html'] = '<' in qualification['qualification_report'] and '>' in qualification['qualification_report']
        
        return jsonify({
            "success": True,
            "count": len(qualifications),
            "qualifications": qualifications
        }), 200
        
    except Exception as e:
        logger.error(f"Error listing lead qualifications: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500
        
@main.route('/lead-dashboard')
def lead_dashboard():
    """
    Display the lead qualification dashboard
    """
    return render_template('lead_dashboard.html')

@main.route('/api/test-gemini-integration', methods=['GET'])
def test_gemini_integration():
    """
    Test endpoint to verify the Gemini integration in lead qualification service
    """
    try:
        from app.services.lead_qualification_service import call_llm_api
        
        test_prompt = "Hello, please generate a brief test response to verify you're working properly."
        system_prompt = "You are a helpful assistant integrated with a lead qualification service."
        
        response = call_llm_api(test_prompt, system_prompt)
        
        return jsonify({
            "success": True,
            "message": "Gemini API integration test",
            "response": response
        }), 200
        
    except Exception as e:
        logger.error(f"Error testing Gemini integration: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@main.route('/api/sessions/update-states', methods=['POST'])
def update_states():
    """
    Endpoint to update states for a specific phone number
    """
    try:
        # Get data from request
        data = request.json
        if not data or 'phone_number' not in data or 'updated_states' not in data:
            return jsonify({
                "success": False,
                "message": "Phone number and updated states are required"
            }), 400

        phone_number = data['phone_number']
        updated_states = data['updated_states']

        # Get pipeline instance from the phone number session
        existing_session = SessionManager.get_session_by_phone(phone_number)
        if not existing_session:
            return jsonify({
                "success": False,
                "message": "No active session found for the provided phone number"
            }), 404

        _, pipeline_instance = existing_session

        # Update the states in both extracted_info and user_info
        if hasattr(pipeline_instance.context, 'extracted_info'):
            pipeline_instance.context.extracted_info.update(updated_states)

        # Log the state before update
        logger.info(f"States before update: {pipeline_instance.context.extracted_info}")

        # Save the updated session
        SessionManager.create_or_update_session(phone_number, pipeline_instance)

        # Log the state after update
        logger.info(f"States after update: {pipeline_instance.context.extracted_info}")

        return jsonify({
            "success": True,
            "message": "States updated successfully",
            "updated_info": pipeline_instance.context.extracted_info
        }), 200

    except Exception as e:
        logger.error(f"Error updating states: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "message": f"Error updating states: {str(e)}"
        }), 500

@main.route('/api/debug/regenerate-report/<phone_number>', methods=['GET'])
def debug_regenerate_report(phone_number):
    """
    Debug endpoint to regenerate a lead qualification report for a specific phone number
    and return detailed information about the report format
    """
    try:
        # Process lead qualification
        result = process_lead_qualification(phone_number)
        
        if not result.get('success'):
            return jsonify({
                "success": False,
                "error": result.get('error', 'Failed to process lead qualification'),
                "phone_number": phone_number
            }), 400
        
        # Get the report
        report = result.get('qualification_report', '')
        
        # Create debug info
        debug_info = {
            "success": True,
            "phone_number": phone_number,
            "report": report,
            "report_length": len(report),
            "has_table_header": "| Dimension | Score | Explanation |" in report,
            "has_table_separator": "|---" in report,
            "has_line_breaks_around_table": "## 2. Qualification Details\n\n|" in report and "|\n\n##" in report,
            "sections": {
                "has_classification_summary": "## 1. Lead Classification Summary" in report,
                "has_qualification_details": "## 2. Qualification Details" in report,
                "has_recommendations": "## 3. Recommendations" in report
            },
            "line_count": len(report.split('\n')),
            "bytes": [ord(c) for c in report[:100]],  # First 100 characters as byte values for debugging
            "report_preview": report[:500] + ("..." if len(report) > 500 else "")
        }
        
        return jsonify(debug_info), 200
        
    except Exception as e:
        logger.error(f"Error regenerating lead qualification report: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": str(e),
            "phone_number": phone_number
        }), 500

@main.route('/api/fix-report/<phone_number>', methods=['GET'])
def fix_lead_report(phone_number):
    """
    Special endpoint to fix the lead qualification report for the specific problematic phone number
    """
    try:
        # Force regenerate the lead qualification
        from app.services.lead_qualification_service import process_lead_qualification, get_lead_qualification_by_phone
        import re
        
        # First, get existing data to preserve information
        existing_qualification = get_lead_qualification_by_phone(phone_number)
        
        # Force regenerate the report
        result = process_lead_qualification(phone_number)
        
        if not result.get('success'):
            return jsonify({
                "success": False,
                "error": result.get('error', 'Failed to process lead qualification'),
                "phone_number": phone_number
            }), 400
        
        # Additional cleanup for this specific report
        report = result.get('qualification_report', '')
        
        # Apply more aggressive cleaning for this specific report
        if '<div' in report or '</div>' in report:
            # More aggressive HTML removal
            report = re.sub(r'<div[^>]*>|</div>', '', report)
            report = re.sub(r'<[^>]*>', '', report)
            
            # Update the stored report
            result['qualification_report'] = report
            
            # Additional table fixing logic
            lines = report.split('\n')
            fixed_lines = []
            table_mode = False
            header_line = -1
            
            for i, line in enumerate(lines):
                # Check if we're entering a table
                if '| Dimension | Score | Explanation |' in line:
                    table_mode = True
                    header_line = i
                    fixed_lines.append(line)
                # If we're in table mode, ensure proper formatting
                elif table_mode and line.strip().startswith('|'):
                    # Normalize table row
                    parts = line.split('|')
                    if len(parts) < 5:  # We need at least 4 | symbols for 3 columns
                        while len(parts) < 5:
                            parts.append('')
                        line = '|'.join(parts)
                    fixed_lines.append(line)
                # Check if we're exiting table mode
                elif table_mode and line.strip() and not line.strip().startswith('|'):
                    table_mode = False
                    fixed_lines.append(line)
                else:
                    fixed_lines.append(line)
                    
            # Ensure there's a separator row after the header
            if header_line >= 0 and header_line + 1 < len(fixed_lines):
                if not fixed_lines[header_line + 1].strip().startswith('|---'):
                    fixed_lines.insert(header_line + 1, '|-----------|-------|-------------|')
                    
            # Rejoin the fixed lines
            fixed_report = '\n'.join(fixed_lines)
            result['qualification_report'] = fixed_report
        
        # Return the fixed report with some debugging info
        return jsonify({
            "success": True,
            "message": "Report fixed successfully",
            "phone_number": phone_number,
            "qualification": result,
            "original_had_html": '<div' in report or '</div>' in report
        }), 200
        
    except Exception as e:
        logger.error(f"Error fixing lead qualification report: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "error": str(e),
            "phone_number": phone_number
        }), 500

@main.route('/api/test-markdown-rendering')
def test_markdown_rendering():
    """
    Test endpoint for markdown rendering of a sample report
    """
    sample_report = """```
# Spiro Electric Bikes - Lead Qualification Report

## 1. Lead Classification Summary

Category: Warm Lead

Overall Assessment: This lead shows strong potential, with a clear purchase timeline and budget. Determining their primary use will be key to converting them into a hot lead.

## 2. Qualification Details

| Dimension | Score | Explanation |
|-----------|-------|-------------|
| Primary Use | 0/10 | The lead's intended use for the bike is currently unknown. |
| Model Selection | 8/10 | The lead has shown interest in the EKON ALPHA model. |
| Purchase Timeline | 9/10 | The lead intends to purchase within a week, indicating urgency. |
| Budget Range | 8/10 | The lead's budget is above INR 100000. |
| Interest Level | 7/10 | The lead is interested in EMI/Finance options, showing serious consideration. |

## 3. Recommendations

1. Immediately contact the lead to determine their primary use case.
2. Provide detailed information about the EKON ALPHA model, highlighting EMI/finance options.
3. Tailor the sales pitch based on their primary use to increase the likelihood of conversion.
```"""

    return render_template('markdown_test.html', report=sample_report)

@main.route('/session-messages')
def session_messages():
    """
    Display the session messages dashboard
    """
    return render_template('session_messages.html')