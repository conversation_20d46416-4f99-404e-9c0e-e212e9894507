package whatsapp_contacts


import (
	"github.com/labstack/echo/v4"
)

func (h *handler) Route(g *echo.Group) {

	// ============================== Sample Route =============================
	g.POST("/create", h.CreateWhatsappContact, SampleCreateValidation)
	g.POST("/update/:id", h.UpdateWhatsappContact, SampleUpdateValidation)
	g.GET("/get/:id", h.GetWhatsappContact)
	g.DELETE("/delete/:id", h.DeleteWhatsappContact)
	g.GET("", h.ListWhatsappContact)
}
