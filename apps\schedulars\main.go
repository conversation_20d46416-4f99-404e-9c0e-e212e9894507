package main

import (
	env "apps/schedulars/config"
	"fmt"
	"html/template"
	"io"
	shared "libs/shared"
	"net/http"
	_ "net/http/pprof"
	"os"

	StartSchedular "apps/schedulars/controller/schedular"

	"github.com/go-playground/validator/v10"
	"github.com/gorilla/websocket"
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"

	schedularRouter "apps/schedulars/controller"
	"path/filepath"
)

type CustomValidator struct{ Validator *validator.Validate }
type TemplateRegistry struct{ templates *template.Template }

var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		// Allow all connections by default, modify as necessary for your use case
		return true
	},
}

func (cv *CustomValidator) Validate(i interface{}) error {
	return cv.Validator.Struct(i)
}
func (t *TemplateRegistry) Render(w io.Writer, name string, data interface{}, c echo.Context) error {

	// Add global methods if data is a map
	if viewContext, isMap := data.(map[string]interface{}); isMap {
		viewContext["reverse"] = c.Echo().Reverse
	}
	t.templates.Funcs(template.FuncMap{"mod": func(i, j int) bool { return i%j == 0 }})
	return t.templates.ExecuteTemplate(w, name, data)
}

func init() {
	ENV := os.Getenv("ENV")
	shared.PrettyPrint("ENV variables :", ENV)
}

func main() {
	shared.PrettyPrint("Global env", env.GlobalEnv)


	go StartSchedular.UpsertSchedulars()
	go StartSchedular.SchedularInit()

	e := echo.New()
	e.GET("/", func(c echo.Context) error {
		message := fmt.Sprintf("Welcome to Spiro Schedular")
		return c.String(http.StatusOK, message)
	})

	// e.GET("/debug/*", echo.WrapHandler(http.DefaultServeMux))
	// Middleware
	e.Use(
		middleware.Logger(),
		middleware.Recover(),
		middleware.CORSWithConfig(middleware.CORSConfig{
			AllowOrigins: []string{"*"},
			AllowMethods: []string{http.MethodGet, http.MethodPut, http.MethodPost, http.MethodDelete},
		}),
		middleware.LoggerWithConfig(middleware.LoggerConfig{
			Format:           fmt.Sprintf("\nSutra : | ${host} | ${time_custom} | ${status} | ${latency_human} | ${remote_ip} | ${method} | ${uri} "),
			CustomTimeFormat: "2006/01/02 15:04:05",
			Output:           os.Stdout,
		}),
	)

	e.HTTPErrorHandler = shared.ErrorHandler
	e.Validator = &CustomValidator{Validator: validator.New()}

	pattern := "public/*.html"
	absPath, _ := filepath.Abs(pattern)

	fmt.Println(absPath)

	e.Renderer = &TemplateRegistry{templates: template.Must(template.New("t").Funcs(template.FuncMap{"mod": shared.Mod}).ParseGlob(absPath))}

	e.Static("/files", "apps/schedulars/assets")
	// e.Static("/swagger-ui", "swagger-ui")
	g := e.Group("/api/v1/sutra/schedulars")

	// Index
	g.GET("", func(c echo.Context) error {
		message := "Welcome to Spiro Schedular Backend"
		return c.String(http.StatusOK, message)
	})

	g.GET("/logs", func(c echo.Context) error {
		html := `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Live Logs</title>
      <style>
        body { font-family: Arial, sans-serif; }
        #log { border: 1px solid #ccc; padding: 10px; height: 300px; overflow-y: scroll; }
      </style>
    </head>
      <body>
        <h1>Live Logs</h1>
        <div id="log"></div>
        <script>
          var logDiv = document.getElementById("log");
          var ws = new WebSocket("ws://localhost:8081/ws/logs");

          ws.onmessage = function(event) {
            var logEntry = document.createElement("div");
            logEntry.textContent = event.data;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
          };
        </script>
      </body>
      </html>
      `
		return c.HTML(http.StatusOK, html)
	})

	// Routes
	schedularRouter.Init(g)

	// Catch-all
	e.POST("*", func(c echo.Context) error {
		return shared.RespPageNotFound(c, "Page Not Found", "Page Not Found")
	})
	e.GET("*", func(c echo.Context) error {
		return shared.RespPageNotFound(c, "Page Not Found", "Page Not Found")
	})

	e.Logger.Fatal(e.Start(":" + fmt.Sprintf("%v", env.GlobalEnv["PORT"])))
}
