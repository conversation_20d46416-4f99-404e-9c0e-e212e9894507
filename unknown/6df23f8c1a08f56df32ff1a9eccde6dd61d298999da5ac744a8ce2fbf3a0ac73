package product_management

import (
	flows "apps/spiro/controllers/whatsapp/flows"

)

type MiddlewareResponse struct {
	Success bool `json:"success"`
}

type ProductsPayload struct {
	Message         string                  `json:"message"`
	MessageVisible  bool                    `json:"message_visible"`
	Products        []flows.FlowButtonGroup `json:"products"`
	ProductsVisible bool                    `json:"products_visible"`
	InitValues      map[string]interface{}  `json:"init_values"`
	Pages           []flows.FlowButtonGroup `json:"pages"`
}

type ProductViewPayload struct {
	ProductId      string                  `json:"product_id"`
	ProductName    string                  `json:"product_name"`
	ProductDetails string                  `json:"product_details"`
	ProductImage   string                  `json:"product_image"`
	Actions        []flows.FlowButtonGroup `json:"actions"`
	ActionVisible  bool                    `json:"action_visible"`
	ActionRequired bool                    `json:"action_required"`
}

type ProductUpdatePayload struct {
	ProductId string `json:"product_id"`
	Price     string `json:"price"`
	Quantity  string `json:"quantity"`
}

type FiltersPayload struct {
	Statuses   []flows.FlowButtonGroup `json:"statuses"`
	Categories []flows.FlowButtonGroup `json:"categories"`
}

type ProductImagesPayload struct {
	ProductId            string `json:"product_id"`
	ProductName          string `json:"product_name"`
	ProductImage1        string `json:"product_image_1"`
	ProductImage1Visible bool   `json:"product_image_1_visible"`
	ProductImage2        string `json:"product_image_2"`
	ProductImage2Visible bool   `json:"product_image_2_visible"`
	ProductImage3        string `json:"product_image_3"`
	ProductImage3Visible bool   `json:"product_image_3_visible"`
}

type SellerMiddlewareProductResponse struct {
	Success bool `json:"success"`
	Data    struct {
		TotalPages int `json:"total_pages"`
		Products   []struct {
			ProductId string `json:"product_id"`
			Text      string `json:"text"`
			Title     string `json:"title"`
			Image     string  `json:"image"`
		} `json:"products"`
	} `json:"data"`
}

type SellerMiddlewareProductViewResponse struct {
	Success bool `json:"success"`
	Data    struct {
		ProductId      string   `json:"product_id"`
		ProductName    string   `json:"product_name"`
		ProductDetails string   `json:"product_details"`
		ProductImages  string `json:"product_images"`
	} `json:"data"`
}

type ProductsResponse struct {
	Action    string `json:"action_key"`
	ProductId string `json:"product_id"`
	PageNo    string `json:"page_no"`
}

type ProductViewResponse struct {
	Action    string `json:"action_key"`
	ProductId string `json:"product_id"`
}

type FiltersResponse struct {
	Status   string `json:"status"`
	Category string `json:"category"`
}

type ProductImagesResponse struct {
	ProductId string `json:"product_id"`
}
