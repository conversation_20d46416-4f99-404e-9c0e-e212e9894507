package products

import (
	// "fmt"
	shared "libs/shared"
	// "strconv"

	"github.com/labstack/echo/v4"
)

type Handler interface {
}

type handler struct {
	service Service
}

var newHandlerObj *handler //singleton object

// singleton function
func NewHandler() *handler {
	if newHandlerObj != nil {
		return newHandlerObj
	}

	new_service := NewService()
	newHandlerObj = &handler{new_service}
	return newHandlerObj
}



func (h *handler) List(c echo.Context) error {

	var data ListRequest
	err := c.Bind(&data)
	if err != nil {
		return shared.RasaMiddlewareError(c, err)
	}

	response, err := h.service.List(data)
	if err != nil {
		return shared.RasaMiddlewareError(c, err)
	}

	return shared.RasaMiddlewareSuccess(c, response)
}


func (h *handler) View(c echo.Context) error {

	var data ViewRequest
	err := c.Bind(&data)
	if err != nil {
		return shared.RasaMiddlewareError(c, err)
	}

	response, err := h.service.View(data)
	if err != nil {
		return shared.RasaMiddlewareError(c, err)
	}

	return shared.RasaMiddlewareSuccess(c, response)
}