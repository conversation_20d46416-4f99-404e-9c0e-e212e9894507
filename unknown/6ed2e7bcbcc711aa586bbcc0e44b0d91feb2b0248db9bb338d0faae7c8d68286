// Markdown rendering utilities
class MarkdownRenderer {
  // Initialize the renderer
  static init() {
    // Load the marked.js library
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js';
    script.onload = () => {
      console.log('Marked library loaded successfully');
      // Configure marked with appropriate options
      marked.setOptions({
        breaks: true,        // Add line breaks when they appear in source
        gfm: true,           // Use GitHub Flavored Markdown
        headerIds: false,    // Don't add IDs to headers
        mangle: false,       // Don't mangle email addresses
        sanitize: false,     // Don't sanitize HTML (we use DOMPurify for this)
      });
    };
    document.head.appendChild(script);

    // Load DOMPurify for sanitization
    const purifyScript = document.createElement('script');
    purifyScript.src = 'https://cdn.jsdelivr.net/npm/dompurify@3.0.5/dist/purify.min.js';
    purifyScript.onload = () => {
      console.log('DOMPurify loaded successfully');
    };
    document.head.appendChild(purifyScript);
  }

  // Convert markdown to HTML and sanitize
  static render(markdownText) {
    if (!window.marked || !window.DOMPurify) {
      console.warn('Markdown libraries not loaded yet');
      return markdownText; // Return plain text if libraries aren't loaded
    }
    
    try {
      // Convert markdown to HTML
      const unsafeHtml = marked.parse(markdownText);
      
      // Sanitize HTML to prevent XSS attacks
      const safeHtml = DOMPurify.sanitize(unsafeHtml);
      
      return safeHtml;
    } catch (error) {
      console.error('Error rendering markdown:', error);
      return markdownText; // Return original text if rendering fails
    }
  }
} 