<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown Rendering Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            padding: 20px;
            background-color: #f9fafb;
        }
        
        .container {
            max-width: 900px;
        }
        
        .card {
            border-radius: 16px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.05), 0 4px 6px -2px rgba(0, 0, 0, 0.025);
            border: none;
            overflow: hidden;
            margin-bottom: 30px;
        }
        
        .card-header {
            background-color: #1a1938;
            color: white;
            border-bottom: none;
            padding: 1.5rem;
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .raw-view {
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 0.9rem;
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 1rem;
        }
        
        .rendered-view table {
            width: 100%;
            border-collapse: collapse;
            margin: 1.5rem 0;
            border: 1px solid #e5e7eb;
        }
        
        .rendered-view table th, 
        .rendered-view table td {
            border: 1px solid #e5e7eb;
            padding: 8px;
            text-align: left;
        }
        
        .rendered-view table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        .rendered-view table tr:nth-child(even) {
            background-color: #f9fafb;
        }
        
        .rendered-view table tr:hover {
            background-color: #f3f4f6;
        }
        
        .rendered-view h1 {
            font-size: 1.8rem;
            margin-bottom: 1rem;
            color: #111;
        }
        
        .rendered-view h2 {
            font-size: 1.5rem;
            margin-top: 1.5rem;
            margin-bottom: 1rem;
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 0.5rem;
        }
        
        .rendered-view h3 {
            font-size: 1.25rem;
            margin-top: 1.25rem;
            margin-bottom: 0.75rem;
            color: #444;
        }
        
        .rendered-view ul,
        .rendered-view ol {
            margin-bottom: 1rem;
            padding-left: 2rem;
        }
        
        .rendered-view li {
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="my-4 text-center">Markdown Rendering Test</h1>
        
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-markdown-fill me-2"></i>Test Rendering</h5>
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="viewToggle">
                    <label class="form-check-label" for="viewToggle">Show Raw Markdown</label>
                </div>
            </div>
            <div class="card-body">
                <div id="renderedView" class="rendered-view"></div>
                <pre id="rawView" class="raw-view d-none">{{ report }}</pre>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-info-circle-fill me-2"></i>Diagnostic Information</h5>
            </div>
            <div class="card-body">
                <div id="diagnosticInfo"></div>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const rawView = document.getElementById('rawView');
            const renderedView = document.getElementById('renderedView');
            const viewToggle = document.getElementById('viewToggle');
            const diagnosticInfo = document.getElementById('diagnosticInfo');
            
            // Get the report from the pre tag
            const reportMarkdown = rawView.textContent;
            
            // Add diagnostic info
            diagnosticInfo.innerHTML = `
                <p><strong>Original Length:</strong> ${reportMarkdown.length} characters</p>
                <p><strong>Starts with backticks:</strong> ${reportMarkdown.trim().startsWith('```')}</p>
                <p><strong>Ends with backticks:</strong> ${reportMarkdown.trim().endsWith('```')}</p>
            `;
            
            // Process markdown (strip backticks if present)
            let processedMarkdown = reportMarkdown;
            if (processedMarkdown.trim().startsWith('```')) {
                // This handles cases where the entire content is wrapped in backticks
                processedMarkdown = processedMarkdown.trim();
                const firstBacktickIndex = processedMarkdown.indexOf('```');
                const lastBacktickIndex = processedMarkdown.lastIndexOf('```');
                
                if (firstBacktickIndex !== lastBacktickIndex) {
                    // Remove first and last backticks
                    processedMarkdown = processedMarkdown.substring(
                        firstBacktickIndex + 3, 
                        lastBacktickIndex
                    ).trim();
                }
            }
            
            // Update diagnostic info
            diagnosticInfo.innerHTML += `
                <p><strong>Processed Length:</strong> ${processedMarkdown.length} characters</p>
                <hr>
                <p><strong>First 50 chars:</strong> <code>${processedMarkdown.substring(0, 50).replace(/</g, '&lt;').replace(/>/g, '&gt;')}</code></p>
            `;
            
            // Configure marked options
            marked.setOptions({
                breaks: true,
                gfm: true,
                tables: true,
                headerIds: false,
                silent: true
            });
            
            try {
                // Render the markdown
                renderedView.innerHTML = marked.parse(processedMarkdown);
                
                // Add table classes and styling
                const tables = renderedView.querySelectorAll('table');
                tables.forEach(table => {
                    table.classList.add('table', 'table-bordered', 'table-hover', 'mb-4');
                    
                    // Wrap table in responsive container
                    const wrapper = document.createElement('div');
                    wrapper.className = 'table-responsive mb-4';
                    table.parentNode.insertBefore(wrapper, table);
                    wrapper.appendChild(table);
                });
                
                // Update diagnostic info about rendering
                diagnosticInfo.innerHTML += `
                    <p><strong>Tables found:</strong> ${tables.length}</p>
                    <p><strong>Headings found:</strong> ${renderedView.querySelectorAll('h1, h2, h3, h4, h5, h6').length}</p>
                    <p><strong>Lists found:</strong> ${renderedView.querySelectorAll('ul, ol').length}</p>
                `;
            } catch (error) {
                renderedView.innerHTML = `
                    <div class="alert alert-danger">
                        <h4 class="alert-heading">Rendering Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
                
                diagnosticInfo.innerHTML += `
                    <div class="alert alert-danger">
                        <p><strong>Error:</strong> ${error.message}</p>
                        <p><strong>Stack:</strong> ${error.stack}</p>
                    </div>
                `;
            }
            
            // Toggle between raw and rendered views
            viewToggle.addEventListener('change', function() {
                if (this.checked) {
                    renderedView.classList.add('d-none');
                    rawView.classList.remove('d-none');
                } else {
                    renderedView.classList.remove('d-none');
                    rawView.classList.add('d-none');
                }
            });
        });
    </script>
</body>
</html> 