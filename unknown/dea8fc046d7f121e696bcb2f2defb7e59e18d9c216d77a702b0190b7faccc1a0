from flask import Flask
import os
import logging
from flask_session import Session
from sqlalchemy import create_engine
from sqlalchemy.orm import scoped_session, sessionmaker

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

def create_app():
    """Create and configure the Flask app"""
    app = Flask(__name__, static_folder='static', static_url_path='/static')
    
    # Configure app from environment variables
    app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'dev_key_change_in_production')
    app.config['SESSION_TYPE'] = 'filesystem'
    app.config['SESSION_FILE_DIR'] = 'flask_session'
    app.config['SESSION_COOKIE_NAME'] = 'spiro_session'
    
    # Database configuration
    app.config['DATABASE_URL'] = os.environ.get('DATABASE_URL', 'sqlite:///persistent_sessions.db')
    
    # Set Flask's session_cookie_name attribute directly
    app.session_cookie_name = 'spiro_session'
    
    # Initialize Flask-Session
    Session(app)
    
    # Register blueprints
    from app.routes import main as main_bp
    app.register_blueprint(main_bp)
    
    # Run migrations
    logger.info("Running database migrations")
    try:
        from app.migrations.add_is_active_to_lead_qualification import run_migration
        migration_result = run_migration()
        if migration_result:
            logger.info("Migrations completed successfully")
        else:
            logger.warning("Migrations failed or were not needed")
    except Exception as e:
        logger.error(f"Error running migrations: {str(e)}")
    
    return app 