/* Markdown styling for chat messages */

/* Message content container */
.markdown-content {
  line-height: 1.6;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Headers */
.markdown-content h1, 
.markdown-content h2, 
.markdown-content h3, 
.markdown-content h4, 
.markdown-content h5, 
.markdown-content h6 {
  margin-top: 1em;
  margin-bottom: 0.5em;
  font-weight: bold;
  line-height: 1.2;
}

.markdown-content h1 { font-size: 1.8em; }
.markdown-content h2 { font-size: 1.5em; }
.markdown-content h3 { font-size: 1.3em; }
.markdown-content h4 { font-size: 1.2em; }
.markdown-content h5 { font-size: 1.1em; }
.markdown-content h6 { font-size: 1em; }

/* Lists */
.markdown-content ul, 
.markdown-content ol {
  padding-left: 2em;
  margin: 0.5em 0;
}

/* Paragraphs */
.markdown-content p {
  margin: 0.75em 0;
}

/* Code blocks */
.markdown-content pre {
  background-color: #f6f8fa;
  border-radius: 3px;
  padding: 0.5em;
  overflow: auto;
  margin: 0.5em 0;
}

.markdown-content code {
  font-family: Consolas, Monaco, 'Andale Mono', monospace;
  font-size: 0.9em;
  background-color: rgba(175, 184, 193, 0.2);
  border-radius: 3px;
  padding: 0.2em 0.4em;
}

.markdown-content pre > code {
  background-color: transparent;
  padding: 0;
  display: block;
}

/* Blockquotes */
.markdown-content blockquote {
  margin: 0.5em 0;
  padding-left: 1em;
  color: #6a737d;
  border-left: 0.25em solid #dfe2e5;
}

/* Tables */
.markdown-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 1em 0;
}

.markdown-content table th,
.markdown-content table td {
  border: 1px solid #dfe2e5;
  padding: 6px 13px;
}

.markdown-content table th {
  background-color: #f6f8fa;
  font-weight: bold;
}

.markdown-content table tr:nth-child(2n) {
  background-color: #f6f8fa;
}

/* Links */
.markdown-content a {
  color: #0366d6;
  text-decoration: none;
}

.markdown-content a:hover {
  text-decoration: underline;
}

/* Images */
.markdown-content img {
  max-width: 100%;
  height: auto;
}

/* Horizontal rule */
.markdown-content hr {
  height: 0.25em;
  padding: 0;
  margin: 1.5em 0;
  background-color: #e1e4e8;
  border: 0;
} 