from sqlalchemy import Column, String, Integer, DateTime, Text, LargeBinary, Boolean
from sqlalchemy.ext.declarative import declarative_base
import datetime
import json
import pickle
import base64

Base = declarative_base()

class PersistentSession(Base):
    """Model for storing persistent user sessions by phone number"""
    __tablename__ = "persistent_sessions"

    id = Column(Integer, primary_key=True, autoincrement=True)
    phone_number = Column(String(20), unique=True, nullable=False, index=True)
    session_data = Column(LargeBinary, nullable=True)  # For storing serialized session data
    conversation_history = Column(Text, nullable=True)  # For storing conversation history as JSON
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    last_active = Column(DateTime, default=datetime.datetime.utcnow)
    is_active = Column(Boolean, default=True)

    def serialize_pipeline(self, pipeline_instance):
        """Serialize a SalePipeline instance to bytes"""
        if pipeline_instance:
            return pickle.dumps(pipeline_instance)
        return None
    
    def deserialize_pipeline(self, serialized_data):
        """Deserialize bytes back to a SalePipeline instance"""
        if serialized_data:
            return pickle.loads(serialized_data)
        return None
    
    def to_dict(self):
        """Convert session to dictionary"""
        conversation_history = []
        try:
            if self.conversation_history:
                conversation_history = json.loads(self.conversation_history)
        except (json.JSONDecodeError, TypeError):
            pass  # Use empty list if conversion fails
            
        return {
            "id": self.id,
            "phone_number": self.phone_number,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "last_active": self.last_active.isoformat() if self.last_active else None,
            "is_active": self.is_active,
            "conversation_history": conversation_history
        }
    
    def __repr__(self):
        return f"<PersistentSession(id={self.id}, phone_number={self.phone_number})>" 