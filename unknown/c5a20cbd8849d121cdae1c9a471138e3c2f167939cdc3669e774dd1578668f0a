package config

var GlobalEnv = map[string]interface{}{
	"PORT": "4001",
	"POSTGRES_CREDENTIAL": map[string]string{
		"DB_HOST":    "************",
        "DB_USER":    "postgres",
        "DB_PASS":    "root",
        "DB_NAME":    "spirodefaultdb",
        "DB_PORT":    "5432",
        "DB_SSLMODE": "disable",
        "DB_TZ":      "Asia/Kolkata",
	},
	"JWT_KEY":                       "yNVrBBM+oAOWOEcXPFjJuvXXpUq/4XR1KuSGX/i+slF+oE/geu2uW25PXjfWS9pwjmTry3WXn7q0DH7I+vNSjw==",
	"JWT_TOKEN_EXPIRATION_DURATION": "3h",
}
