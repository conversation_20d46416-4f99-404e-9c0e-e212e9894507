package sample

import (
	"fmt"
	shared "libs/shared"
	"strconv"

	"github.com/labstack/echo/v4"
)

type Handler interface {
}

type handler struct {
	service Service
}

var newHandlerObj *handler //singleton object

// singleton function
func NewHandler() *handler {
	if newHandlerObj != nil {
		return newHandlerObj
	}

	new_service := NewService()
	newHandlerObj = &handler{new_service}
	return newHandlerObj
}

// ------------------Product Brand--------------------------------------------------------------------------------------------------
func (h *handler) CreateSample(c echo.Context) error {
	data := c.Get("sampleCreate").(SampleCreatePayloadDto)

	fmt.Println("HH data", data)	

	metaData := shared.ApiMetaData{}
	shared.JsonMarshaller(c.Get("metaData"), &metaData)

	serviceResp, err := h.service.CreateSample(data, metaData)
	if err != nil {
		return shared.RespFailure(c, "Internal Error", err)
	}

	fmt.Println("serviceResp", serviceResp)

	var apiResp = SampleCreateorUpdateRespDto{}
	shared.JsonMarshaller(serviceResp, &apiResp)

	return shared.RespSuccess(c, "Created Successfully", apiResp)
}
func (h *handler) UpdateSample(c echo.Context) error {
	data := c.Get("sampleUpdate").(SampleUpdatePayloadDto)

	metaData := shared.ApiMetaData{}
	shared.JsonMarshaller(c.Get("metaData"), &metaData)

	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		return shared.RespFailure(c, "Invalid ID", err)
	}

	filterQuery := map[string]interface{}{
		"id": id,
	}

	serviceResp, err := h.service.UpdateSample(data, metaData, filterQuery)
	if err != nil {
		return shared.RespFailure(c, "Internal Error", err)
	}

	var apiResp = SampleCreateorUpdateRespDto{}
	shared.JsonMarshaller(serviceResp, &apiResp)

	return shared.RespSuccess(c, "Updated Successfully", apiResp)
}
func (h *handler) GetSample(c echo.Context) error {
	metaData := shared.ApiMetaData{}
	shared.JsonMarshaller(c.Get("metaData"), &metaData)

	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		return shared.RespFailure(c, "Invalid ID", err)
	}

	filterQuery := map[string]interface{}{
		"id": id,
	}

	serviceResp, err := h.service.GetSample(metaData, filterQuery)
	if err != nil {
		return shared.RespFailure(c, "Internal Error", err)
	}

	var apiResp = SampleGetRespDto{}
	shared.JsonMarshaller(serviceResp, &apiResp)

	return shared.RespSuccess(c, "Retrieved Successfully", apiResp)
}
func (h *handler) ListSample(c echo.Context) error {
	metaData := shared.ApiMetaData{}
	shared.JsonMarshaller(c.Get("metaData"), &metaData)

	filterQuery := map[string]interface{}{}
	sortings := map[string]interface{}{
		"created_at": -1,
		// "updated_at": -1,
	}
	perPage := 10
	pageNo := 1

	if per_page := c.QueryParam("per_page"); per_page != "" {
		perPage, _ = strconv.Atoi(per_page)
	}
	if page_no := c.QueryParam("page_no"); page_no != "" {
		pageNo, _ = strconv.Atoi(page_no)
	}

	serviceResp, err := h.service.ListSample(metaData, filterQuery, sortings, perPage, pageNo)
	if err != nil {
		return shared.RespFailure(c, "Internal Error", err)
	}

	var apiResp = []SampleListRespDto{}
	shared.JsonMarshaller(serviceResp["data"], &apiResp)

	var finalResp = []interface{}{}
	shared.JsonMarshaller(apiResp, &finalResp)

	return shared.RespSuccessWithPagination(c, "List retrieved successfully", finalResp, serviceResp["pagination"])
}
func (h *handler) DeleteSample(c echo.Context) error {
	metaData := shared.ApiMetaData{}
	shared.JsonMarshaller(c.Get("metaData"), &metaData)

	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		return shared.RespFailure(c, "Invalid ID", err)
	}

	filterQuery := map[string]interface{}{
		"id": id,
	}

	serviceResp, err := h.service.DeleteSample(metaData, filterQuery)
	fmt.Println("serviceResp", serviceResp)
	fmt.Println("err", err)
	fmt.Println("ffffffffffffffffffffff", filterQuery)
	if err != nil {
		return shared.RespFailure(c, "Internal Error", err)
	}

	return shared.RespSuccess(c, "Deleted Successfully", id)
}
