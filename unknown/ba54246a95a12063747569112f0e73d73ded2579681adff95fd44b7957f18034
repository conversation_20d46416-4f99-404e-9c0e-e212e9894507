{"name": "spiro", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "root": "apps/spiro", "sourceRoot": "apps/spiro", "tags": [], "implicitDependencies": ["enums", "shared"], "targets": {"build": {"executor": "@nx-go/nx-go:build", "options": {"outputPath": "dist/apps/spiro", "main": "{projectRoot}/main.go", "buildTarget": "binary"}, "dependsOn": ["^build"]}, "serve": {"executor": "@nx-go/nx-go:serve", "options": {"main": "{projectRoot}/main.go", "buildArgs": ["-mod=vendor"]}}, "test": {"executor": "@nx-go/nx-go:test"}, "lint": {"executor": "@nx-go/nx-go:lint"}}}