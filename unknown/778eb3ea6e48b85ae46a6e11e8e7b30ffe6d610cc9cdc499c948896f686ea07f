package onboarding

import (
	"gorm.io/datatypes"
)

type OnboardingRequest struct {
	FirstName   string         `json:"first_name"`
	LastName    string         `json:"last_name"`
	Name        string         `json:"name"`
	Email       string         `json:"email"`
	PhoneNumber string         `json:"phone_number"`
	StoreLogo   datatypes.JSON `json:"store_logo"`
	StoreBanner string         `json:"store_banner"`

	Website   string `json:"website"`
	StoreName string `json:"store_name"`

	ShortDescription       string         `json:"short_desc"`
	LongDescription        string         `json:"long_desc"`
	FssaiNumber            string         `json:"fssai_number"`
	StoreSymbol            string         `json:"store_symbol"`
	StoreEmail             string         `josn:"store_email"`
	StoreContactNumber     string         `json:"store_contact_number"`
	StoreContactPersonName string         `json:"store_contact_person_name"`
	Holidays               datatypes.JSON `json:"holidays,omitempty"`
	// Domain                 string         `json:"domain"`

	GSTNumber        string `json:"gstin"`
	PANNumber        string `json:"pan_card"`
	GstDoc           string `json:"gst_doc"`
	PanImage         string `json:"pan_image"`
	DigitalSignature string `json:"signature"`

	BankAccountHolderName string         `json:"account_holder_name"`
	BankName              string         `json:"bank_name"`
	AccountDetails        datatypes.JSON `json:"account_details"`
	BankAccountNumber     string         `json:"account_number"`
	BankIFSCCode          string         `json:"ifsc_code"`
	CancellerCheque       string         `json:"canceller_cheque"`
	UpiAddress            string         `json:"upi_address"`
	BranchName            string         `json:"branch_name"`

	Latitude  string `json:"latitude"`
	Longitude string `json:"longitude"`
	Address   *struct {
		AddressLine1 string `json:"address_line_1"`
		AddressLine2 string `json:"address_line_2"`
		AddressLine3 string `json:"address_line_3"`
		City         string `json:"city"`
		State        string `json:"state"`
		PinCode      string `json:"pin_code"`
		Country      string `json:"country"`
	} `json:"address"`
	StoreTimings []struct {
		Day    string `json:"day"`
		IsOpen bool   `json:"is_open"`
		Time   []struct {
			OpeningTime string `json:"opening_time"`
			ClosingTime string `json:"closing_time"`
		} `json:"time"`
	} `json:"store_timings"`
	IsOnboarded *bool `json:"is_onboarded"`
}

type LoginRequest struct {
	PhoneNumber string `json:"phone_number"`
}

type LoginResponse struct {
	NewUser bool   `json:"new_user"`
	UserId  string `json:"user_id"`
}

type UserDto struct {
	PhoneNumber string `json:"phone_number"`
	JWTToken    string `json:"jwt_token"`
	IgmToken    string `json:"igm_token"`
	ID          uint   `json:"id"`
	StoreId     uint   `json:"store_id"`
	BankId      uint   `json:"bank_id"`
	BusinessId  uint   `json:"business_id"`
}

type UserDetailApiRequest struct {
	Id                                uint   `json:"Id"`
	Mobile                            string `json:"Mobile"`
	Salutation                        string `json:"Salutation"`
	First_Name                        string `json:"First_Name"`
	Last_Name                         string `json:"Last_Name"`
	Identification_Document_Available string `json:"Identification_Document_Available"`
	Gender                            string `json:"Gender"`
	Country                           string `json:"Country"`
	State                             string `json:"State"`
	City                              string `json:"City"`
	SMS_Language                      string `json:"SMS_Language"`
	Lead_Source                       string `json:"Lead_Source"`
	Enquiry_Rating                    int    `json:"Enquiry_Rating"`
	Rating_Reason                     string `json:"Rating_Reason"`
	Sacco_Name                        string `json:"Sacco_Name"`
	Building                          string `json:"Building"`
	Street                            string `json:"Street"`
	Zip_Code                          string `json:"Zip_Code"`
	Latitude                          string `json:"Latitude"`
	Longitude                         string `json:"Longitude"`
	Verbal_Language                   string `json:"Verbal_Language"`
	Birth_Date                        string `json:"Birth_Date"`
	National_ID                       string `json:"National_ID"`
	KCB_Account_No                    string `json:"KCB_Account_No"`
	Kinga_Reference_ID                string `json:"Kinga_Reference_ID"`
	KRA_PIN                           string `json:"KRA_PIN"`

	//Default values
	Customer_Type           string `json:"Customer_Type"`
	Entity_Type			  string `json:"Entity Type"`

}

type UserDetailsApiResponse struct {
	Meta struct {
		Status bool `json:"status"`
	} `json:"meta"`
	Data struct {
		Id             uint   `json:"id"`
		Name           string `json:"name"`
		Email          string `json:"email"`
		DefaultAddress struct {
			City         string `json:"city"`
			State        string `json:"state"`
			PinCode      string `json:"pin_code"`
			AddressLine1 string `json:"address_line_1"`
		} `json:"default_address"`
		ContactFromTime string `json:"contact_from_time"`
		ContactToTime   string `json:"contact_to_time"`
	} `json:"data"`
}

type UserDetailApiResponse struct {

		Id                                uint   `json:"Id"`
		Mobile                            string `json:"Mobile"`
		Salutation                        string `json:"Salutation"`
		First_Name                        string `json:"First_Name"`
		Last_Name                         string `json:"Last_Name"`
		Identification_Document_Available string `json:"Identification_Document_Available"`
		Gender                            string `json:"Gender"`
		Country                           string `json:"Country"`
		State                             string `json:"State"`
		City                              string `json:"City"`
		SMS_Language                      string `json:"SMS_Language"`
		Lead_Source                       string `json:"Lead_Source"`
		Enquiry_Rating                    int    `json:"Enquiry_Rating"`
		Rating_Reason                     string `json:"Rating_Reason"`
		Sacco_Name                        string `json:"Sacco_Name"`
		Building                          string `json:"Building"`
		Street                            string `json:"Street"`
		Zip_Code                          string `json:"Zip_Code"`
		Latitude                          string `json:"Latitude"`
		Longitude                         string `json:"Longitude"`
		Verbal_Language                   string `json:"Verbal_Language"`
		Birth_Date                        string `json:"Birth_Date"`
		National_ID                       string `json:"National_ID"`
		KCB_Account_No                    string `json:"KCB_Account_No"`
		Kinga_Reference_ID                string `json:"Kinga_Reference_ID"`
		KRA_PIN                           string `json:"KRA_PIN"`

}

type StoreDetailsApiResponse struct {
	Meta struct {
		Status bool `json:"status"`
	} `json:"meta"`
	Data struct {
		Id                 uint   `json:"id"`
		Name               string `json:"name"`
		ShortDesc          string `json:"short_desc"`
		LongDesc           string `json:"long_desc"`
		Website            string `json:"website"`
		FssaiNumber        string `json:"fssai_number"`
		StoreSymbols       string `json:"store_symbols"`
		StoreBanner        string `json:"store_banner"`
		StoreContactNumber string `json:"store_contact_number"`
		StoreEmail         string `json:"store_email"`
		OrderMinimumValue  int    `json:"order_minimum_value"`
		FulfillmentTypeId  int    `json:"fulfillment_type_id"`
		DefaultAddress     struct {
			City         string `json:"city"`
			State        string `json:"state"`
			PinCode      string `json:"pin_code"`
			AddressLine1 string `json:"address_line_1"`
		} `json:"default_address"`
	} `json:"data"`
}

type BusinessDetailsApiResponse struct {
	Meta struct {
		Status bool `json:"status"`
	} `json:"meta"`
	Data struct {
		Id                uint   `json:"id"`
		BusinessName      string `json:"bussiness_name"`
		AccountHolderName string `json:"account_holder_name"`
		Gstin             string `json:"gstin"`
		PanCard           string `json:"pan_card"`
	} `json:"data"`
}

type BankDetailsApiResponse struct {
	Meta struct {
		Status bool `json:"status"`
	} `json:"meta"`
	Data struct {
		Id                uint   `json:"id"`
		AccountHolderName string `json:"account_holder_name"`
		AccountNumber     string `json:"account_number"`
		IfscCode          string `json:"ifsc_code"`
		BankName          string `json:"bank_name"`
	} `json:"data"`
}

type UserDetails struct {
	PhoneNumber     string `json:"phone_number,omitempty"`
	Name            string `json:"name"`
	Email           string `json:"email"`
	City            string `json:"city"`
	State           string `json:"state"`
	PinCode         string `json:"pin_code"`
	Address         string `json:"address"`
	ContactFromTime string `json:"contact_from_time"`
	ContactToTime   string `json:"contact_to_time"`
}

type StoreDetails struct {
	PhoneNumber        string `json:"phone_number,omitempty"`
	Name               string `json:"name"`
	ShortDesc          string `json:"short_desc"`
	LongDesc           string `json:"long_desc"`
	Website            string `json:"website"`
	FssaiNumber        string `json:"fssai_number"`
	StoreSymbols       string `json:"store_symbols"`
	StoreBanner        string `json:"store_banner"`
	StoreContactNumber string `json:"store_contact_number"`
	StoreEmail         string `json:"store_email"`
	City               string `json:"city"`
	State              string `json:"state"`
	PinCode            string `json:"pin_code"`
	Address            string `json:"address"`
	OrderMinimumValue  string `json:"order_minimum_value"`
	FulfillmentType    string `json:"fulfillment_type"`
}

type BusinessDetails struct {
	PhoneNumber       string `json:"phone_number,omitempty"`
	BusinessName      string `json:"business_name"`
	AccountHolderName string `json:"account_holder_name"`
	Gstin             string `json:"gstin"`
	PanCard           string `json:"pan_card"`
}

type BankDetails struct {
	PhoneNumber       string `json:"phone_number,omitempty"`
	AccountHolderName string `json:"account_holder_name"`
	AccountNumber     string `json:"account_number"`
	IfscCode          string `json:"ifsc_code"`
	BankName          string `json:"bank_name"`
}

type UpdateUserDetailsRequest struct {
	Name            string  `json:"name"`
	Email           string  `json:"email"`
	DefaultAddress  Address `json:"default_address"`
	SectionKey      string  `json:"section_key"`
	ContactFromTime string  `json:"contact_from_time"`
	ContactToTime   string  `json:"contact_to_time"`
}

type UpdateStoreDetailsRequest struct {
	Name               string  `json:"name"`
	Website            string  `json:"website"`
	StoreContactNumber string  `json:"store_contact_number"`
	StoreEmail         string  `json:"store_email"`
	LongDescription    string  `json:"long_desc"`
	StoreSymbols       string  `json:"store_symbols"`
	StoreBanner        string  `json:"store_banner"`
	FSSAINumber        string  `json:"fssai_number"`
	DefaultAddress     Address `json:"default_address"`
	SectionKey         string  `json:"section_key"`
	OrderMinimumValue  int     `json:"order_minimum_value"`
	FulfillmentTypeId  int     `json:"fulfillment_type_id"`
}

type UpdateBankDetailsRequest struct {
	AccountHolderName string `json:"account_holder_name"`
	AccountNumber     string `json:"account_number"`
	IFSCCode          string `json:"ifsc_code"`
	BankName          string `json:"bank_name"`
	SectionKey        string `json:"section_key"`
}

type UpdateBusinessDetailsRequest struct {
	BusinessName      string `json:"bussiness_name"`
	AccountHolderName string `json:"account_holder_name"`
	GSTIN             string `json:"gstin"`
	PanCard           string `json:"pan_card"`
	SectionKey        string `json:"section_key"`
}

type Address struct {
	AddressLine1 string `json:"address_line_1"`
	City         string `json:"city"`
	State        string `json:"state"`
	PinCode      string `json:"pin_code"`
}

type Response struct {
	Meta struct {
		Status bool `json:"status"`
	} `json:"meta"`
}
