package onboarding

import (
	// res "code_develop_go_0.0/backend_core/pkg/util/response"
	shared "libs/shared"
	"github.com/labstack/echo/v4"
)

type handler struct {
	service Service
}

var sellerHandler *handler

func NewHandler() *handler {
	if sellerHandler != nil {
		return sellerHandler
	}
	sellerHandler = &handler{
		NewService(),
	}
	return sellerHandler
}



func (h *handler) GetUserDetails(c echo.Context) error {

	var data LoginRequest
	err := c.Bind(&data)
	if err != nil {
		return shared.RasaMiddlewareError(c, err)
	}

	response, err := h.service.GetUserDetails(data)
	if err != nil {
		return shared.RasaMiddlewareError(c, err)
	}

	return shared.RasaMiddlewareSuccess(c, response)
}



func (h *handler) UpdateUserDetails(c echo.Context) error {

	var data UserDetailApiRequest
	err := c.Bind(&data)
	if err != nil {
		return shared.RasaMiddlewareError(c, err)
	}

	err = h.service.UpdateUserDetails(data)
	if err != nil {
		return shared.RasaMiddlewareError(c, err)
	}

	return shared.RasaMiddlewareSuccess(c, nil)
}