package users

import (
	// env "spiro/config"
	// "fmt"
	// shared "libs/shared"

	"github.com/labstack/echo/v4"
)

func (h *handler) Route(g *echo.Group) {

	// ============================== Sample Route =============================
	g.POST("/create", h.CreateSample, SampleCreateValidation)
	g.POST("/update/:id", h.UpdateSample, SampleUpdateValidation)
	g.GET("/get/:id", h.GetSample)
	g.DELETE("/delete/:id", h.DeleteSample)
	g.GET("", h.ListSample)
}
