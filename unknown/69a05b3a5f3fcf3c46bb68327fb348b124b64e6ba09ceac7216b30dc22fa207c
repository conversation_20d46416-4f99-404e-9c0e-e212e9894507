package product_management

import (
	"errors"
	"fmt"
	"strconv"
	"strings"

	flows "apps/spiro/controllers/whatsapp/flows"
	whatsapp "libs/shared/db_connectors/model"
	// cache "libs/shared/utils/cache"
	helpers "libs/shared/utils/helpers"
)

type Service interface {
	Route(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact, decryptedData flows.DataChannelDecrypted) (flows.DataChannelResponse, error)
}

type service struct {
}

var ServiceObj *service

func NewService() *service {
	if ServiceObj != nil {
		return ServiceObj
	}
	ServiceObj = &service{}
	return ServiceObj
}

func (s *service) Route(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact, decryptedData flows.DataChannelDecrypted) (flows.DataChannelResponse, error) {

	if decryptedData.Action == "INIT" {
		return s.Init(account, contact, decryptedData)
	}
	if decryptedData.Screen == "EV_MODELS" {
		return s.ScreenProducts(account, contact, decryptedData)
	}
	if decryptedData.Screen == "MODEL_DETAILS" {
		return s.ScreenProductView(account, contact, decryptedData)
	}
	
	return flows.DataChannelResponse{}, errors.New("screen not found")
}

func (s *service) Init(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact, decryptedData flows.DataChannelDecrypted) (flows.DataChannelResponse, error) {
	return s.Products(account, contact, decryptedData, "")
}

func (s *service) Products(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact, decryptedData flows.DataChannelDecrypted, pageNo string) (flows.DataChannelResponse, error) {

	if pageNo == "" {
		pageNo = "1"
	}

	data := ProductsPayload{
		Message:         "empty",
		MessageVisible:  false,
		Products:        []flows.FlowButtonGroup{},
		ProductsVisible: true,
		InitValues:      map[string]interface{}{"page_no": pageNo, "product": ""},
		Pages:           []flows.FlowButtonGroup{},
	}

	middlewareRequest := map[string]interface{}{
		"phone_number": contact.WhatsappNumber,
		"per_page":     20,
	}

	middlewareRequest["page_no"], _ = strconv.Atoi(pageNo)

	// var filtersResponse FiltersResponse
	// helpers.JsonMarshaller(decryptedData.Data, &filtersResponse)


	// productIds := cache.GetCacheKey(account.WhatsappNumber + ":" + contact.WhatsappNumber + ":FilteredProductIds")
	// if productIds != nil && len(middlewareRequest) == 3 {
	// 	if productIds == "" {
	// 		data.MessageVisible = true
	// 		data.Message = "No results for the applied filters"
	// 	} else {
	// 		middlewareRequest["product_ids"] = productIds
	// 	}
	// }

	responseInterface, err := helpers.UrlRequest(
		"POST",
		account.MiddlewareUrl+"/products",
		middlewareRequest,
		map[string]string{"Authorization": "Bearer " + account.MiddlewareToken},
	)
	if err != nil {
		return flows.DataChannelResponse{}, err
	}

	var response SellerMiddlewareProductResponse
	helpers.JsonMarshaller(responseInterface, &response)
	// helpers.PrettyPrint("middleware response",response)
	if !response.Success {
		return flows.DataChannelResponse{}, errors.New("error from middleware")
	}

	for _, product := range response.Data.Products {
		data.Products = append(data.Products, flows.FlowButtonGroup{
			ID:          product.ProductId,
			Title:       product.Title,
			Description: product.Text,
			Image     :  product.Image,
		})
	}

	if len(data.Products) == 0 {
		data.Products = append(data.Products, flows.FlowButtonGroup{
			ID:          "empty",
			Title:       "empty",
			Description: "",
		})
		data.ProductsVisible = false
		data.Message = "Currently you have no products"
		data.MessageVisible = true
	}

	if response.Data.TotalPages == 0 {
		response.Data.TotalPages = 1
	}
	for i := 1; i <= response.Data.TotalPages; i++ {
		data.Pages = append(data.Pages, flows.FlowButtonGroup{
			ID:          fmt.Sprint(i),
			Title:       fmt.Sprintf("Page %v", i),
			Description: "",
		})
	}

	dataChannelResponse := flows.DataChannelResponse{
		Version: decryptedData.Version,
		Screen:  "EV_MODELS",
		Data:    data,
	}

	return dataChannelResponse, nil
}

func (s *service) ScreenProducts(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact, decryptedData flows.DataChannelDecrypted) (flows.DataChannelResponse, error) {

	var dataChannelResponse flows.DataChannelResponse

	var response ProductsResponse
	helpers.JsonMarshaller(decryptedData.Data, &response)

	 if response.Action == "product_view" {
		return s.ProductView(account, contact, decryptedData, response)
	} else if response.Action == "page_no" {
		return s.Products(account, contact, decryptedData, response.PageNo)
	} else {
		dataChannelResponse = flows.DataChannelResponse{
			Version: decryptedData.Version,
			Screen:  "SUCCESS",
			Data: map[string]interface{}{
				"extension_message_response": map[string]interface{}{
					"params": map[string]interface{}{
						"flow_token": decryptedData.FlowToken,
					},
				},
			},
		}
	}
	return dataChannelResponse, nil
}

func (s *service) ProductView(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact, decryptedData flows.DataChannelDecrypted, response ProductsResponse) (flows.DataChannelResponse, error) {

	responseInterface, err := helpers.UrlRequest(
		"POST",
		account.MiddlewareUrl+"/products/view",
		map[string]interface{}{
			"phone_number": contact.WhatsappNumber,
			"product_id":   response.ProductId,
		},
		map[string]string{"Authorization": "Bearer " + account.MiddlewareToken},
	)
	if err != nil {
		return flows.DataChannelResponse{}, err
	}

	var middlewareResponse SellerMiddlewareProductViewResponse
	helpers.JsonMarshaller(responseInterface, &middlewareResponse)
	if !middlewareResponse.Success {
		return flows.DataChannelResponse{}, errors.New("error from middleware")
	}

	data := ProductViewPayload{
		ProductId:      response.ProductId,
		ProductName:    middlewareResponse.Data.ProductName,
		ProductDetails: middlewareResponse.Data.ProductDetails,
		ProductImage:   middlewareResponse.Data.ProductImages,
		Actions:        []flows.FlowButtonGroup{{ID: "update", Title: "Update Product", Description: ""}},
		ActionVisible:  true,
		ActionRequired: false,
	}

	lines := strings.Split(middlewareResponse.Data.ProductDetails, "\n")

	// Loop through each line to find the line containing "Status"
	var status string
	for _, line := range lines {
		if strings.Contains(line, "Status") {
			// Extract the status value
			status = strings.TrimSpace(strings.Split(line, ":")[1])
			break
		}
	}

	fmt.Println("Product Status=====>", status)


	

	if len(data.Actions) == 0 {
		data.Actions = append(data.Actions, flows.FlowButtonGroup{
			ID:          "empty",
			Title:       "",
			Description: "empty",
		})
	}

	dataChannelResponse := flows.DataChannelResponse{
		Version: decryptedData.Version,
		Screen:  "MODEL_DETAILS",
		Data:    data,
	}

	return dataChannelResponse, nil
}

func (s *service) ScreenProductView(account whatsapp.WhatsappAccount, contact whatsapp.WhatsappContact, decryptedData flows.DataChannelDecrypted) (flows.DataChannelResponse, error) {

	var response ProductViewResponse
	helpers.JsonMarshaller(decryptedData.Data, &response)

	 if response.Action == "" {
		return s.Products(account, contact, decryptedData, "")
	} 

	return flows.DataChannelResponse{}, nil
}


