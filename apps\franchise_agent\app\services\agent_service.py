import logging
import json
import os
import re
from typing import Dict, Any, List, Optional, Tuple
from enum import Enum
from pydantic import BaseModel, Field
from openai import OpenAI

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# Initialize OpenAI client with Gemini API
try:
    client = OpenAI(
        api_key=os.environ.get("GEMINI_API_KEY", "your_gemini_api_key_here"),
        base_url="https://generativelanguage.googleapis.com/v1beta/openai/"
    )
    logger.info("OpenAI client initialized with Gemini API")
except Exception as e:
    logger.error(f"Error initializing OpenAI client: {str(e)}")
    client = None

# Define function tools for each agent

# Initial Message Agent tools
initial_message_tools = [
    {
        "type": "function",
        "function": {
            "name": "create_welcome_message",
            "description": "Creates a welcoming first message for potential franchisees",
            "parameters": {
                "type": "object",
                "properties": {},
                "required": []
            }
        }
    }
]

# Requirements Collection Agent tools
requirements_collection_tools = [
    {
        "type": "function",
        "function": {
            "name": "extract_business_type",
            "description": "Extracts the business type from user input",
            "parameters": {
                "type": "object",
                "properties": {
                    "business_type": {
                        "type": "string",
                        "description": "The type of business the user is interested in (e.g., restaurant, retail, service)"
                    }
                },
                "required": ["business_type"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "extract_investment_capacity",
            "description": "Extracts the investment capacity from user input",
            "parameters": {
                "type": "object",
                "properties": {
                    "investment_capacity": {
                        "type": "number",
                        "description": "The amount of money the user is willing to invest in the franchise"
                    }
                },
                "required": ["investment_capacity"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "extract_location",
            "description": "Extracts the preferred location from user input (must be within Africa)",
            "parameters": {
                "type": "object",
                "properties": {
                    "location": {
                        "type": "string",
                        "description": "The location where the user wants to open the franchise (must be within Africa)"
                    }
                },
                "required": ["location"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "extract_experience",
            "description": "Extracts the business experience from user input",
            "parameters": {
                "type": "object",
                "properties": {
                    "experience": {
                        "type": "string",
                        "description": "The user's prior business experience"
                    }
                },
                "required": ["experience"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "generate_requirements_question",
            "description": "Generates a question to collect missing requirements",
            "parameters": {
                "type": "object",
                "properties": {
                    "missing_field": {
                        "type": "string",
                        "description": "The field that is missing (business_type, investment_capacity, location, experience)"
                    }
                },
                "required": ["missing_field"]
            }
        }
    }
]

# Lead Scoring Agent tools
lead_scoring_tools = [
    {
        "type": "function",
        "function": {
            "name": "score_lead",
            "description": "Scores the lead based on collected information",
            "parameters": {
                "type": "object",
                "properties": {
                    "score": {
                        "type": "number",
                        "description": "The lead score (0-100)"
                    },
                    "lead_quality": {
                        "type": "string",
                        "enum": ["Hot", "Warm", "Cold"],
                        "description": "The lead quality classification"
                    },
                    "explanation": {
                        "type": "string",
                        "description": "Explanation of the lead score and quality"
                    }
                },
                "required": ["score", "lead_quality", "explanation"]
            }
        }
    }
]

# Lead Nurturing Agent tools
lead_nurturing_tools = [
    {
        "type": "function",
        "function": {
            "name": "advance_nurturing_stage",
            "description": "Advances the nurturing stage when the LLM has completed the current stage's objectives",
            "parameters": {
                "type": "object",
                "properties": {
                    "current_stage": {
                        "type": "integer",
                        "description": "The current nurturing stage"
                    },
                    "next_stage": {
                        "type": "integer",
                        "description": "The next nurturing stage to advance to"
                    },
                    "reason": {
                        "type": "string",
                        "description": "Reason for advancing to the next stage"
                    }
                },
                "required": ["current_stage", "next_stage", "reason"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "provide_business_model_info",
            "description": "Provides information about franchise business models",
            "parameters": {
                "type": "object",
                "properties": {
                    "business_type": {
                        "type": "string",
                        "description": "The type of business to provide information about"
                    },
                    "information": {
                        "type": "string",
                        "description": "Detailed information about the business model"
                    }
                },
                "required": ["business_type", "information"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "provide_profit_margin_info",
            "description": "Provides information about profit margins for different franchise types",
            "parameters": {
                "type": "object",
                "properties": {
                    "business_type": {
                        "type": "string",
                        "description": "The type of business to provide profit margin information about"
                    },
                    "information": {
                        "type": "string",
                        "description": "Detailed information about profit margins"
                    }
                },
                "required": ["business_type", "information"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "answer_franchise_question",
            "description": "Answers specific questions about Spiro's franchise model in detail",
            "parameters": {
                "type": "object",
                "properties": {
                    "question_type": {
                        "type": "string",
                        "description": "The specific type of question being asked (e.g., micro-franchise, profit margins, business model)"
                    },
                    "answer": {
                        "type": "string",
                        "description": "A detailed and comprehensive answer to the specific question, including relevant facts from the knowledge base"
                    },
                    "follow_up": {
                        "type": "string",
                        "description": "A follow-up question to ask the user if they want more information"
                    }
                },
                "required": ["question_type", "answer", "follow_up"]
            }
        }
    }
]

class StateAction(str, Enum):
    """Enum for state update actions"""
    UPDATE = "update"
    DELETE = "delete"
    NONE = "none"

class UserInformation(BaseModel):
    """Model for storing user franchise information"""
    business_type: Optional[str] = None
    investment_capacity: Optional[int] = None
    location: Optional[str] = None
    experience: Optional[str] = None
    experience_years: Optional[int] = None  # Store years of experience as integer (0 for no experience)
    lead_score: Optional[int] = None
    lead_quality: Optional[str] = None
    lead_explanation: Optional[str] = None

    def is_complete(self) -> bool:
        """Check if all required fields are filled"""
        return all([
            self.business_type is not None,
            self.investment_capacity is not None,
            self.location is not None,
            self.experience is not None,
            # experience_years is not strictly required but should be set to 0 for no experience
            self.experience_years is not None
        ])

    def get_missing_fields(self) -> List[str]:
        """Get a list of missing required fields"""
        missing = []
        if self.business_type is None:
            missing.append("business_type")
        if self.investment_capacity is None:
            missing.append("investment_capacity")
        if self.location is None:
            missing.append("location")
        if self.experience is None:
            missing.append("experience")
        # Only add experience_years as missing if experience is also missing
        # This prevents asking about experience_years when experience is already collected
        if self.experience_years is None and self.experience is None:
            missing.append("experience_years")
        return missing

    def get_collected_fields(self) -> List[str]:
        """Get a list of collected fields"""
        collected = []
        if self.business_type is not None:
            collected.append("business_type")
        if self.investment_capacity is not None:
            collected.append("investment_capacity")
        if self.location is not None:
            collected.append("location")
        if self.experience is not None:
            collected.append("experience")
        if self.experience_years is not None:
            collected.append("experience_years")
        return collected

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary, excluding None values"""
        return {k: v for k, v in self.model_dump().items() if v is not None}

class StateUpdate(BaseModel):
    """Model for state updates"""
    key: str
    value: Any
    action: StateAction = StateAction.UPDATE

class AgentState(BaseModel):
    """Model for agent state"""
    data: Dict[str, Any] = {}
    user_info: UserInformation = Field(default_factory=UserInformation)

    def update(self, updates: List[StateUpdate]) -> None:
        """Update state based on state updates"""
        for update in updates:
            if update.action == StateAction.UPDATE:
                # Check if this is a user information field
                if update.key in UserInformation.model_fields:
                    setattr(self.user_info, update.key, update.value)
                    # Also save the value in data for backward compatibility
                    self.data[update.key] = update.value
                else:
                    self.data[update.key] = update.value
            elif update.action == StateAction.DELETE:
                if update.key in UserInformation.model_fields:
                    setattr(self.user_info, update.key, None)
                    # Also remove from data if present
                    if update.key in self.data:
                        del self.data[update.key]
                elif update.key in self.data:
                    del self.data[update.key]

    def get(self, key: str, default: Any = None) -> Any:
        """Get a value from state or user_info"""
        if key in UserInformation.model_fields:
            value = getattr(self.user_info, key)
            return value if value is not None else default
        return self.data.get(key, default)

    def set(self, key: str, value: Any) -> None:
        """Set a value in state or user_info"""
        if key in UserInformation.model_fields:
            setattr(self.user_info, key, value)
            # Also save in data for backward compatibility
            self.data[key] = value
        else:
            self.data[key] = value

    def delete(self, key: str) -> None:
        """Delete a key from state or user_info"""
        if key in UserInformation.model_fields:
            setattr(self.user_info, key, None)
            # Also remove from data if present
            if key in self.data:
                del self.data[key]
        elif key in self.data:
            del self.data[key]

    def to_dict(self) -> Dict[str, Any]:
        """Convert state to dictionary including user info"""
        result = self.data.copy()
        user_info_dict = self.user_info.to_dict()
        # Add user_info to result but also keep the top-level fields for backward compatibility
        result["user_info"] = user_info_dict
        for key, value in user_info_dict.items():
            result[key] = value
        return result

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AgentState':
        """Create an AgentState instance from a dictionary"""
        user_info_data = data.pop("user_info", {}) if "user_info" in data else {}

        # Create a new instance
        instance = cls(data=data)

        # Handle user_info if present in the data
        if user_info_data:
            instance.user_info = UserInformation(**user_info_data)
        else:
            # Extract user_info fields from the main data
            user_info_fields = {}
            for field in UserInformation.model_fields:
                if field in data:
                    user_info_fields[field] = data[field]

            if user_info_fields:
                instance.user_info = UserInformation(**user_info_fields)

        return instance

class AgentResponse(BaseModel):
    """Model for agent responses"""
    message: str
    state_updates: List[StateUpdate] = []
    should_progress: bool = False

    def to_dict(self) -> Dict[str, Any]:
        """Convert response to dictionary"""
        return {
            "message": self.message,
            "state_updates": [update.model_dump() for update in self.state_updates],
            "should_progress": self.should_progress
        }

    def to_json_response(self) -> str:
        """Convert response to standard JSON format"""
        state_fields = {}
        for update in self.state_updates:
            if update.action == StateAction.UPDATE:
                state_fields[update.key] = update.value

        return json.dumps({
            "response": self.message,
            "should_progress": "yes" if self.should_progress else "no",
            "state_field": state_fields
        }, ensure_ascii=False)

    @classmethod
    def from_json_response(cls, json_str: str) -> 'AgentResponse':
        """Create AgentResponse from standard JSON response format"""
        try:
            # Log the raw response for debugging
            logger.debug(f"Raw JSON response to parse: {json_str}")

            # Try multiple parsing strategies
            parsed_data = cls._try_parse_json(json_str)

            if parsed_data:
                message = parsed_data.get("response", "")
                should_progress = parsed_data.get("should_progress", "no").lower() == "yes"
                state_fields = parsed_data.get("state_field", {})

                state_updates = []
                for key, value in state_fields.items():
                    state_updates.append(StateUpdate(
                        key=key,
                        value=value,
                        action=StateAction.UPDATE
                    ))

                return cls(
                    message=message,
                    state_updates=state_updates,
                    should_progress=should_progress
                )
            else:
                # If JSON parsing fails completely, use the raw response as the message
                # This provides a fallback for when LLM returns plain text instead of JSON
                logger.warning("JSON parsing failed, using raw response as message")

                # Clean up the response text
                clean_message = json_str.strip()

                # Remove any markdown formatting if present
                if clean_message.startswith("```") and clean_message.endswith("```"):
                    lines = clean_message.split('\n')
                    if len(lines) > 2:
                        clean_message = '\n'.join(lines[1:-1]).strip()

                # If the message is too long or seems like an error, provide a default response
                if len(clean_message) > 500 or "error" in clean_message.lower() or not clean_message:
                    clean_message = "I need to collect accurate information about your business interests. Could you please provide more details?"

                return cls(message=clean_message, state_updates=[], should_progress=False)

        except Exception as e:
            logger.error(f"Error parsing JSON response: {str(e)}")
            logger.error(f"Failed JSON content: {json_str}")
            # Provide a meaningful fallback message instead of "Error processing response"
            fallback_message = "I need to collect accurate information about your business interests. Could you please provide more details?"
            return cls(message=fallback_message, state_updates=[], should_progress=False)

    @staticmethod
    def _try_parse_json(json_str: str) -> Dict[str, Any]:
        """Try multiple strategies to parse the JSON string"""
        # Strategy 1: Parse as-is
        try:
            return json.loads(json_str)
        except json.JSONDecodeError:
            logger.debug("Strategy 1 (parse as-is) failed")

        # Strategy 2: Strip whitespace and try again
        try:
            cleaned = json_str.strip()
            return json.loads(cleaned)
        except json.JSONDecodeError:
            logger.debug("Strategy 2 (strip whitespace) failed")

        # Strategy 3: Extract from code blocks
        if "```" in json_str:
            try:
                # Find content between code block delimiters
                start_pos = json_str.find("```")
                if start_pos != -1:
                    # Look for the end of the opening ```
                    content_start = json_str.find("\n", start_pos)
                    if content_start == -1:
                        content_start = start_pos + 3
                    else:
                        content_start += 1

                    end_pos = json_str.rfind("```")
                    if end_pos > content_start:
                        extracted_json = json_str[content_start:end_pos].strip()
                        logger.info(f"Extracted JSON from code block: {extracted_json[:100]}...")
                        return json.loads(extracted_json)
            except json.JSONDecodeError:
                logger.debug("Strategy 3 (extract from code blocks) failed")

        # Strategy 4: Search for JSON object pattern with better bracket matching
        try:
            # Look for the first { and find the matching }
            start_pos = json_str.find("{")
            if start_pos != -1:
                bracket_count = 0
                end_pos = start_pos
                for i in range(start_pos, len(json_str)):
                    if json_str[i] == '{':
                        bracket_count += 1
                    elif json_str[i] == '}':
                        bracket_count -= 1
                        if bracket_count == 0:
                            end_pos = i
                            break

                if bracket_count == 0 and end_pos > start_pos:
                    potential_json = json_str[start_pos:end_pos+1]
                    logger.info(f"Extracted potential JSON by bracket matching: {potential_json[:100]}...")
                    return json.loads(potential_json)
        except json.JSONDecodeError:
            logger.debug("Strategy 4 (bracket matching) failed")

        # Strategy 5: Try line by line
        lines = json_str.split("\n")
        for line in lines:
            line = line.strip()
            if line.startswith("{") and line.endswith("}"):
                try:
                    logger.info(f"Found JSON line: {line[:100]}...")
                    return json.loads(line)
                except json.JSONDecodeError:
                    continue

        # Strategy 6: Look for JSON-like patterns and try to fix common issues
        try:
            # Remove common prefixes/suffixes that might interfere
            cleaned = json_str.strip()

            # Remove "Here's the response:" or similar prefixes
            prefixes_to_remove = [
                "here's the response:",
                "here is the response:",
                "response:",
                "json:",
                "here's the json:",
                "here is the json:"
            ]

            for prefix in prefixes_to_remove:
                if cleaned.lower().startswith(prefix):
                    cleaned = cleaned[len(prefix):].strip()
                    break

            # Try to find and extract JSON from the cleaned text
            start_pos = cleaned.find("{")
            if start_pos != -1:
                bracket_count = 0
                end_pos = start_pos
                for i in range(start_pos, len(cleaned)):
                    if cleaned[i] == '{':
                        bracket_count += 1
                    elif cleaned[i] == '}':
                        bracket_count -= 1
                        if bracket_count == 0:
                            end_pos = i
                            break

                if bracket_count == 0 and end_pos > start_pos:
                    potential_json = cleaned[start_pos:end_pos+1]
                    logger.info(f"Extracted JSON after cleaning: {potential_json[:100]}...")
                    return json.loads(potential_json)
        except json.JSONDecodeError:
            logger.debug("Strategy 6 (pattern cleaning) failed")

        logger.warning("All JSON parsing strategies failed")
        return None

class Agent:
    """Base class for all agents"""
    def __init__(self, name: str):
        self.name = name
        self.tools = []

    def process(self, user_input: str, state: AgentState) -> AgentResponse:
        """Process user input and return a response"""
        # Unused parameters are expected in abstract method
        # pylint: disable=unused-argument
        raise NotImplementedError("Subclasses must implement process method")

    def process_json_response(self, json_response: str, state: AgentState) -> AgentResponse:
        """Process a JSON formatted response and update state"""
        agent_response = AgentResponse.from_json_response(json_response)

        # Debug log for state updates
        logger.info(f"Processing JSON response with updates: {[update.model_dump() for update in agent_response.state_updates]}")

        for update in agent_response.state_updates:
            # Debug log for state update
            logger.info(f"Applying state update: {update.model_dump()}")
            state.update([update])

            # Debug log for user info after update
            if update.key in UserInformation.model_fields:
                logger.info(f"Updated user_info.{update.key} = {getattr(state.user_info, update.key)}")

        return agent_response

    def can_handle(self, state: AgentState) -> bool:
        """Determine if this agent can handle the current state"""
        # Unused parameter is expected in abstract method
        # pylint: disable=unused-argument
        raise NotImplementedError("Subclasses must implement can_handle method")

    def generate_standard_response(self, message: str, should_progress: bool = False,
                                  state_updates: Dict[str, Any] = None) -> str:
        """Generate a standardized JSON response format"""
        state_fields = {}
        state_update_objects = []

        if state_updates:
            for key, value in state_updates.items():
                state_fields[key] = value
                state_update_objects.append(StateUpdate(
                    key=key,
                    value=value,
                    action=StateAction.UPDATE
                ))

        response = AgentResponse(
            message=message,
            state_updates=state_update_objects,
            should_progress=should_progress
        )

        return response.to_json_response()

class InitialMessageAgent(Agent):
    """Agent that creates the initial welcome message"""
    def __init__(self):
        super().__init__("InitialMessageAgent")
        self.tools = initial_message_tools

    def process(self, user_input: str, state: AgentState) -> AgentResponse:
        """Create initial welcome message using LLM with clear instructions"""
        # Parameters not used in this implementation
        # pylint: disable=unused-argument
        if not client:
            # Fallback if LLM client is not available
            welcome_message = "What type of business are you currently operating or interested in? 🏢"
            logger.info("Using fallback welcome message (no LLM client)")
        else:
            try:
                # Call LLM with very explicit instructions
                system_prompt = (
                    "You are a WhatsApp chatbot for Spiro, an electric vehicle (EV) company. You ARE the Spiro EV Franchise Agent. "
                    "Your task is to write a first message that YOU (the chatbot) will send to potential franchisees. "
                    "This is NOT a template for a human agent - YOU are the agent speaking directly to the user. "
                    "\n\nYour message should: "
                    "\n1. SKIP ANY INTRODUCTION - do NOT introduce yourself or the company "
                    "\n2. DIRECTLY ask about their business background as your first question "
                    "\n3. Be professional but friendly and conversational "
                    "\n4. Include appropriate emoji (1-2 maximum) "
                    "\n5. Keep it concise (1-2 sentences maximum) "
                    "\n6. ALWAYS end with a rhetorical question to encourage user engagement - NEVER end with a declarative sentence"
                    "\n\nIMPORTANT: Write in first person as if YOU are directly speaking to the user. DO NOT write a template with placeholders."
                    "\n\nCRITICAL: DO NOT introduce yourself or the company. ONLY ask the business background question directly."
                    "\n\nCRITICAL: CONTACT INFORMATION RESTRICTIONS:"
                    "\n- NEVER provide any phone numbers or contact numbers"
                    "\n- DO NOT make up, hallucinate, or guess any phone numbers"
                    "\n- If users ask for contact information, politely inform them that a human agent will contact them"
                    "\n- DO NOT provide any website links or contact information"
                    "\n\nCRITICAL: HANDLING ILLEGAL/HARMFUL CONTENT:"
                    "\n- If user provides illegal information, self-harm content, or unethical business practices, respond STRICTLY with:"
                    "\n- 'We do not endorse harmful practices. Kindly enter a valid business experience that aligns with legal and ethical standards.'"
                    "\n- Do NOT engage with, acknowledge, or repeat any illegal/harmful content"
                    "\n- Immediately redirect to appropriate business-related information"
                    "\n- Maintain professional boundaries at all times"
                    "\n\nAVOID REPETITIVE MESSAGES:"
                    "\n- You have access to previous conversation history"
                    "\n- Never send the same or similar messages repeatedly"
                    "\n- Always consider what has already been discussed"
                    "\n- Adapt your responses based on the conversation flow"
                    "\n- If you've already asked a question, don't ask it again"
                    "\n\nENGAGEMENT REQUIREMENT:"
                    "\n- ALWAYS end your message with a rhetorical question to keep the conversation flowing"
                    "\n- Never end with statements like 'Please let me know' or 'I look forward to hearing from you'"
                    "\n- Examples of good rhetorical endings: 'What type of business interests you most?', 'Which industry catches your attention?'"
                )

                response = client.chat.completions.create(
                    model="gemini-2.0-flash",
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": "Write the first message that you (the Spiro EV Franchise Agent chatbot) will send to a potential franchisee."}
                    ]
                )

                # Use the LLM-generated content if available, otherwise use fallback message
                fallback_message = "What type of business are you currently operating or interested in? 🏢"

                if hasattr(response, 'choices') and len(response.choices) > 0 and hasattr(response.choices[0], 'message') and hasattr(response.choices[0].message, 'content') and response.choices[0].message.content.strip():
                    welcome_message = response.choices[0].message.content
                    logger.info(f"Using LLM-generated welcome message: {welcome_message[:100]}...")
                else:
                    welcome_message = fallback_message
                    logger.info("Using fallback welcome message (LLM response issue)")
            except Exception as e:
                logger.error(f"Error calling LLM for welcome message: {str(e)}")
                welcome_message = "What type of business are you currently operating or interested in? 🏢"
                logger.info("Using fallback welcome message (exception)")

        # Create state updates to transition to the next agent
        state_updates = [
            StateUpdate(key="current_agent", value="RequirementsCollectionAgent", action=StateAction.UPDATE),
            StateUpdate(key="conversation_started", value=True, action=StateAction.UPDATE)
        ]

        return AgentResponse(message=welcome_message, state_updates=state_updates)

    def can_handle(self, state: AgentState) -> bool:
        """This agent handles only the initial message"""
        return not state.get("conversation_started", False)

class RequirementsCollectionAgent(Agent):
    """Agent that collects business requirements"""
    def __init__(self):
        super().__init__("RequirementsCollectionAgent")
        self.tools = requirements_collection_tools
        self.required_fields = [
            "business_type",
            "investment_capacity",
            "location",
            "experience"
        ]

    def process(self, user_input: str, state: AgentState) -> AgentResponse:
        """Process user input to collect requirements using LLM"""
        # Use UserInformation model methods to check requirements status
        collected_fields = state.user_info.get_collected_fields()
        missing_fields = state.user_info.get_missing_fields()

        # Additional check to ensure experience is not in missing_fields if it's already collected
        if "experience" in collected_fields and "experience" in missing_fields:
            missing_fields.remove("experience")

        # Additional check to ensure experience_years is not in missing_fields if experience is already collected
        if "experience" in collected_fields and "experience_years" in missing_fields:
            missing_fields.remove("experience_years")

        # Debug log for user information state
        logger.info(f"Current user_info state: {state.user_info.to_dict()}")
        logger.info(f"Collected fields: {collected_fields}")
        logger.info(f"Missing fields after additional checks: {missing_fields}")

        state_updates = []
        standard_response = None

        # Use LLM to extract information if available
        if client and user_input:
            try:
                # Create a comprehensive system prompt for collecting all requirements
                system_prompt = (
                    "You are the Spiro EV Franchise Agent chatbot, collecting essential information from potential franchisees. "
                    "Your task is to have a natural conversation while systematically gathering the following requirements one by one:\n"
                    "1. business_type: The type of business the user is interested in or has experience with\n"
                    "2. investment_capacity: The amount the user is willing to invest (in Kenyan Shillings - KES) - ALWAYS convert to an integer value - MUST be greater than 0\n"
                    "3. location: Where the user wants to establish their franchise (MUST be within Africa only)\n"
                    "4. experience: The user's prior business experience\n\n"

                    "IMPORTANT ROLE BOUNDARIES:\n"
                    "- You are STRICTLY a Spiro franchise representative chatbot\n"
                    "- ONLY discuss topics related to Spiro franchising\n"
                    "- DO NOT provide help, advice, or information on any unrelated topics\n"
                    "- If users ask about unrelated topics, politely redirect them to franchise-related discussion\n"
                    "- If users provide inappropriate, illegal, or nonsensical information, politely ask them to provide valid information\n\n"

                    "CRITICAL: CONTACT INFORMATION RESTRICTIONS:\n"
                    "- NEVER provide any phone numbers or contact numbers\n"
                    "- DO NOT make up, hallucinate, or guess any phone numbers\n"
                    "- If users ask for contact information, politely inform them that a human agent will contact them\n"
                    "- DO NOT provide any website links or contact information\n\n"

                    "AVOID REPETITIVE MESSAGES:\n"
                    "- You have access to the complete conversation history\n"
                    "- NEVER send the same or similar messages repeatedly\n"
                    "- Always check what has already been discussed before responding\n"
                    "- If you've already asked a specific question, don't ask it again\n"
                    "- Adapt your responses based on previous user answers\n"
                    "- Build upon the conversation naturally without repeating information\n"
                    "- If a user has already provided information, acknowledge it and move forward\n\n"

                    "Current status:\n"
                )

                # Add information about collected fields
                if collected_fields:
                    system_prompt += "COLLECTED INFORMATION:\n"
                    for field in collected_fields:
                        system_prompt += f"- {field}: {getattr(state.user_info, field, 'Not specified')}\n"
                else:
                    system_prompt += "- No information collected yet\n"

                # Add information about missing fields
                system_prompt += "\nSTILL NEEDED:\n"
                for field in missing_fields:
                    system_prompt += f"- {field}\n"

                # Add guidance for extraction and next steps
                system_prompt += (
                    "\nINSTRUCTIONS:\n"
                    "1. Extract any missing information from the user's message\n"
                    "2. If all requirements are collected, ask the user if they're ready to move to the next step (DO NOT mention human agents - that comes later)\n"
                    "3. If requirements are still missing, ask about the NEXT missing requirement in a conversational way\n"
                    "4. Focus on one requirement at a time to avoid overwhelming the user\n"
                    "5. Be professional, friendly, and concise\n"
                    "6. Use appropriate emoji (1-2 maximum) to maintain a friendly tone\n"
                    "7. CRITICAL: When all requirements are collected, ask for user confirmation like 'Are you ready to move to the next step?' before transitioning\n"
                    "8. ALWAYS end your response with a rhetorical question to encourage user engagement - NEVER end with a declarative sentence\n"
                    "\nHANDLING SPECIFIC RESPONSES:\n"
                    "1. For business type: When users say 'not interested', 'don't know', etc., understand this as 'No specific business type preference' or 'Undecided on business type'. DO NOT keep asking repeatedly if they've clearly indicated they don't have a preference.\n"
                    "2. For experience: When users respond with 'no', 'none', 'don't know', etc., understand this as 'No prior business experience'. Be specific about what the user lacks experience in.\n"
                    "3. For investment capacity: ALWAYS convert to INTEGER values in Kenyan Shillings (KES), even if users provide values like '1 crore', '1 million', etc. For example:\n"
                    "   - '1 million KES' → 1000000\n"
                    "   - '1 crore KES' → 10000000\n"
                    "   - '50 lakhs KES' → 5000000\n"
                    "   - '1 lakh KES' → 100000\n"
                    "   - '2.5 million KES' → 2500000\n"
                    "   CRITICAL: Investment capacity MUST be greater than 0. If the user says '0 KES' or any equivalent, politely explain that a positive investment amount is required for franchise opportunities.\n"
                    "\n4. For location: CRITICAL LOCATION RESTRICTIONS:\n"
                    "   - ONLY accept locations within Africa (countries like Kenya, Uganda, Rwanda, Nigeria, Ghana, South Africa, Tanzania, Ethiopia, etc.)\n"
                    "   - If user provides a location outside Africa (like USA, UK, India, China, etc.), politely inform them that Spiro franchises are currently only available in African countries\n"
                    "   - Only accept the location after confirming it's a valid African location\n"
                    "   - If unsure about a location, ask for clarification: 'Could you please specify which country this city is in?'\n"
                    "\n5. For experience: When users indicate they have experience, ASK THEM HOW MANY YEARS of experience they have. If they have no experience, understand this as 0 years.\n"
                    "\n6. HANDLING INVALID INFORMATION: If a user provides information that is clearly invalid, nonsensical, or inappropriate:\n"
                    "   - Politely ask them to provide valid information\n"
                    "   - Explain what kind of information you need\n"
                    "   - Do not acknowledge or repeat the inappropriate content\n"
                    "   - Maintain a professional tone\n"
                    "\n7. CRITICAL: HANDLING ILLEGAL/HARMFUL CONTENT:\n"
                    "   - If user provides illegal information, self-harm content, or unethical business practices, respond STRICTLY with:\n"
                    "   - 'We do not endorse harmful practices. Kindly enter a valid business experience that aligns with legal and ethical standards.'\n"
                    "   - Do NOT engage with, acknowledge, or repeat any illegal/harmful content\n"
                    "   - Immediately redirect to appropriate business-related information\n"
                    "   - Maintain professional boundaries at all times\n"
                    "\nCRITICAL INSTRUCTIONS FOR ALREADY COLLECTED INFORMATION:\n"
                    "1. Focus first on extracting information from the user's message before asking new questions\n"
                    "2. YOU are responsible for understanding and processing the information - do not rely on patterns\n"
                    "3. If a user repeatedly says they don't know or aren't interested, STOP asking and move on\n"
                    "4. Pay close attention to the conversation history to avoid asking the same question repeatedly\n"
                    "5. If the user has clearly indicated they don't have a preference, accept that and move on\n"
                    "6. NEVER ask for information that has already been provided - check the COLLECTED INFORMATION section carefully\n"
                    "7. If experience information is already collected, DO NOT ask about experience again under ANY circumstances\n"
                    "8. If years of experience is already collected, DO NOT ask about years of experience again\n"
                    "9. ONLY collect information related to franchise requirements - do not ask for personal information beyond what's needed\n"
                    "10. If a user tries to change the subject to something unrelated to Spiro franchising, politely redirect them back to the franchise discussion\n"
                    "\n\nEXPERIENCE HANDLING - CRITICAL INSTRUCTIONS:\n"
                    "1. BEFORE asking ANY question about experience, ALWAYS check if experience information is already collected\n"
                    "2. If experience information is already collected (regardless of what it is), NEVER ask about experience again\n"
                    "3. When a user indicates they have business experience, ask them how many years of experience they have ONLY if this information is NOT already collected\n"
                    "4. Track the years as a number\n"
                    "5. If they have no experience, understand this as 0 years\n"
                    "6. ALWAYS include both descriptive experience information and years of experience in your understanding\n"
                    "7. If they don't specify years but indicate experience, ask them directly: 'How many years of experience do you have?'\n"
                    "8. If years of experience is already known (including 0), DO NOT ask about years of experience again\n"
                    "9. If the user has no prior experience, automatically understand this as 0 years without explicitly asking or mentioning it\n"
                    "\n\nDYNAMIC RESPONSE GENERATION:\n"
                    "1. NEVER use hardcoded messages or conditions\n"
                    "2. Generate ALL responses dynamically based on the user's input\n"
                    "3. Maintain a natural conversation flow while collecting required information\n"
                    "4. Adapt your questions based on previous responses\n"
                    "5. NEVER mention internal data structures, fields, or state in your responses\n"
                    "6. DO NOT tell the user that you are 'storing' or 'setting' information in fields or states\n"
                    "7. Speak naturally as a human representative would - avoid technical or system-like language\n"
                    "8. When confirming information, simply acknowledge it naturally without mentioning how it's being tracked\n"
                    "9. If the user provides invalid, nonsensical, or inappropriate information:\n"
                    "   - Respond with a polite request for valid information\n"
                    "   - Example: 'I need to collect accurate information about your business interests. Could you please provide more details?'\n"
                    "   - Do not acknowledge or repeat inappropriate content\n"
                    "10. If the user tries to discuss unrelated topics:\n"
                    "   - Politely redirect them back to the franchise discussion\n"
                    "   - Example: 'I'm here specifically to help with Spiro franchise inquiries. Let's continue discussing your franchise requirements.'\n"
                    "11. ALWAYS maintain your role as a Spiro franchise representative - do not engage with requests for other services or information\n"
                    "12. CRITICAL: HANDLING ILLEGAL/HARMFUL CONTENT:\n"
                    "   - If user provides illegal information, self-harm content, or unethical business practices, respond STRICTLY with:\n"
                    "   - 'We do not endorse harmful practices. Kindly enter a valid business experience that aligns with legal and ethical standards.'\n"
                    "   - Do NOT engage with, acknowledge, or repeat any illegal/harmful content\n"
                    "   - Immediately redirect to appropriate business-related information\n"
                    "   - Maintain professional boundaries at all times\n\n"

                    "You MUST respond in this JSON format:\n"
                    '{"response": "Your message to the user", "should_progress": "yes/no", "state_field": {"field_name1": "value1", "field_name2": "value2"}}\n'
                    "\nPut your conversational response in the 'response' field.\n"
                    "Use 'should_progress: yes' ONLY if all requirements are collected AND the user has confirmed they're ready to move to the next step.\n"
                    "In 'state_field', include any fields you've extracted from the user's message (e.g., business_type, investment_capacity, experience, experience_years, etc.).\n"
                    "CRITICAL: Always include state_field updates when you extract information!\n"
                    "CRITICAL: Do NOT set should_progress to yes just because all requirements are collected - wait for user confirmation first!\n"
                    "CRITICAL: ALWAYS end your 'response' with a rhetorical question to encourage user engagement - NEVER end with a declarative sentence!"
                    "\n\nRESPONSE FORMAT EXAMPLE (for structure reference only, do not use this content):\n"
                    '{"response": "Thank you for that information! What is your investment budget for the franchise?", "should_progress": "no", "state_field": {"business_type": "retail"}}\n'
                    "\nCRITICAL: Return ONLY the raw JSON object. DO NOT wrap it in markdown code blocks (```). DO NOT include any additional text or formatting. Your entire response must be valid JSON."
                )

                # Get conversation history from state
                conversation_history = state.get("messages", [])

                # Build messages array with history
                messages = [{"role": "system", "content": system_prompt}]

                # Add more conversation history for better context
                if conversation_history:
                    # Add up to 5 most recent messages for better context
                    for msg in conversation_history[-10:]:
                        messages.append({"role": msg.get("role", "user"), "content": msg.get("content", "")})

                # Add the current user input if not already included in history
                if user_input and (not conversation_history or conversation_history[-1].get("content") != user_input):
                    messages.append({"role": "user", "content": user_input})

                # Call LLM for information extraction and response generation
                logger.info(f"Sending messages to LLM for requirements collection: {[msg.get('role', '') for msg in messages]}")

                response = client.chat.completions.create(
                    model="gemini-2.0-flash",
                    messages=messages
                )

                # Process the response
                if hasattr(response, 'choices') and len(response.choices) > 0 and hasattr(response.choices[0], 'message') and hasattr(response.choices[0].message, 'content'):
                    content = response.choices[0].message.content

                    # Print the full response for debugging
                    logger.debug(f"FULL LLM RESPONSE: {content}")

                    # Try to parse content as JSON
                    try:
                        standard_response = self.process_json_response(content, state)

                        # Extract the message and check for should_progress
                        response_message = standard_response.message
                        should_progress = standard_response.should_progress

                        # If should_progress is true, move directly to lead nurturing agent (skipping scoring)
                        if should_progress:
                            logger.info("Should progress is true, transitioning directly to LeadNurturingAgent")
                            state_updates.append(StateUpdate(key="current_agent", value="LeadNurturingAgent", action=StateAction.UPDATE))
                            standard_response.state_updates.append(StateUpdate(key="current_agent", value="LeadNurturingAgent", action=StateAction.UPDATE))

                        return standard_response

                    except json.JSONDecodeError as json_err:
                        logger.warning(f"LLM did not return valid JSON: {json_err}")
                        logger.warning(f"Invalid JSON content: {content}")

                    # If JSON parsing fails, extract information manually
                    # Extract structured information from the response
                    # Look for business type
                    if "business_type" in missing_fields:
                        business_type_patterns = [
                            r"business\s*type[:\s]\s*([\w\s]+)",
                            r"business[:\s]\s*([\w\s]+)",
                            r"type\s*of\s*business[:\s]\s*([\w\s]+)"
                        ]
                        for pattern in business_type_patterns:
                            match = re.search(pattern, content, re.IGNORECASE)
                            if match:
                                business_type = match.group(1).strip()
                                if business_type and len(business_type) > 0:
                                    logger.info(f"Extracted business_type: {business_type}")
                                    state_updates.append(StateUpdate(key="business_type", value=business_type, action=StateAction.UPDATE))
                                    missing_fields.remove("business_type")
                                    collected_fields.append("business_type")
                                    break

                    # Look for investment capacity
                    if "investment_capacity" in missing_fields:
                        # First try to extract from JSON state_field if available
                        try:
                            json_data = json.loads(content)
                            if "state_field" in json_data and "investment_capacity" in json_data["state_field"]:
                                investment_capacity = int(json_data["state_field"]["investment_capacity"])

                                # Reject 0 dollars as investment amount
                                if investment_capacity <= 0:
                                    logger.info(f"Rejecting zero or negative investment capacity from JSON: {investment_capacity}")
                                    # Don't update state, keep the field as missing
                                    raise ValueError("Investment capacity must be greater than 0")

                                logger.info(f"Extracted investment_capacity from JSON: {investment_capacity}")
                                state_updates.append(StateUpdate(key="investment_capacity", value=investment_capacity, action=StateAction.UPDATE))
                                missing_fields.remove("investment_capacity")
                                collected_fields.append("investment_capacity")
                        except (json.JSONDecodeError, ValueError, KeyError):
                            # If JSON extraction fails, try regex patterns
                            # Add patterns for common currency expressions
                            investment_patterns = [
                                r"investment\s*capacity[:\s]\s*\$?([\d,]+)",
                                r"investment[:\s]\s*\$?([\d,]+)",
                                r"budget[:\s]\s*\$?([\d,]+)",
                                r"\$([\d,]+)",
                                # Patterns for million, billion, crore, lakh
                                r"([\d.]+)\s*million",
                                r"([\d.]+)\s*billion",
                                r"([\d.]+)\s*crore",
                                r"([\d.]+)\s*cr",
                                r"([\d.]+)\s*lakh",
                                r"([\d.]+)\s*lac"
                            ]

                            for pattern in investment_patterns:
                                match = re.search(pattern, content, re.IGNORECASE)
                                if match:
                                    investment_str = match.group(1).replace(',', '')
                                    try:
                                        # Convert based on the unit
                                        if "million" in pattern:
                                            investment_capacity = int(float(investment_str) * 1000000)
                                        elif "billion" in pattern:
                                            investment_capacity = int(float(investment_str) * 1000000000)
                                        elif "crore" in pattern or "cr" in pattern:
                                            investment_capacity = int(float(investment_str) * 10000000)  # 1 crore = 10 million
                                        elif "lakh" in pattern or "lac" in pattern:
                                            investment_capacity = int(float(investment_str) * 100000)  # 1 lakh = 100,000
                                        else:
                                            investment_capacity = int(investment_str)

                                        # Reject 0 dollars as investment amount
                                        if investment_capacity <= 0:
                                            logger.info(f"Rejecting zero or negative investment capacity: {investment_capacity}")
                                            continue  # Skip this match and try other patterns

                                        logger.info(f"Extracted investment_capacity: {investment_capacity}")
                                        state_updates.append(StateUpdate(key="investment_capacity", value=investment_capacity, action=StateAction.UPDATE))
                                        missing_fields.remove("investment_capacity")
                                        collected_fields.append("investment_capacity")
                                        break
                                    except ValueError:
                                        logger.error(f"Error converting investment capacity: {investment_str}")

                    # Look for location
                    if "location" in missing_fields:
                        location_patterns = [
                            r"location[:\s]\s*([\w\s,]+)",
                            r"place[:\s]\s*([\w\s,]+)",
                            r"area[:\s]\s*([\w\s,]+)",
                            r"city[:\s]\s*([\w\s,]+)"
                        ]
                        for pattern in location_patterns:
                            match = re.search(pattern, content, re.IGNORECASE)
                            if match:
                                location = match.group(1).strip()
                                if location and len(location) > 0:
                                    logger.info(f"Extracted location: {location}")
                                    state_updates.append(StateUpdate(key="location", value=location, action=StateAction.UPDATE))
                                    missing_fields.remove("location")
                                    collected_fields.append("location")
                                    break

                    # Look for experience - but ONLY if it's not already collected
                    if "experience" in missing_fields and "experience" not in collected_fields:
                        # First try to extract from JSON state_field if available
                        try:
                            json_data = json.loads(content)
                            if "state_field" in json_data and "experience" in json_data["state_field"]:
                                experience = json_data["state_field"]["experience"]
                                # Make sure it's detailed enough
                                if experience.lower() in ["no", "none", "no experience", "don't know", "not sure"]:
                                    experience = "No prior business experience"
                                    # Set experience_years to 0 for no experience
                                    state_updates.append(StateUpdate(key="experience_years", value=0, action=StateAction.UPDATE))
                                logger.info(f"Extracted experience from JSON: {experience}")
                                state_updates.append(StateUpdate(key="experience", value=experience, action=StateAction.UPDATE))

                                # Also check for experience_years in the JSON
                                if "experience_years" in json_data["state_field"]:
                                    try:
                                        experience_years = int(json_data["state_field"]["experience_years"])
                                        logger.info(f"Extracted experience_years from JSON: {experience_years}")
                                        state_updates.append(StateUpdate(key="experience_years", value=experience_years, action=StateAction.UPDATE))
                                    except (ValueError, TypeError):
                                        # If conversion fails, try to extract years from the experience text
                                        years_match = re.search(r'(\d+)\s*years?', experience, re.IGNORECASE)
                                        if years_match:
                                            experience_years = int(years_match.group(1))
                                            logger.info(f"Extracted experience_years from text: {experience_years}")
                                            state_updates.append(StateUpdate(key="experience_years", value=experience_years, action=StateAction.UPDATE))
                                        elif experience.lower() in ["no", "none", "no experience", "don't know", "not sure"]:
                                            # Set to 0 for no experience
                                            state_updates.append(StateUpdate(key="experience_years", value=0, action=StateAction.UPDATE))

                                missing_fields.remove("experience")
                                collected_fields.append("experience")
                        except (json.JSONDecodeError, ValueError, KeyError):
                            # If JSON extraction fails, try regex patterns
                            experience_patterns = [
                                r"experience[:\s]\s*([\w\s,]+)",
                                r"background[:\s]\s*([\w\s,]+)",
                                r"worked\s*in[:\s]\s*([\w\s,]+)",
                                r"years\s*of\s*experience[:\s]\s*([\w\s,]+)"
                            ]

                            # First check for negative responses
                            negative_patterns = [
                                r"no\s+experience",
                                r"don't\s+have\s+experience",
                                r"don't\s+know",
                                r"not\s+sure",
                                r"\bno\b",
                                r"\bnone\b"
                            ]

                            for pattern in negative_patterns:
                                if re.search(pattern, content, re.IGNORECASE):
                                    experience = "No prior business experience"
                                    logger.info(f"Extracted negative experience: {experience}")
                                    state_updates.append(StateUpdate(key="experience", value=experience, action=StateAction.UPDATE))
                                    # Set experience_years to 0 for no experience
                                    state_updates.append(StateUpdate(key="experience_years", value=0, action=StateAction.UPDATE))
                                    missing_fields.remove("experience")
                                    collected_fields.append("experience")
                                    break

                            # If no negative pattern matched, try the regular patterns
                            if "experience" in missing_fields:
                                for pattern in experience_patterns:
                                    match = re.search(pattern, content, re.IGNORECASE)
                                    if match:
                                        experience = match.group(1).strip()
                                        if experience and len(experience) > 0:
                                            # Make sure it's detailed enough
                                            if experience.lower() in ["no", "none", "don't know", "not sure"]:
                                                experience = "No prior business experience"
                                                # Set experience_years to 0 for no experience
                                                state_updates.append(StateUpdate(key="experience_years", value=0, action=StateAction.UPDATE))
                                            else:
                                                # Try to extract years from the experience text
                                                years_match = re.search(r'(\d+)\s*years?', experience, re.IGNORECASE)
                                                if years_match:
                                                    experience_years = int(years_match.group(1))
                                                    logger.info(f"Extracted experience_years from text: {experience_years}")
                                                    state_updates.append(StateUpdate(key="experience_years", value=experience_years, action=StateAction.UPDATE))

                                            logger.info(f"Extracted experience: {experience}")
                                            state_updates.append(StateUpdate(key="experience", value=experience, action=StateAction.UPDATE))
                                            missing_fields.remove("experience")
                                            collected_fields.append("experience")
                                            break

                    # Get the response message directly from LLM
                    response_message = content

            except Exception as e:
                logger.error(f"Error calling LLM for requirements collection: {str(e)}")
                # Use fallback response message on error
                response_message = self._generate_fallback_response(missing_fields, collected_fields)
        else:
            # Use fallback response if LLM is not available
            response_message = self._generate_fallback_response(missing_fields, collected_fields)

        # Log the current state of requirements collection
        logger.info(f"Requirements collection status - Collected: {collected_fields}, Missing: {missing_fields}")

        # Check if all requirements are collected - but don't auto-transition, wait for user confirmation
        should_progress = False
        if not missing_fields:
            # Check if user has confirmed they're ready to proceed
            user_confirmation_phrases = [
                "yes", "ready", "ok", "sure", "proceed", "continue", "next step",
                "move forward", "go ahead", "let's go", "i'm ready", "im ready"
            ]
            user_confirmed = any(phrase in user_input.lower() for phrase in user_confirmation_phrases) if user_input else False

            if user_confirmed:
                should_progress = True
                # User confirmed they're ready, transition to lead nurturing
                logger.info("All requirements collected and user confirmed readiness, transitioning to LeadNurturingAgent")
                state_updates.append(StateUpdate(key="current_agent", value="LeadNurturingAgent", action=StateAction.UPDATE))
            else:
                # All requirements collected but user hasn't confirmed - ask for confirmation
                logger.info("All requirements collected but waiting for user confirmation")

        # If we didn't already create and return a standard response from JSON
        if standard_response is None:
            return AgentResponse(
                message=response_message,
                state_updates=state_updates,
                should_progress=should_progress
            )

    def _generate_fallback_response(self, missing_fields: List[str], collected_fields: List[str]) -> str:
        """Generate a fallback response when LLM is not available"""
        # Define fallback questions for each field - using natural language that doesn't leak internal state
        # All questions end with rhetorical questions to encourage engagement
        field_questions = {
            "business_type": "What type of business are you currently operating or interested in? This helps us understand your background better. Which industry catches your attention most? 🏢",
            "investment_capacity": "What's your investment budget for a potential Spiro EV franchise? Our franchises typically require a minimum investment. Could you share your specific budget in Kenyan Shillings (KES)? 💰",
            "location": "Where in Africa are you looking to establish a Spiro EV franchise? Please note that Spiro franchises are currently only available in African countries. Which African location interests you most? 📍",
            "experience": "Do you have any prior experience running a business or franchise? Even if you don't have direct experience, please share any relevant background you might have. What's your business journey been like so far? 💼"
        }

        if not missing_fields:
            return "Thanks for providing all the information! Are you ready to move to the next step where I can share more details about Spiro's franchise opportunities? 😊"
        else:
            # Make sure we don't ask about experience if it's already collected
            if "experience" in missing_fields and "experience" in collected_fields:
                # Remove experience from missing fields if it's already collected
                missing_fields = [field for field in missing_fields if field != "experience"]

            # If we removed experience and now there are no missing fields
            if not missing_fields:
                return "Thanks for providing all the information! Are you ready to move to the next step where I can share more details about Spiro's franchise opportunities? 😊"

            next_field = missing_fields[0]
            if collected_fields:
                context = f"Thanks for sharing that information. "
            else:
                context = ""
            return context + field_questions.get(next_field, f"Please tell me about your {next_field.replace('_', ' ')}")

    def can_handle(self, state: AgentState) -> bool:
        """This agent handles requirement collection"""
        return state.get("current_agent") == "RequirementsCollectionAgent"

class LeadScoringAgent(Agent):
    """
    Agent that previously scored leads based on collected information.
    Now acts as a pass-through to immediately transition to LeadNurturingAgent.
    Kept for backward compatibility.
    """
    def __init__(self):
        super().__init__("LeadScoringAgent")
        self.tools = lead_scoring_tools

    def process(self, user_input: str, state: AgentState) -> AgentResponse:
        """
        This method now acts as a pass-through to immediately transition to LeadNurturingAgent.
        No actual scoring is performed.
        """
        # user_input parameter not used in this implementation
        # pylint: disable=unused-argument
        # Explicitly acknowledge unused parameter to satisfy IDE warning
        _ = user_input
        # Log that we're bypassing the scoring agent
        logger.info("LeadScoringAgent bypassed - transitioning directly to LeadNurturingAgent")

        # Log the collected information from user_info for debugging
        logger.info(f"User business type: {state.user_info.business_type}")
        logger.info(f"User investment capacity: {state.user_info.investment_capacity or 0}")
        logger.info(f"User experience: {state.user_info.experience}")
        logger.info(f"User experience years: {state.user_info.experience_years}")

        # Set default values
        score = 50  # Default middle score
        lead_quality = "Warm"  # Default middle quality
        explanation = "Lead qualification has been disabled in this system."

        # Create state updates to transition to LeadNurturingAgent
        state_updates = [
            StateUpdate(key="lead_score", value=score, action=StateAction.UPDATE),
            StateUpdate(key="lead_quality", value=lead_quality, action=StateAction.UPDATE),
            StateUpdate(key="lead_explanation", value=explanation, action=StateAction.UPDATE),
            StateUpdate(key="current_agent", value="LeadNurturingAgent", action=StateAction.UPDATE)
        ]

        # No filler message - transition directly to LeadNurturingAgent without any response
        # The LeadNurturingAgent will handle the first message
        response_message = ""

        # Add nurturing_stage initialization to the state updates
        state_updates.append(StateUpdate(key="nurturing_stage", value=0, action=StateAction.UPDATE))

        # Log the transition
        logger.info(f"Bypassing lead scoring and transitioning to LeadNurturingAgent")

        # Return response with immediate transition
        return AgentResponse(message=response_message, state_updates=state_updates, should_progress=True)

    def can_handle(self, state: AgentState) -> bool:
        """This agent handles lead scoring"""
        return state.get("current_agent") == "LeadScoringAgent"

# Define function tools for the final agent
final_agent_tools = [
    {
        "type": "function",
        "function": {
            "name": "provide_final_message",
            "description": "Provides a final message to the user informing them that a human agent will contact them",
            "parameters": {
                "type": "object",
                "properties": {
                    "message": {
                        "type": "string",
                        "description": "The final message to send to the user"
                    }
                },
                "required": ["message"]
            }
        }
    }
]

class LeadNurturingAgent(Agent):
    """Agent that nurtures leads by providing information"""
    def __init__(self):
        super().__init__("LeadNurturingAgent")
        self.tools = lead_nurturing_tools

    def process(self, user_input: str, state: AgentState) -> AgentResponse:
        """Provide information to nurture the lead using LLM"""
        # Get the collected information from user_info
        business_type = state.user_info.business_type
        investment_capacity = state.user_info.investment_capacity or 0
        experience = state.user_info.experience
        experience_years = state.user_info.experience_years or 0

        # Initialize response_message with a default value
        # This will be overridden by the LLM response in most cases

        # Check if this is the first message in the LeadNurturingAgent (transition from requirements collection)
        is_first_nurturing_message = False
        conversation_history = state.get("messages", [])

        # Look for transition indicators in the conversation history
        for i in range(len(conversation_history) - 1, -1, -1):
            msg = conversation_history[i]
            if msg.get("role") == "assistant" and any(phrase in msg.get("content", "") for phrase in [
                "Thanks for confirming all the details",
                "Thank you for providing all the information needed",
                "I'll now share details about Spiro's electric vehicle franchise",
                "Let me start by telling you about our company"
            ]):
                is_first_nurturing_message = True
                break

        # Also check if this is the very first message from this agent
        if not is_first_nurturing_message:
            # Count how many messages have been sent by this agent
            nurturing_messages_count = 0
            for msg in conversation_history:
                if msg.get("role") == "assistant" and state.get("current_agent") == "LeadNurturingAgent":
                    nurturing_messages_count += 1

            # If this is the first or second message from this agent, treat it as the first nurturing message
            if nurturing_messages_count <= 1:
                is_first_nurturing_message = True

        # Let the LLM handle all responses dynamically - no hardcoded messages
        response_message = ""

        # Check if we need to track the nurturing stage
        nurturing_stage = state.get("nurturing_stage", 0)
        logger.info(f"Current nurturing stage: {nurturing_stage}")

        # Let the LLM handle everything dynamically based on the current stage
        logger.info(f"At nurturing stage {nurturing_stage}, letting LLM handle response completely dynamically")

        # Initialize state_updates list
        state_updates = []

        # Use LLM to generate response if available
        if client and user_input:
            try:
                # Let the LLM decide which tool to use - completely dynamic

                # Create a detailed system prompt with the Spiro knowledge base and instructions
                # Include the current nurturing stage to help the LLM understand where we are in the conversation
                nurturing_stage = state.get("nurturing_stage", 0)

                system_prompt = (
                    f"You are a lead nurturing agent for Spiro, an electric vehicle (EV) company in Africa. Your role is to provide "
                    f"information about Spiro's franchise opportunities to potential franchisees. "

                    f"CURRENT NURTURING STAGE: {nurturing_stage}\n"
                    f"0 = First contact, share ONE brief business model message\n"
                    f"1 = Business model shared, share ONE success story message\n"
                    f"2 = Success story shared, provide facility link\n"
                    f"3 = Final stage, transition to human agent\n\n"

                    f"WHATSAPP MESSAGE FORMAT:\n"
                    f"- Keep messages SHORT and conversational (2-3 sentences max)\n"
                    f"- NO markdown formatting (no *, **, #, etc.)\n"
                    f"- Use simple text with emojis for engagement\n"
                    f"- Break long information into multiple short messages if needed\n"
                    f"- Sound natural and friendly, like texting a friend\n\n"

                    f"STAGE-SPECIFIC BEHAVIOR:\n"
                    f"- Stage 0: Share ONE brief business model message (2-3 sentences), then advance to next stage\n"
                    f"- Stage 1: Share ONE success story highlight (2-3 sentences), then advance to next stage\n"
                    f"- Stage 2: ONLY provide the facility upload link with this message: 'Please share your basic details, site location, and photos at https://qa-fms.spironet.com/. Our team will assess your space to determine the franchise feasibility.'\n"
                    f"- Use the advance_nurturing_stage function when you complete a stage's objective\n\n"

                    f"CRITICAL QUESTION RESTRICTIONS:\n"
                    f"- DO NOT ask questions that seek information from the user\n"
                    f"- DO NOT ask 'What interests you?', 'What would you like to know?', etc.\n"
                    f"- DO NOT ask for preferences or opinions\n"
                    f"- Instead, use rhetorical questions or statements to maintain engagement\n"
                    f"- Examples of acceptable rhetorical questions:\n"
                    f"  - 'Sounds good?' (after sharing information)\n"
                    f"  - 'Ready to hear more?' (before sharing next piece)\n"
                    f"  - 'Exciting, isn't it?' (after sharing success stories)\n\n"

                    f"IMPORTANT: You are in FULL CONTROL of this conversation. There are NO hardcoded responses.\n"
                    f"You must dynamically generate ALL responses based on the user's input and the current stage.\n"
                    f"Follow the conversation flow but respond naturally to the user's specific questions.\n\n"

                    f"USER'S LATEST MESSAGE: '{user_input}'\n\n"

                    f"AVOID REPETITIVE MESSAGES:\n"
                    f"- You have access to the complete conversation history\n"
                    f"- NEVER send the same or similar messages repeatedly\n"
                    f"- Always check what information has already been shared\n"
                    f"- If you've already explained something, don't explain it again\n"
                    f"- Build upon previous conversations naturally\n"
                    f"- Adapt your responses based on what the user has already learned\n"
                    f"- If a topic has been covered, acknowledge it and move forward\n"
                    f"- CRITICAL: Check if the facility upload link (https://qa-fms.spironet.com/) has already been shared - if yes, DO NOT share it again\n"
                    f"- Once the link is provided, transition to final agent\n\n"

                    f"CRITICAL INSTRUCTIONS:\n"
                    f"1. NEVER reveal your internal thought process\n"
                    f"2. NEVER repeat the same information twice\n"
                    f"3. NEVER output raw state information\n"
                    f"4. NEVER ask questions seeking user information\n"
                    f"5. ONLY use rhetorical questions for engagement\n"
                    f"6. Share information proactively without asking preferences\n"
                    f"7. Move through stages based on user acknowledgment\n"
                    f"8. You are STRICTLY a Spiro franchise representative chatbot\n"
                    f"9. ONLY discuss topics related to Spiro franchising\n"
                    f"10. DO NOT provide help, advice, or information on any unrelated topics\n"
                    f"11. If users ask about unrelated topics, politely redirect them back to franchise-related discussion\n"
                    f"12. If users provide inappropriate, illegal, or nonsensical information, politely ask them to provide valid information\n"
                    f"13. NEVER repeat the same sentence or link multiple times in one response\n"

                    f"EXAMPLE RESPONSES:\n"
                    f"Stage 0 (Business Model):\n"
                    f"'Great! Spiro uses a micro-franchise model with battery-swapping stations. You can earn a guaranteed income monthly, plus performance-based earnings. Sounds good? 😉'\n\n"
                    
                    f"Stage 1 (Success Story):\n"
                    f"'Our franchisees consistently earn their guaranteed income monthly, and many exceed it through performance-based incentives. Exciting, isn't it? 🚀'\n\n"
                    
                    f"Stage 2 (Facility Link):\n"
                    f"'Please share your basic details, site location, and photos at https://qa-fms.spironet.com/. Our team will assess your space to determine the franchise feasibility.'\n\n"

                    f"CRITICAL: CONTACT INFORMATION RESTRICTIONS:\n"
                    f"- NEVER provide any phone numbers or contact numbers\n"
                    f"- DO NOT make up, hallucinate, or guess any phone numbers\n"
                    f"- If users ask for contact information, politely inform them that a human agent will contact them\n"
                    f"- DO NOT provide any website links or contact information\n\n"

                    f"IMPORTANT BEHAVIOR AT NURTURING STAGE 2:\n"
                    f"- When reaching stage 2, ONLY provide the facility upload link with the specified message\n"
                    f"- DO NOT ask any additional questions about facility type or other details\n"
                    f"- After providing the link, transition to final agent\n"
                    f"- DO NOT ask for permission or confirmation before sharing the link\n"
                    f"- DO NOT engage in further conversation after sharing the link\n\n"

                    f"RESPONDING TO USER INPUT:\n"
                    f"- ALWAYS acknowledge what the user said first\n"
                    f"- When at stage 2, provide the facility upload link immediately with the specified message\n"
                    f"- Be natural and conversational, not robotic\n\n"

                    f"CRITICAL ABOUT LINKS:\n"
                    f"- When providing the facility upload link, ALWAYS use the specified message format\n"
                    f"- NEVER use placeholders like 'this link' or 'click here' - the full URL must be visible\n\n"

                    f"BUSINESS MODEL MESSAGE FORMAT:\n"
                    f"- Keep it to ONE brief message about the micro-franchise model\n"
                    f"- Focus on battery-swapping stations and guaranteed income\n"
                    f"- Only provide more details if the user specifically asks\n\n"

                    f"SUCCESS STORY MESSAGE FORMAT:\n"
                    f"- Keep it to ONE brief message highlighting key achievements\n"
                    f"- Focus on franchisee success and network growth\n"
                    f"- Only provide more details if the user specifically asks\n\n"

                    f"CONVERSATION FLOW:\n"
                    f"1. Share ONE business model message\n"
                    f"2. If user acknowledges, move to ONE success story\n"
                    f"3. If user acknowledges, provide facility upload link\n"
                    f"4. After sharing link, transition to final agent\n\n"

                    f"Key facts about Spiro:\n"
                    f"- Leading EV company in Africa focusing on two-wheelers with battery-swapping technology\n"
                    f"- Uses a micro-franchise model with distributors ($0.3M investment) and micro-franchisees\n"
                    f"- Distributors earn one-time margins plus 6% of service station revenue\n"
                    f"- Micro-franchisees earn $150 monthly guaranteed plus performance-based earnings\n"
                    f"- No prior experience required, training provided\n"
                    f"- Deployed 30,000+ e-bikes and 600 battery-swapping stations across Africa\n"
                    f"- Secured $50M funding and has government partnerships\n"
                    f"- TIME 100 Most Influential Company in 2024\n"
                    f"- Uganda partnership: 140,000 bikes by 2028, Kenya: 1.2M EVs\n"
                    f"- High franchisee retention and success rates\n"
                    f"- Proven business model with multiple income streams\n\n"

                    f"User profile: \n"
                    f"- Business Type Interest: {business_type}\n"
                    f"- Investment Capacity: KES {investment_capacity}\n"
                    f"- Experience: {experience}\n"
                    f"- Experience Years: {experience_years} years\n\n"

                    f"The user's question was: '{user_input}'\n"
                    f"If this is a specific question about our franchise model, answer it in detail, then ask if they have more questions."

                    "\n\nCOMMUNICATION STYLE REQUIREMENTS:\n"
                    "1. Be conversational, friendly, and concise\n"
                    "2. Break information into small, digestible chunks\n"
                    "3. Use a casual, friendly tone with occasional emoji (1-2 maximum)\n"
                    "4. Let the conversation flow naturally based on user responses\n"
                    "5. Everything should be controlled by the LLM - no static messages or conditions\n"
                    "6. ALWAYS end your response with a rhetorical question to encourage user engagement\n"

                    "\n\n=== SPIRO FRANCHISE KNOWLEDGE BASE ===\n"
                    "1. Company Overview\n"
                    "Spiro is a leading electric vehicle (EV) company in Africa, dedicated to transforming urban mobility through sustainable, eco-friendly transportation solutions. With a mission to enhance livelihoods through clean energy, Spiro focuses on electrifying mobility, particularly for two-wheelers like motorcycles and scooters, using innovative battery-swapping technology. The company operates in East & West African countries such as Rwanda, Uganda, and Kenya, with plans for further expansion across the continent.\n\n"

                    "Mission: To lead the large-scale electrification of mobility in Africa, reducing carbon emissions and promoting a cleaner environment while empowering local communities through job creation and entrepreneurship.\n\n"

                    "Achievements:\n"
                    "- Deployed over 30,000 e-bikes across Africa\n"
                    "- Secured a $50 million debt facility from Afreximbank to drive growth\n"
                    "- Recognized as a TIME 100 Most Influential Company in 2024\n\n"

                    "Sustainability Impact:\n"
                    "Aligns with UN Sustainable Development Goals (SDGs), including Affordable and Clean Energy (SDG 7), Decent Work and Economic Growth (SDG 8), Industry, Innovation, and Infrastructure (SDG 9), Sustainable Cities and Communities (SDG 11), and Climate Action (SDG 13).\n\n"

                    "2. Business Model\n"
                    "Spiro operates a micro-franchise energy network model designed to distribute charging infrastructure and battery subscription services for electric vehicles. The model is structured to scale rapidly, leveraging local entrepreneurs through a Franchise Owned Franchise Operated (FOFO) approach.\n\n"

                    "Network Structure:\n"
                    "- Spiro establishes a network in each country with a single distributor model overseeing multiple micro-franchises\n"
                    "- Good number of micro-franchises can operate in a single country, each managing battery-swapping stations\n\n"

                    "Key Roles and Responsibilities:\n"
                    "Distributor:\n"
                    "- Invests $0.3 million initially\n"
                    "- Maintains at least 20 service stations (SS)\n"
                    "- Appoints and manages hundreds of micro-franchises\n"
                    "- Sells charging infrastructure and batteries on a 3-year subscription\n"
                    "- Ensures network maintenance and uptime\n"
                    "- Requirements: $0.3 million initial investment, interest-free security deposit, purchase 100 units of swapping infrastructure\n\n"

                    "Micro-Franchise:\n"
                    "- Purchases charging infrastructure and battery subscriptions for 3 years\n"
                    "- Operates battery-swapping stations\n"
                    "- Option to repurchase subscription after 3 years\n"
                    "- Requirements: Purchase infrastructure and subscriptions, no prior experience required, good infrastructure\n\n"

                    "Franchise Owned Franchise Operated (FOFO) Model:\n"
                    "- The FOFO model empowers local entrepreneurs by allowing them to own and operate their franchises, ensuring efficient management of battery-swapping stations\n"
                    "- This approach fosters community involvement and aligns with Spiro's commitment to economic growth and social well-being\n\n"

                    "Marketing and Lead Generation:\n"
                    "- Spiro employs innovative marketing strategies, such as newspaper advertisements with QR codes, to attract potential micro-franchisees\n"
                    "- QR codes direct users to a page where they can submit details like map locations and contact information, streamlining the recruitment process\n\n"

                    "Operational Highlights:\n"
                    "- Spiro has deployed around 600 battery-swapping stations across four East & West countries\n"
                    "- Partnerships with governments (e.g., Uganda for 140,000 bikes by 2028, Kenya for 1.2 million EVs) enhance scalability\n"
                    "- Collaboration with Chinese manufacturer Horwin for EV bikes, with plans to build local assembly plants and battery factories\n\n"

                    "3. Profit Margin\n"
                    "Spiro's profit structure is designed to provide stable income for both distributors and micro-franchisees, with opportunities for additional earnings based on performance.\n\n"

                    "Distributor Profit Margin:\n"
                    "- One-time Margin: Earned on the sale of charging infrastructure and batteries to micro-franchises (specific percentage not disclosed)\n"
                    "- Recurring Revenue: 6% of service station (SS) revenue monthly, providing a steady income stream\n"
                    "- The combination of one-time and recurring revenue creates a multi-layered profit model, dependent on sales volume and network performance\n\n"

                    "Micro-Franchise Profit Margin:\n"
                    "- Guaranteed Revenue: $150 monthly, ensuring a stable base income\n"
                    "- Additional Revenue: Performance-based earnings tied to the utilization percentage of the site's capacity (specific amounts not detailed)\n"
                    "- This structure offers micro-franchisees predictability with potential for growth as demand for battery-swapping services increases\n\n"

                    "4. Information for Potential Franchisees\n"
                    "Why Invest in Spiro?\n"
                    "- Market Leadership: Spiro is a pioneer in Africa's electric mobility sector, with a proven track record and strong government support\n"
                    "- Sustainability Focus: Aligns with global trends toward clean energy, appealing to environmentally conscious investors\n"
                    "- Scalability: Ambitious expansion plans and funding ensure long-term growth potential\n"
                    "- Community Impact: Empowers local entrepreneurs, creating jobs and fostering economic development\n\n"

                    "Support Provided:\n"
                    "- Training: Comprehensive programs to equip franchisees with the skills needed to operate battery-swapping stations, no prior experience required\n"
                    "- Marketing: Assistance with lead generation through QR code campaigns and other strategies\n"
                    "- Technical Support: Guidance on infrastructure setup and battery management to ensure operational efficiency\n\n"

                    "Requirements:\n"
                    "Distributors:\n"
                    "- Initial investment of $0.3 million\n"
                    "- Commitment to maintain at least 20 service stations\n"
                    "- Ability to manage a network of micro-franchises\n\n"

                    "Micro-Franchisees:\n"
                    "- Purchase of charging infrastructure and battery subscriptions for 3 years\n"
                    "- Willingness to operate a battery-swapping station\n"
                    "- Good Infrastructure\n\n"

                    "Growth Prospects:\n"
                    "- Expansion Plans: Spiro aims to deploy millions of EVs across Africa, supported by government partnerships and a $50 million funding deal\n"
                    "- Local Manufacturing: Plans to establish assembly plants and battery factories, reducing reliance on imports and creating new opportunities\n"
                    "- Market Demand: Growing demand for sustainable transportation in urban Africa positions Spiro for significant growth\n\n"

                    "5. Frequently Asked Questions (FAQs)\n"
                    "Q: What is Spiro's primary business?\n"
                    "A: Spiro provides electric vehicle solutions, selling electric two wheeler & focusing on battery-swapping stations for two-wheelers in Africa.\n\n"

                    "Q: How does the franchise model work?\n"
                    "A: Spiro uses a FOFO model where distributors manage micro-franchises operating battery-swapping stations under a 3-year subscription system.\n\n"

                    "Q: What are the revenue streams for franchisees?\n"
                    "A: Distributors earn one-time margins and 6% of SS revenue; micro-franchisees get $150 monthly plus performance-based earnings.\n\n"

                    "Q: Is prior experience required?\n"
                    "A: No, Spiro provides training, making the opportunity accessible to newcomers.\n\n"

                    "Q: What support does Spiro offer?\n"
                    "A: Training, marketing, and technical support for franchisees.\n\n"

                    "Q: What are Spiro's growth plans?\n"
                    "A: Expansion across Africa, local manufacturing, and large-scale EV deployments.\n\n"

                    "Q: How does Spiro contribute to sustainability?\n"
                    "A: Promotes clean energy, reduces emissions, and supports SDGs through job creation and innovation.\n\n"

                    "6. Success Stories and Case Studies\n"
                    "Spiro's franchise network has achieved remarkable success across Africa, demonstrating the viability and profitability of the micro-franchise model.\n\n"

                    "Regional Expansion Success:\n"
                    "- Successfully deployed over 30,000 e-bikes across multiple African countries\n"
                    "- Established 600+ battery-swapping stations in East & West Africa\n"
                    "- Achieved recognition as a TIME 100 Most Influential Company in 2024\n"
                    "- Secured $50 million debt facility from Afreximbank, demonstrating investor confidence\n\n"

                    "Government Partnership Achievements:\n"
                    "- Uganda Partnership: Committed to deploying 140,000 electric bikes by 2028\n"
                    "- Kenya Partnership: Agreement to deploy 1.2 million electric vehicles\n"
                    "- These partnerships validate Spiro's business model and ensure long-term market stability\n\n"

                    "Franchisee Success Metrics:\n"
                    "- Micro-franchisees consistently earn their guaranteed $150 monthly revenue\n"
                    "- Many franchisees exceed base earnings through performance-based incentives\n"
                    "- High franchisee retention rate due to stable income and comprehensive support\n"
                    "- Successful franchisees often expand to operate multiple stations\n\n"

                    "Technology and Innovation Success:\n"
                    "- Battery-swapping technology proven effective in African urban environments\n"
                    "- Partnership with Chinese manufacturer Horwin for reliable EV bikes\n"
                    "- Plans for local assembly plants and battery factories creating additional opportunities\n"
                    "- Innovative marketing strategies (QR code campaigns) successfully attracting new franchisees\n\n"

                    "Community Impact Stories:\n"
                    "- Created hundreds of entrepreneurship opportunities for local communities\n"
                    "- Contributed to reduced carbon emissions in urban areas\n"
                    "- Supported UN Sustainable Development Goals through clean energy initiatives\n"
                    "- Empowered individuals with no prior business experience to become successful entrepreneurs\n\n"

                    "Financial Performance Highlights:\n"
                    "- Distributors benefit from both one-time margins and recurring 6% monthly revenue\n"
                    "- Proven revenue model with multiple income streams\n"
                    "- Strong financial backing enabling continued expansion and franchisee support\n"
                    "- Consistent growth trajectory across all operational markets\n\n"

                    "=== FACILITY IMAGE UPLOAD LINK ===\n"
                    "When appropriate, provide this link for users to upload images of their facility: https://qa-fms.spironet.com/\n"
                    "DO NOT provide any other links.\n\n"
                    "CRITICAL: NEVER use placeholders like 'this link' or 'click here' - ALWAYS include the COMPLETE URL: https://qa-fms.spironet.com/\n"
                    "This is extremely important - the complete URL must always be visible to the user."

                    "=== CONTACT INFORMATION ===\n"
                    "DO NOT provide contact information in this agent. Contact information should only be provided by the Final Agent.\n\n"
                )

                # Get conversation history from state
                conversation_history = state.get("messages", [])

                # Build messages array with history
                messages = [{"role": "system", "content": system_prompt}]

                # Add more conversation history for this agent
                # Since this is the final agent, include more context (at least 5 messages)
                if conversation_history:
                    # Add up to 10 most recent messages to provide better context, but filter out empty messages
                    for msg in conversation_history[-10:]:
                        content = msg.get("content", "").strip()
                        if content:  # Only add messages with actual content
                            messages.append({"role": msg.get("role", "user"), "content": content})

                # Add the current user input if not already included in history
                if user_input and user_input.strip() and (not conversation_history or conversation_history[-1].get("content") != user_input):
                    messages.append({"role": "user", "content": user_input.strip()})

                # Ensure we have at least one user message to prevent empty text parameter error
                has_user_message = any(msg.get("role") == "user" for msg in messages)
                if not has_user_message:
                    messages.append({"role": "user", "content": "Please continue with the conversation."})

                # Call LLM with function calling - use more tokens for detailed responses
                logger.info(f"Calling LLM for nurturing stage {nurturing_stage} with user input: '{user_input}'")
                # Always use function calling so LLM can call advance_nurturing_stage
                response = client.chat.completions.create(
                    model="gemini-2.0-flash",
                    messages=messages,
                    tools=self.tools,
                    max_tokens=300  # Keep short for WhatsApp format
                )

                logger.info(f"Received LLM response: {str(response)[:200]}...")

                # Process the response
                if hasattr(response, 'choices') and len(response.choices) > 0:
                    message = response.choices[0].message
                    logger.info(f"Processing message: {str(message)[:200]}...")

                    # Always capture the message content first, before processing tool calls
                    if message.content:
                        response_message = message.content
                        logger.info(f"Captured LLM content: {response_message[:100]}...")

                    # Check if there are tool calls
                    if hasattr(message, 'tool_calls') and message.tool_calls:
                        logger.info(f"Found tool calls: {str(message.tool_calls)[:200]}...")
                        for tool_call in message.tool_calls:
                            logger.info(f"Processing tool call: {tool_call.function.name}")
                            if tool_call.function.name == "advance_nurturing_stage":
                                try:
                                    args = json.loads(tool_call.function.arguments)
                                    current_stage = args.get("current_stage", nurturing_stage)
                                    next_stage = args.get("next_stage", nurturing_stage + 1)
                                    reason = args.get("reason", "LLM-controlled advancement")

                                    logger.info(f"LLM requesting stage advancement from {current_stage} to {next_stage}: {reason}")

                                    # Update the nurturing stage
                                    state_updates.append(StateUpdate(key="nurturing_stage", value=next_stage, action=StateAction.UPDATE))

                                    # If LLM called function but didn't provide content, provide stage-appropriate content
                                    if not response_message:
                                        if current_stage == 0 and next_stage == 1:
                                            response_message = "Great! Let me tell you about our business model. Spiro operates a micro-franchise energy network for electric vehicles."
                                        elif current_stage == 1 and next_stage == 2:
                                            response_message = "Perfect! Here are some exciting success stories from our franchise network. Which aspect would you like to know more about? 🚀"
                                        else:
                                            response_message = "Thank you! Let's continue with the next step. What would you like to explore further? 😊"
                                        logger.info(f"Added fallback content for stage {current_stage} -> {next_stage}")

                                    # If advancing to stage 3, transition to FinalAgent
                                    if next_stage == 3:
                                        state_updates.append(StateUpdate(key="current_agent", value="FinalAgent", action=StateAction.UPDATE))
                                        logger.info("Stage 3 reached, transitioning to FinalAgent")

                                        # Ensure we have a message when transitioning to FinalAgent
                                        if not response_message:
                                            response_message = "Perfect! Thank you for all the information. A human agent will contact you soon to discuss the next steps. Are you excited about this opportunity? 😊"

                                        return AgentResponse(message=response_message, state_updates=state_updates, should_progress=True)

                                except Exception as e:
                                    logger.error(f"Error parsing advance_nurturing_stage: {str(e)}")

                            elif tool_call.function.name == "provide_business_model_info":
                                try:
                                    args = json.loads(tool_call.function.arguments)
                                    information = args.get("information", "")
                                    if information:
                                        response_message = information
                                except Exception as e:
                                    logger.error(f"Error parsing business_model_info: {str(e)}")

                            elif tool_call.function.name == "provide_profit_margin_info":
                                try:
                                    args = json.loads(tool_call.function.arguments)
                                    information = args.get("information", "")
                                    if information:
                                        response_message = information
                                except Exception as e:
                                    logger.error(f"Error parsing profit_margin_info: {str(e)}")

                            elif tool_call.function.name == "answer_franchise_question":
                                try:
                                    args = json.loads(tool_call.function.arguments)
                                    answer = args.get("answer", "")
                                    follow_up = args.get("follow_up", "Do you have any other questions about our franchise opportunities?")

                                    # Check if the answer is "I don't have access to that information" for a short response
                                    short_responses = ["no", "nope", "ok", "okay", "sure", "yes", "yeah", "fine"]
                                    if (answer == "I don't have access to that information" or
                                        "don't have access" in answer.lower()) and user_input.lower().strip() in short_responses:

                                        # If we're at stage 2, interpret this as the user having no more questions
                                        if nurturing_stage == 2:
                                            # Create a custom response for this specific case
                                            response_message = (
                                                "Great! Since you don't have any more questions, I'd like to invite you to upload images of your "
                                                "facility using the following link: https://qa-fms.spironet.com/\n\n"
                                                "This will help us better understand your space and provide more tailored recommendations for "
                                                "your Spiro franchise setup."
                                            )

                                            # Update the nurturing stage to 3 and transition to FinalAgent
                                            state_updates.append(StateUpdate(key="nurturing_stage", value=3, action=StateAction.UPDATE))
                                            state_updates.append(StateUpdate(key="current_agent", value="FinalAgent", action=StateAction.UPDATE))

                                            # Set should_progress to true to ensure the transition happens
                                            return AgentResponse(message=response_message, state_updates=state_updates, should_progress=True)
                                    # Normal case - combine the answer with the follow-up question
                                    elif answer:
                                        # Make sure the answer is detailed and ends with the follow-up question
                                        if not answer.endswith("?"):
                                            response_message = f"{answer}\n\n{follow_up}"
                                        else:
                                            response_message = answer

                                        # Update the nurturing stage to indicate we've answered a question
                                        # but don't advance to the final stage yet
                                        if state.get("nurturing_stage", 0) == 2:
                                            # Stay at stage 2 to allow for more questions
                                            pass
                                except Exception as e:
                                    logger.error(f"Error parsing answer_franchise_question: {str(e)}")
                    # Message content was already captured above, no need to process again
                    else:
                        # If there's no content and no function calls, or function calls didn't set response_message
                        if not response_message:
                            logger.warning("LLM response has no content and no valid function calls, using fallback")
                            # Use a stage-appropriate fallback
                            if nurturing_stage == 0:
                                response_message = "Great! Let me tell you about Spiro's business model. We're a leading electric vehicle company in Africa with a micro-franchise model. What interests you most about this approach? 🚗"
                            elif nurturing_stage == 1:
                                response_message = "Perfect! Here are some exciting success stories from our franchise network. Which success story would you like to hear more about? 🚀"
                            else:
                                response_message = "I'm here to help with any questions about Spiro's franchise opportunities! What would you like to know more about?"

                        # If we're at nurturing stage 1 and the user asked about micro-franchise,
                        # don't advance to stage 2 automatically
                        if nurturing_stage == 1 and "micro" in user_input.lower() and "franchise" in user_input.lower():
                            logger.info("User asked about micro-franchise at stage 1, staying at stage 1 to answer the question")
                            # Don't update the nurturing stage, stay at 1 to properly answer the question
                            pass
                        elif nurturing_stage == 2:
                            # If we're at stage 2, stay there to allow for more questions
                            logger.info("At stage 2, staying at stage 2 to allow for more questions")
                            pass
            except Exception as e:
                logger.error(f"Error calling LLM for lead nurturing: {str(e)}")

                # Try again with a simpler prompt if the first LLM call fails
                try:
                    # Create a simpler system prompt for retry that still includes key knowledge
                    # Include the current nurturing stage to help the LLM understand where we are in the conversation
                    nurturing_stage = state.get("nurturing_stage", 0)

                    retry_prompt = (
                        f"You are a lead nurturing agent for Spiro, an electric vehicle (EV) company in Africa. "
                        f"Your role is to provide information about Spiro's franchise opportunities to potential franchisees. "
                        f"Answer ONLY from the knowledge base - do NOT hallucinate information. "
                        f"Keep your response CONVERSATIONAL but DETAILED enough to properly answer the user's question.\n\n"

                        f"CURRENT NURTURING STAGE: {nurturing_stage}\n"
                        f"0 = First contact, share ONE brief business model message\n"
                        f"1 = Business model shared, share ONE success story message\n"
                        f"2 = Success story shared, provide facility link\n"
                        f"3 = Final stage, transition to human agent\n\n"

                        f"WHATSAPP MESSAGE FORMAT:\n"
                        f"- Keep messages SHORT and conversational (2-3 sentences max)\n"
                        f"- NO markdown formatting (no *, **, #, etc.)\n"
                        f"- Use simple text with emojis for engagement\n"
                        f"- Sound natural and friendly, like texting a friend\n\n"

                        f"STAGE-SPECIFIC BEHAVIOR:\n"
                        f"- Stage 0: Share ONE brief business model message\n"
                        f"- Stage 1: Share ONE success story highlight\n"
                        f"- Stage 2: ONLY provide the facility upload link with this message: 'Please share your basic details, site location, and photos at https://qa-fms.spironet.com/. Our team will assess your space to determine the franchise feasibility.'\n"
                        f"- Use the advance_nurturing_stage function when you complete a stage's objective\n\n"

                        f"IMPORTANT: You are in FULL CONTROL of this conversation. There are NO hardcoded responses.\n"
                        f"You must dynamically generate ALL responses based on the user's input and the current stage.\n"
                        f"Follow the conversation flow but respond naturally to the user's specific questions.\n\n"

                        f"USER'S LATEST MESSAGE: '{user_input}'\n\n"

                        f"AVOID REPETITIVE MESSAGES:\n"
                        f"- You have access to the complete conversation history\n"
                        f"- NEVER send the same or similar messages repeatedly\n"
                        f"- Always check what information has already been shared\n"
                        f"- If you've already explained something, don't explain it again\n"
                        f"- Build upon previous conversations naturally\n"
                        f"- Adapt your responses based on what the user has already learned\n"
                        f"- CRITICAL: Check if the facility upload link (https://qa-fms.spironet.com/) has already been shared - if yes, DO NOT share it again\n"
                        f"- Once the link is provided, transition to final agent\n\n"

                        f"CRITICAL INSTRUCTIONS:\n"
                        f"1. NEVER reveal your internal thought process\n"
                        f"2. NEVER repeat the same information twice\n"
                        f"3. NEVER output raw state information\n"
                        f"4. CAREFULLY READ AND DIRECTLY ANSWER THE USER'S SPECIFIC QUESTION\n"
                        f"5. DO NOT ignore the user's question to follow a script\n"
                        f"6. ALWAYS end your response by asking if they have more questions\n"
                        f"7. You are STRICTLY a Spiro franchise representative chatbot\n"
                        f"8. ONLY discuss topics related to Spiro franchising\n"
                        f"9. DO NOT provide help, advice, or information on any unrelated topics\n"
                        f"10. If users ask about unrelated topics, politely redirect them back to franchise-related discussion\n"
                        f"11. If users provide inappropriate, illegal, or nonsensical information, politely ask them to provide valid information\n"
                        f"12. ALWAYS end your response with a rhetorical question to encourage user engagement but dont ask anything specific like his preference or something - NEVER end with a declarative sentence\n"
                        f"13. CRITICAL: HANDLING ILLEGAL/HARMFUL CONTENT:\n"
                        f"    - If user provides illegal information, self-harm content, or unethical business practices, respond STRICTLY with:\n"
                        f"    - 'We do not endorse harmful practices. Kindly enter a valid business experience that aligns with legal and ethical standards.'\n"
                        f"    - Do NOT engage with, acknowledge, or repeat any illegal/harmful content\n"
                        f"    - Immediately redirect to appropriate business-related information\n"
                        f"    - Maintain professional boundaries at all times\n\n"

                        f"CRITICAL: CONTACT INFORMATION RESTRICTIONS:\n"
                        f"- NEVER provide any phone numbers or contact numbers\n"
                        f"- DO NOT make up, hallucinate, or guess any phone numbers\n"
                        f"- If users ask for phone numbers, tell them to visit the website for contact information\n"
                        f"- ONLY provide the website: https://www.spironet.com when contact information is needed\n\n"

                        f"IMPORTANT BEHAVIOR AT NURTURING STAGE 2:\n"
                        f"- When reaching stage 2, ONLY provide the facility upload link with the specified message\n"
                        f"- DO NOT ask any additional questions about facility type or other details\n"
                        f"- After providing the link, transition to final agent\n"
                        f"- DO NOT ask for permission or confirmation before sharing the link\n"
                        f"- DO NOT engage in further conversation after sharing the link\n\n"

                        f"RESPONDING TO USER INPUT:\n"
                        f"- ALWAYS acknowledge what the user said first\n"
                        f"- When at stage 2, provide the facility upload link immediately with the specified message\n"
                        f"- Be natural and conversational, not robotic\n\n"

                        f"CRITICAL ABOUT LINKS:\n"
                        f"- When providing the facility upload link, ALWAYS use the specified message format\n"
                        f"- NEVER use placeholders like 'this link' or 'click here' - the full URL must be visible\n\n"

                        f"BUSINESS MODEL MESSAGE FORMAT:\n"
                        f"- Keep it to ONE brief message about the micro-franchise model\n"
                        f"- Focus on battery-swapping stations and guaranteed income\n"
                        f"- Only provide more details if the user specifically asks\n\n"

                        f"SUCCESS STORY MESSAGE FORMAT:\n"
                        f"- Keep it to ONE brief message highlighting key achievements\n"
                        f"- Focus on franchisee success and network growth\n"
                        f"- Only provide more details if the user specifically asks\n\n"

                        f"CONVERSATION FLOW:\n"
                        f"1. Share ONE business model message\n"
                        f"2. If user acknowledges, move to ONE success story\n"
                        f"3. If user acknowledges, provide facility upload link\n"
                        f"4. After sharing link, transition to final agent\n\n"

                        f"Key facts about Spiro:\n"
                        f"- Leading EV company in Africa focusing on two-wheelers with battery-swapping technology\n"
                        f"- Uses a micro-franchise model with distributors ($0.3M investment) and micro-franchisees\n"
                        f"- Distributors earn one-time margins plus 6% of service station revenue\n"
                        f"- Micro-franchisees earn $150 monthly guaranteed plus performance-based earnings\n"
                        f"- No prior experience required, training provided\n"
                        f"- Deployed 30,000+ e-bikes and 600 battery-swapping stations across Africa\n"
                        f"- Secured $50M funding and has government partnerships\n"
                        f"- TIME 100 Most Influential Company in 2024\n"
                        f"- Uganda partnership: 140,000 bikes by 2028, Kenya: 1.2M EVs\n"
                        f"- High franchisee retention and success rates\n"
                        f"- Proven business model with multiple income streams\n\n"

                        f"User profile: \n"
                        f"- Business Type Interest: {business_type}\n"
                        f"- Investment Capacity: KES {investment_capacity}\n"
                        f"- Experience: {experience}\n"
                        f"- Experience Years: {experience_years} years\n\n"

                        f"The user's question was: '{user_input}'\n"
                        f"If this is a specific question about our franchise model, answer it in detail, then ask if they have more questions."
                    )

                    # Make a simpler LLM call with more tokens for detailed responses
                    retry_response = client.chat.completions.create(
                        model="gemini-2.0-flash",
                        messages=[
                            {"role": "system", "content": retry_prompt},
                            {"role": "user", "content": user_input}
                        ],
                        max_tokens=400  # Increased to allow for more detailed answers
                    )

                    if hasattr(retry_response, 'choices') and len(retry_response.choices) > 0 and hasattr(retry_response.choices[0], 'message') and hasattr(retry_response.choices[0].message, 'content'):
                        response_message = retry_response.choices[0].message.content
                        logger.info(f"Using simplified LLM response after initial failure: {response_message[:100]}...")
                    else:
                        # Get the current nurturing stage
                        nurturing_stage = state.get("nurturing_stage", 0)
                        logger.info(f"Fallback response - current nurturing stage: {nurturing_stage}")

                        # Use a generic fallback response - let the LLM handle everything dynamically
                        response_message = (
                            f"I'm here to help you learn more about Spiro's franchise opportunities. "
                            f"Spiro is a leading electric vehicle company in Africa with a micro-franchise model. "
                            f"What aspect interests you most - our business model, success stories, or the facility upload process?"
                        )

                        # Don't update the nurturing stage in fallback mode
                        # This will allow the LLM to handle it properly on the next message
                except Exception as retry_error:
                    logger.error(f"Error in retry LLM call: {str(retry_error)}")
                    # Use the same logic for the final fallback
                    # Get the current nurturing stage
                    nurturing_stage = state.get("nurturing_stage", 0)
                    logger.info(f"Final fallback response - current nurturing stage: {nurturing_stage}")

                    # Use a generic fallback response - let the LLM handle everything dynamically
                    response_message = (
                        f"I'm here to help you learn more about Spiro's franchise opportunities. "
                        f"Spiro is a leading electric vehicle company in Africa with a micro-franchise model. "
                        f"What would you like to explore - our business model, success stories, or uploading images of your facility?"
                    )

                    # Don't update the nurturing stage in fallback mode
                    # This will allow the LLM to handle it properly on the next message

        # Let the LLM control stage progression completely through function calls
        # No automatic stage updates - the LLM decides when to advance stages
        # Let the LLM control stage progression completely - no keyword checking
        # The LLM will indicate when it's ready to move to the next stage through its response content
        # We'll only track basic progression to help the LLM understand where it is in the conversation

        return AgentResponse(message=response_message, state_updates=state_updates)

    def can_handle(self, state: AgentState) -> bool:
        """This agent handles lead nurturing"""
        return state.get("current_agent") == "LeadNurturingAgent"

class FinalAgent(Agent):
    """Agent that informs the user a human agent will contact them"""
    def __init__(self):
        super().__init__("FinalAgent")
        self.tools = final_agent_tools

    def process(self, user_input: str, state: AgentState) -> AgentResponse:
        """Process user input and provide the final message"""
        # Get the collected information from user_info for context
        business_type = state.user_info.business_type
        investment_capacity = state.user_info.investment_capacity or 0
        location = state.user_info.location
        experience = state.user_info.experience
        experience_years = state.user_info.experience_years or 0

        # Check if final message has already been sent to prevent repetition
        final_message_sent = state.get("final_message_sent", False)
        final_message_count = state.get("final_message_count", 0)

        # Initialize response_message with a default value
        response_message = None

        # Get conversation history to analyze previous responses
        conversation_history = state.get("messages", [])

        # Extract previous assistant messages to avoid repetition
        previous_assistant_messages = []
        for msg in conversation_history:
            if msg.get("role") == "assistant" and msg.get("content"):
                previous_assistant_messages.append(msg.get("content"))

        # Use LLM to generate a personalized final message if available
        if client:
            try:
                # Create an enhanced system prompt with better memory instructions
                system_prompt = (
                    f"You are the final agent for Spiro, an electric vehicle (EV) company in Africa. "
                    f"Your role is to handle the final interaction after the facility upload link has been shared. "
                    f"\n\nCRITICAL RESPONSE FLOW:\n"
                    f"1. After link is shared and user acknowledges (says 'ok', 'sure', etc.):\n"
                    f"   - Respond ONLY with: 'Thank you for your interest. A human agent will contact you soon.'\n\n"
                    f"2. If user mentions they have uploaded images or completed the upload:\n"
                    f"   - Ask for feedback: 'How was your experience with the website? Did you encounter any issues?'\n\n"
                    f"3. For any subsequent messages (unless user asks a specific question):\n"
                    f"   - Respond with just: 'END'\n\n"
                    f"4. If user asks a specific question:\n"
                    f"   - Answer their question briefly\n"
                    f"   - Then return to sending 'END' for subsequent messages\n\n"

                    f"CRITICAL RESPONSE REQUIREMENTS:\n"
                    f"- NEVER return an empty message\n"
                    f"- ALWAYS provide a response in the 'response' field\n"
                    f"- DO NOT ask rhetorical questions\n"
                    f"- DO NOT try to continue the conversation\n"
                    f"- Only ask for feedback if user explicitly mentions uploading images\n"
                    f"- For simple acknowledgments like 'ok', 'sure', 'yes', respond with 'END'\n\n"

                    f"EXAMPLES:\n"
                    f"User: 'ok' (after link)\n"
                    f"Response: 'Thank you for your interest. A human agent will contact you soon.'\n\n"
                    f"User: 'ok' (after thank you message)\n"
                    f"Response: 'END'\n\n"
                    f"User: 'I uploaded the images'\n"
                    f"Response: 'How was your experience with the website? Did you encounter any issues?'\n\n"
                    f"User: 'ok' (after any other message)\n"
                    f"Response: 'END'\n\n"

                    f"IMPORTANT INSTRUCTIONS:\n"
                    f"1. Be brief and professional\n"
                    f"2. DO NOT provide any new information about the franchise\n"
                    f"3. DO NOT ask any questions except for website feedback when user mentions upload\n"
                    f"4. DO NOT try to continue the conversation\n"
                    f"5. If unsure, respond with 'END'\n"
                    f"6. NEVER leave the response empty\n\n"

                    f"CRITICAL: HANDLING ILLEGAL/HARMFUL CONTENT:\n"
                    f"- If user provides illegal/harmful content, respond with 'END'\n"
                    f"- Do NOT engage with inappropriate content\n\n"

                    f"PREVIOUS ASSISTANT MESSAGES TO AVOID REPEATING:\n"
                    f"{chr(10).join([f'- {msg[:100]}...' for msg in previous_assistant_messages[-5:]]) if previous_assistant_messages else 'None'}\n"
                )

                # Build messages array with enhanced conversation history
                messages = [{"role": "system", "content": system_prompt}]

                # Add the last 10 messages for context (keeping as requested)
                if conversation_history:
                    # Filter out empty messages and add context
                    for msg in conversation_history[-10:]:
                        content = msg.get("content", "").strip()
                        if content:  # Only add messages with actual content
                            messages.append({"role": msg.get("role", "user"), "content": content})

                # Add the current user input if not already included in history
                if user_input and user_input.strip() and (not conversation_history or conversation_history[-1].get("content") != user_input):
                    messages.append({"role": "user", "content": user_input.strip()})

                # Ensure we have at least one user message to prevent empty text parameter error
                has_user_message = any(msg.get("role") == "user" for msg in messages)
                if not has_user_message:
                    messages.append({"role": "user", "content": "Please provide the final message."})

                # Call LLM for the final message with enhanced context
                response = client.chat.completions.create(
                    model="gemini-2.0-flash",
                    messages=messages,
                    max_tokens=300,  # Keep it concise
                    temperature=0.7  # Add some creativity while maintaining consistency
                )

                # Process the response
                if hasattr(response, 'choices') and len(response.choices) > 0 and hasattr(response.choices[0], 'message') and hasattr(response.choices[0].message, 'content'):
                    final_message = response.choices[0].message.content

                    # Remove any website links that might have been included
                    if "spironet.com" in final_message:
                        # Remove any lines containing the website
                        lines = final_message.split('\n')
                        filtered_lines = [line for line in lines if "spironet.com" not in line.lower() and "website:" not in line.lower()]
                        final_message = '\n'.join(filtered_lines).strip()

                    # Update state to track final message usage
                    state_updates = [
                        StateUpdate(key="final_message_sent", value=True, action=StateAction.UPDATE),
                        StateUpdate(key="final_message_count", value=final_message_count + 1, action=StateAction.UPDATE)
                    ]

                    logger.info(f"Generated final message (count: {final_message_count + 1}): {final_message[:100]}...")
                else:
                    # Fallback message if LLM response is not valid
                    final_message = self._get_fallback_message(final_message_count, business_type)
                    state_updates = [
                        StateUpdate(key="final_message_sent", value=True, action=StateAction.UPDATE),
                        StateUpdate(key="final_message_count", value=final_message_count + 1, action=StateAction.UPDATE)
                    ]
                    logger.info("Using fallback final message (LLM response issue)")
            except Exception as e:
                logger.error(f"Error calling LLM for final message: {str(e)}")
                # Fallback message on error
                final_message = self._get_fallback_message(final_message_count, business_type)
                state_updates = [
                    StateUpdate(key="final_message_sent", value=True, action=StateAction.UPDATE),
                    StateUpdate(key="final_message_count", value=final_message_count + 1, action=StateAction.UPDATE)
                ]
                logger.info("Using fallback final message (exception)")
        else:
            # Fallback message if LLM is not available
            final_message = self._get_fallback_message(final_message_count, business_type)
            state_updates = [
                StateUpdate(key="final_message_sent", value=True, action=StateAction.UPDATE),
                StateUpdate(key="final_message_count", value=final_message_count + 1, action=StateAction.UPDATE)
            ]
            logger.info("Using fallback final message (no LLM client)")

        # Final validation to ensure we never return an empty message
        if not final_message or not final_message.strip():
            logger.warning("Empty message after all processing, using emergency fallback")
            final_message = "Thank you for your interest in Spiro. A human agent will contact you soon to discuss next steps."

        # Update state
        state_updates = [
            StateUpdate(key="final_message_sent", value=True, action=StateAction.UPDATE),
            StateUpdate(key="final_message_count", value=final_message_count + 1, action=StateAction.UPDATE)
        ]

        return AgentResponse(message=final_message, state_updates=state_updates)

    def _get_fallback_message(self, message_count: int, business_type: str) -> str:
        """Generate a fallback message that varies based on count to avoid repetition"""
        if message_count == 0:
            return (
                f"Thank you for your interest in Spiro's franchise opportunities! "
                f"A human agent will contact you soon to discuss the next steps. "
                f"Looking forward to having you as part of our {business_type or 'business'} network! 🚗"
            )
        elif message_count == 1:
            return "Thank you for uploading your images. How was your experience with the website? Did you encounter any issues? 📝"
        else:
            return "Thank you for your feedback. A human agent will be in touch with you soon. 🤝"

    def can_handle(self, state: AgentState) -> bool:
        """This agent handles the final message"""
        return state.get("current_agent") == "FinalAgent"

class AgentFactory:
    """Factory for creating agents"""
    @staticmethod
    def create_agent(agent_type: str) -> Agent:
        """Create an agent of the specified type"""
        if agent_type == "InitialMessageAgent":
            return InitialMessageAgent()
        elif agent_type == "RequirementsCollectionAgent":
            return RequirementsCollectionAgent()
        elif agent_type == "LeadScoringAgent":
            return LeadScoringAgent()
        elif agent_type == "LeadNurturingAgent":
            return LeadNurturingAgent()
        elif agent_type == "FinalAgent":
            return FinalAgent()
        else:
            raise ValueError(f"Unknown agent type: {agent_type}")

class AgentManager:
    """Manager for handling agent interactions"""
    def __init__(self):
        self.agents = {
            "InitialMessageAgent": InitialMessageAgent(),
            "RequirementsCollectionAgent": RequirementsCollectionAgent(),
            "LeadScoringAgent": LeadScoringAgent(),
            "LeadNurturingAgent": LeadNurturingAgent(),
            "FinalAgent": FinalAgent()
        }

        # Log initialization of agents
        logger.info("AgentManager initialized with agents: " + ", ".join(self.agents.keys()))

    def process_message(self, user_input: str, state: AgentState) -> Tuple[str, List[StateUpdate], bool]:
        """Process a message using the appropriate agent"""
        # Determine which agent should handle this message
        current_agent_name = state.get("current_agent", "InitialMessageAgent")
        logger.info(f"Processing message with agent: {current_agent_name}")

        # Debug log for state at the beginning of processing
        logger.info(f"Current user_info state before processing: {state.user_info.to_dict()}")

        # Get the agent
        agent = self.agents.get(current_agent_name)
        if not agent:
            # Default to initial message agent if agent not found
            logger.warning(f"Agent {current_agent_name} not found, defaulting to InitialMessageAgent")
            agent = self.agents["InitialMessageAgent"]

        # Update conversation history in state
        if user_input:
            # Get existing messages or initialize empty list
            messages = state.get("messages", [])

            # Add user message to history
            messages.append({"role": "user", "content": user_input})

            # Update state with new messages
            state.set("messages", messages)
            logger.info(f"Added user message to conversation history: {user_input[:50]}...")

        # Process the message
        try:
            response = agent.process(user_input, state)

            # Log the response and state updates
            logger.info(f"Agent {current_agent_name} response: {response.message[:100]}...")
            logger.info(f"Agent {current_agent_name} state updates: {[update.model_dump() for update in response.state_updates]}")

            # Debug log user_info state after processing
            logger.info(f"User info after agent processing: {state.user_info.to_dict()}")

            # Update state with agent's state updates
            state.update(response.state_updates)

            # Debug log user_info state after applying updates
            logger.info(f"User info after applying updates: {state.user_info.to_dict()}")

            # Check if the response is accidentally the same as the user's input or very similar
            if user_input:
                # Get the current nurturing stage
                nurturing_stage = state.get("nurturing_stage", 0)
                logger.info(f"Echo detection - current nurturing stage: {nurturing_stage}")

                # Check for exact match or echo
                is_exact_match = response.message.strip() == user_input.strip()
                is_echo = user_input.strip().lower() in response.message.strip().lower() and len(response.message) < len(user_input) + 20

                if is_exact_match or is_echo:
                    logger.warning(f"Response matches or echoes user input, replacing with appropriate response")

                    # Use a generic response for echo detection that works for all stages
                    response.message = (
                        "I'm here to help you learn more about Spiro's franchise opportunities. "
                        "Spiro is a leading electric vehicle company in Africa with a micro-franchise model. "
                        "What would you like to know more about - our business model, success stories, or uploading images of your facility?"
                    )

                    # Don't update the nurturing stage in echo detection
                    # This will allow the LLM to handle it properly on the next message

            # Add assistant response to conversation history (only if not empty)
            if response.message and response.message.strip():
                messages = state.get("messages", [])
                messages.append({"role": "assistant", "content": response.message})
                state.set("messages", messages)
            else:
                logger.warning("Skipping empty assistant message from being added to conversation history")

            # Check if agent has changed
            new_agent_name = state.get("current_agent")
            if new_agent_name != current_agent_name:
                logger.info(f"Agent transition: {current_agent_name} -> {new_agent_name}")

                # If agent changed and response is empty, automatically process the next agent
                if not response.message or not response.message.strip():
                    logger.info(f"Empty response from {current_agent_name}, automatically processing {new_agent_name}")
                    # Recursively call process_message with the new agent
                    return self.process_message(user_input, state)

            # Check if should_progress is true but didn't transition
            if response.should_progress and new_agent_name == current_agent_name:
                # Force transition to the next agent (skipping LeadScoringAgent)
                if current_agent_name == "InitialMessageAgent":
                    next_agent = "RequirementsCollectionAgent"
                elif current_agent_name == "RequirementsCollectionAgent":
                    next_agent = "LeadNurturingAgent"  # Skip LeadScoringAgent
                elif current_agent_name == "LeadScoringAgent":
                    next_agent = "LeadNurturingAgent"
                elif current_agent_name == "LeadNurturingAgent":
                    # Check if we should transition to FinalAgent
                    if "upload" in response.message.lower() and "facility" in response.message.lower():
                        next_agent = "FinalAgent"
                        logger.info("Detected facility upload link in message, transitioning to FinalAgent")
                    else:
                        next_agent = current_agent_name
                else:
                    next_agent = current_agent_name

                if next_agent != current_agent_name:
                    logger.info(f"Forcing agent transition due to should_progress: {current_agent_name} -> {next_agent}")
                    state.set("current_agent", next_agent)
                    new_update = StateUpdate(key="current_agent", value=next_agent, action=StateAction.UPDATE)
                    response.state_updates.append(new_update)

                    # If response is empty after transition, automatically process the next agent
                    if not response.message or not response.message.strip():
                        logger.info(f"Empty response after transition to {next_agent}, automatically processing")
                        return self.process_message(user_input, state)

            return response.message, response.state_updates, response.should_progress
        except Exception as e:
            logger.error(f"Error processing message with agent {current_agent_name}: {str(e)}", exc_info=True)
            error_message = "I'm sorry, I encountered an error processing your request. Please try again."

            # Add error message to conversation history
            messages = state.get("messages", [])
            messages.append({"role": "assistant", "content": error_message})
            state.set("messages", messages)

            return error_message, [], False

    def parse_standard_response(self, json_response: str, state: AgentState) -> Tuple[str, List[StateUpdate], bool]:
        """Parse standard JSON response format and update state"""
        try:
            # Parse the JSON response
            agent_response = AgentResponse.from_json_response(json_response)

            # Update state with state updates
            state.update(agent_response.state_updates)

            # Check if agent should progress
            should_progress = agent_response.should_progress

            # Update agent if should_progress is true
            if should_progress:
                current_agent_name = state.get("current_agent", "InitialMessageAgent")
                if current_agent_name == "InitialMessageAgent":
                    new_agent_name = "RequirementsCollectionAgent"
                elif current_agent_name == "RequirementsCollectionAgent":
                    new_agent_name = "LeadNurturingAgent"  # Skip LeadScoringAgent
                elif current_agent_name == "LeadScoringAgent":
                    new_agent_name = "LeadNurturingAgent"
                elif current_agent_name == "LeadNurturingAgent":
                    # Check if we should transition to FinalAgent
                    if "upload" in agent_response.message.lower() and "facility" in agent_response.message.lower():
                        new_agent_name = "FinalAgent"
                        logger.info("Detected facility upload link in message, transitioning to FinalAgent")
                    else:
                        new_agent_name = current_agent_name
                else:
                    new_agent_name = current_agent_name

                if new_agent_name != current_agent_name:
                    logger.info(f"Agent transition from JSON response: {current_agent_name} -> {new_agent_name}")
                    state.set("current_agent", new_agent_name)

            return agent_response.message, agent_response.state_updates, should_progress

        except Exception as e:
            logger.error(f"Error parsing standard response format: {str(e)}")
            return "Error processing response", [], False
