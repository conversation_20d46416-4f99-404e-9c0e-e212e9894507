from sqlalchemy import Column, String, Integer, DateTime, Text, Boolean
from sqlalchemy.ext.declarative import declarative_base
import datetime
import json

Base = declarative_base()

class LeadSession(Base):
    """Model for storing lead agent sessions by phone number"""
    __tablename__ = "lead_sessions"

    id = Column(Integer, primary_key=True, autoincrement=True)
    phone_number = Column(String(20), unique=True, nullable=False, index=True)
    state_data = Column(Text, nullable=True)  # For storing state as JSON
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    last_active = Column(DateTime, default=datetime.datetime.utcnow)
    is_active = Column(Boolean, default=True)
    
    def to_dict(self):
        """Convert session to dictionary"""
        state_data = {}
        try:
            if self.state_data:
                state_data = json.loads(self.state_data)
        except (json.JSONDecodeError, TypeError):
            pass  # Use empty dict if conversion fails
            
        return {
            "id": self.id,
            "phone_number": self.phone_number,
            "state_data": state_data,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "last_active": self.last_active.isoformat() if self.last_active else None,
            "is_active": self.is_active
        }
    
    def __repr__(self):
        return f"<LeadSession(id={self.id}, phone_number={self.phone_number})>"
