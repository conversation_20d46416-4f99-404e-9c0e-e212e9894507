package flows

type ProductAttributeFlow struct {
	Product1Attribute string                      `json:"product_1_attribute"`
	Product1Visible   bool                        `json:"product_1_visible"`
	Product1Required  bool                        `json:"product_1_required"`
	Product1Values    []ProductAttributeValueFlow `json:"product_1_values"`

	Product2Attribute string                      `json:"product_2_attribute"`
	Product2Visible   bool                        `json:"product_2_visible"`
	Product2Required  bool                        `json:"product_2_required"`
	Product2Values    []ProductAttributeValueFlow `json:"product_2_values"`

	Product3Attribute string                      `json:"product_3_attribute"`
	Product3Visible   bool                        `json:"product_3_visible"`
	Product3Required  bool                        `json:"product_3_required"`
	Product3Values    []ProductAttributeValueFlow `json:"product_3_values"`

	Product4Attribute string                      `json:"product_4_attribute"`
	Product4Visible   bool                        `json:"product_4_visible"`
	Product4Required  bool                        `json:"product_4_required"`
	Product4Values    []ProductAttributeValueFlow `json:"product_4_values"`

	Product5Attribute string                      `json:"product_5_attribute"`
	Product5Visible   bool                        `json:"product_5_visible"`
	Product5Required  bool                        `json:"product_5_required"`
	Product5Values    []ProductAttributeValueFlow `json:"product_5_values"`
}

type ProductAttributeValueFlow struct {
	ID    string `json:"id"`
	Title string `json:"title"`
}

type FlowButtonGroup struct {
	ID          string `json:"id"`
	Title       string `json:"title"`
	Description string `json:"description"`
    Image     string  `json:"image"`
}

type DataChannelRequest struct {
	EncryptedAesKey   string      `json:"encrypted_aes_key"`
	EncryptedFlowData string      `json:"encrypted_flow_data"`
	InitialVector     string      `json:"initial_vector"`
	Response          interface{} `json:"response"`
	AESKey            []byte      `json:"aes_key"`
	IV                []byte      `json:"iv"`
}

type DataChannelDecrypted struct {
	Action    string      `json:"action"`
	Screen    string      `json:"screen"`
	Data      interface{} `json:"data"`
	Version   string      `json:"version"`
	FlowToken string      `json:"flow_token"`
}

type DataChannelResponse struct {
	Version string      `json:"version,omitempty"`
	Screen  string      `json:"screen,omitempty"`
	Data    interface{} `json:"data,omitempty"`
}

type ProductAttributeData struct {
	ProductName string
	ImageUrl    string
	IsCompleted bool
	Attributes  []AttributeData
}

type AttributeData struct {
	AttributeName   string
	AttributeValues []string
	SelectedValue   string
}
