package products

import (
	"time"
)

type SampleCreatePayloadDto struct {
	LookupType  string `json:"lookup_type"`
	// LookupCode  string `json:"lookup_code"`
	DisplayName string `json:"display_name"`
	// IsActive    *bool  `json:"is_active"`
}

type SampleCreateorUpdateRespDto struct {
	Id uint `json:"id"`
}

type SampleUpdatePayloadDto struct {
	LookupType  string `json:"lookup_type,omitempty"`
	// LookupCode  string `json:"lookup_code,omitempty"`
	DisplayName string `json:"display_name,omitempty"`
	// IsActive    *bool  `json:"is_active,omitempty"`
}
type SampleGetRespDto struct {
	Id          uint      `json:"id"`
	LookupType  string    `json:"lookup_type"`
	// LookupCode  string    `json:"lookup_code"`
	DisplayName string    `json:"display_name"`
	IsActive    *bool     `json:"is_active"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type SampleListRespDto struct {
	Id          uint   `json:"id"`
	LookupType  string `json:"lookup_type"`
	// LookupCode  string `json:"lookup_code"`
	DisplayName string `json:"display_name"`
}

type LookupCodeDTO struct {
	Id         uint   `json:"id"`
	LookupCode string `json:"lookup_code"`
	Name       string `json:"display_name"`
	LookupType string `json:"lookup_type"`
}



// -------------REQUESTS----------------
type ListRequest struct {
	PhoneNumber string `json:"phone_number"`
	PageNo      uint   `json:"page_no"`
	PerPage     uint   `json:"per_page"`
	ProductIds  string `json:"product_ids"`
	Status      string `json:"status"`
	L1Category  string `json:"l1_category"`
}

type ViewRequest struct {
	PhoneNumber string `json:"phone_number"`
	ProductId   string `json:"product_id"`
}

type UpdatePriceRequest struct {
	PhoneNumber string  `json:"phone_number"`
	ProductId   string  `json:"product_id"`
	Price       float64 `json:"price"`
}

type UpdateInventoryRequest struct {
	PhoneNumber string `json:"phone_number"`
	ProductId   string `json:"product_id"`
	Inventory   uint   `json:"inventory"`
}

// -------------RESPONSES----------------
type ListResponse struct {
	TotalPages uint      `json:"total_pages"`
	Products   []Product `json:"products"`
}

type Product struct {
	ProductId string `json:"product_id"`
	Title    string  `json:"title"`
	Text      string `json:"text"`
	Image     string `json:"image"`
}

type ViewResponse struct {
	ProductId      string   `json:"product_id"`
	ProductName    string   `json:"product_name"`
	ProductDetails string   `json:"product_details"`
	ProductImages  string `json:"product_images"`
}

type ViewProductApiResponse struct {
	Meta struct {
		Status bool `json:"status"`
	} `json:"meta"`
	Data struct {
		ID                 int    `json:"id"`
		ProductTemplateID  int    `json:"product_template_id"`
		ProductTemplateSKU string `json:"product_template_sku_id"`
		Name               string `json:"name"`
		GroupID            string `json:"group_id"`
		SKUID              string `json:"sku_id"`
		Level1CategoryID   int    `json:"level1_category_id"`
		Level2CategoryID   int    `json:"level2_category_id"`
		Level1Category     struct {
			Name string `json:"name"`
		} `json:"level1_category"`
		Level2Category struct {
			Name string `json:"name"`
		} `json:"level2_category"`
		ShortDescription string   `json:"short_desc"`
		LongDescription  string   `json:"long_desc"`
		ImageArr         []string `json:"image_arr"`
		MRP              int      `json:"mrp"`
		SalesPrice       int      `json:"sales_price"`
		PaymentTypeID    int      `json:"payment_type_id"`
		StatusID         int      `json:"status_id"`
		Status           struct {
			DisplayName string `json:"display_name"`
		} `json:"status"`
		HSNReferenceNumber string        `json:"hsn_reference_number"`
		DraftReason        []interface{} `json:"draft_reason"`
		IsActive           bool          `json:"is_active"`
	} `json:"data"`
}

type ViewProductLocationApiResponse struct {
	Meta struct {
		Status bool `json:"status"`
	} `json:"meta"`
	Data []struct {
		ID             int `json:"id"`
		AlertQuantity  int `json:"alert_quantity"`
		OnHandQuantity int `json:"on_hand_quantity"`
		Location       struct {
			ID   int    `json:"id"`
			Name string `json:"name"`
		} `json:"location"`
	} `json:"data"`
}

type ListProductApiResponse struct {
	Meta struct {
		Status     bool `json:"status"`
		Pagination struct {
			TotalPages uint `json:"total_pages"`
		} `json:"pagination"`
	} `json:"meta"`
	Data []struct {
		ID          string `json:"id"`
        Title       string `json:"title"`
        Description string `json:"description"`
		Image       string `json:"image"`
			} `json:"data"`
}

type UpdateStatusRequest struct {
	PhoneNumber string  `json:"phone_number"`
	ProductId   string  `json:"product_id"`
	StatusId       string `json:"status_id"`
}

type Products struct {
    Meta struct {
        Status     bool `json:"status"`
        Pagination struct {
            TotalPages uint `json:"total_pages"`
        } `json:"pagination"`
    } `json:"meta"`
    Data []struct {
        ID          string `json:"id"`
        Title       string `json:"title"`
        Description string `json:"description"`
		Image       string `json:"image"`
		ViewDescription string `json:"view_description"`
    } `json:"data"`
}
