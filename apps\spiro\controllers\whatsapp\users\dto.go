package users

import (
	"time"
)

type SampleCreatePayloadDto struct {
	LookupType  string `json:"lookup_type"`
	// LookupCode  string `json:"lookup_code"`
	DisplayName string `json:"display_name"`
	// IsActive    *bool  `json:"is_active"`
}

type SampleCreateorUpdateRespDto struct {
	Id uint `json:"id"`
}

type SampleUpdatePayloadDto struct {
	LookupType  string `json:"lookup_type,omitempty"`
	// LookupCode  string `json:"lookup_code,omitempty"`
	DisplayName string `json:"display_name,omitempty"`
	// IsActive    *bool  `json:"is_active,omitempty"`
}
type SampleGetRespDto struct {
	Id          uint      `json:"id"`
	LookupType  string    `json:"lookup_type"`
	// LookupCode  string    `json:"lookup_code"`
	DisplayName string    `json:"display_name"`
	IsActive    *bool     `json:"is_active"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type SampleListRespDto struct {
	Id          uint   `json:"id"`
	LookupType  string `json:"lookup_type"`
	// LookupCode  string `json:"lookup_code"`
	DisplayName string `json:"display_name"`
}

type LookupCodeDTO struct {
	Id         uint   `json:"id"`
	LookupCode string `json:"lookup_code"`
	Name       string `json:"display_name"`
	LookupType string `json:"lookup_type"`
}
type MessageFromWhatsapp struct {
	From        string                          `json:"from,omitempty"`
	Id          string                          `json:"id,omitempty"`
	Timestamp   string                          `json:"timestamp,omitempty"`
	Type        string                          `json:"type,omitempty"`
	Text        *MessageFromWhatsappText        `json:"text,omitempty"`
	Interactive *MessageFromWhatsappInteractive `json:"interactive,omitempty"`
	Location    *MessageFromWhatsappLocation    `json:"location,omitempty"`
	Image       *MessageFromWhatsappMedia       `json:"image,omitempty"`
	Audio       *MessageFromWhatsappMedia       `json:"audio,omitempty"`
}
type MessageFromWhatsappText struct {
	Body string `json:"body,omitempty"`
}
type MessageFromWhatsappInteractive struct {
	Type        string                          `json:"type,omitempty"`
	ListReply   *MessageFromWhatsappListReply   `json:"list_reply,omitempty"`
	ButtonReply *MessageFromWhatsappButtonReply `json:"button_reply,omitempty"`
	NfmReply    *MessageFromWhatsappNfmReply    `json:"nfm_reply,omitempty"`
}
type MessageFromWhatsappLocation struct {
	Latitude  interface{} `json:"latitude"`
	Longitude interface{} `json:"longitude"`
	Name      string      `json:"name"`
	Address   string      `json:"address"`
}
type MessageFromWhatsappButtonReply struct {
	Id    string `json:"id,omitempty"`
	Title string `json:"title,omitempty"`
}

type MessageFromWhatsappListReply struct {
	Id          string `json:"id,omitempty"`
	Title       string `json:"title,omitempty"`
	Description string `json:"description,omitempty"`
}

type MessageFromWhatsappNfmReply struct {
	ResponseJson string `json:"response_json,omitempty"`
	Body         string `json:"body,omitempty"`
	Name         string `json:"name,omitempty"`
}

type MessageFromWhatsappMedia struct {
	Caption  string `json:"caption,omitempty"`
	MimeType string `json:"mime_type,omitempty"`
	SHA256   string `json:"sha256,omitempty"`
	ID       string `json:"id,omitempty"`
	Url      string `json:"url"`
	Base64Data string `json:"base64_data"`
}