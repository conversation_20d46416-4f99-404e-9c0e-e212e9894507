import os
from dotenv import load_dotenv
from app import create_app
from app.models.persistent_session import Base
from app.services.session_service import engine, SessionManager

# Load environment variables from .env file
load_dotenv()

# Initialize the application
app = create_app()

# Ensure database tables are created
Base.metadata.create_all(engine)

# Cleanup expired sessions on startup
expired_count = SessionManager.cleanup_expired_sessions(days=30)
print(f"Cleaned up {expired_count} expired sessions on startup")

if __name__ == '__main__':
    # Get configuration from environment variables or use defaults
    host = os.getenv('FLASK_HOST', '0.0.0.0')
    port = int(os.getenv('FLASK_PORT', 5000))
    debug = os.getenv('FLASK_DEBUG', '1') == '1'
    
    app.run(host=host, port=port, debug=debug) 