package sample

import (
	"time"
)

type SampleCreatePayloadDto struct {
	LookupType  string `json:"lookup_type"`
	LookupCode  string `json:"lookup_code"`
	DisplayName string `json:"display_name"`
	IsActive    *bool  `json:"is_active"`
}

type SampleCreateorUpdateRespDto struct {
	Id uint `json:"id"`
}

type SampleUpdatePayloadDto struct {
	LookupType  string `json:"lookup_type,omitempty"`
	LookupCode  string `json:"lookup_code,omitempty"`
	DisplayName string `json:"display_name,omitempty"`
	IsActive    *bool  `json:"is_active,omitempty"`
}
type SampleGetRespDto struct {
	Id          uint      `json:"id"`
	LookupType  string    `json:"lookup_type"`
	LookupCode  string    `json:"lookup_code"`
	DisplayName string    `json:"display_name"`
	IsActive    *bool     `json:"is_active"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type SampleListRespDto struct {
	Id          uint   `json:"id"`
	LookupType  string `json:"lookup_type"`
	LookupCode  string `json:"lookup_code"`
	DisplayName string `json:"display_name"`
}
