package whatsapp

type LoginResponse struct {
	Success bool `json:"success"`
	Data    struct {
		NewUser bool   `json:"new_user"`
		UserId  string `json:"user_id"`
	} `json:"data"`
}

type UserDetailsResponse struct {
	Success bool        `json:"success"`
	Data    UserDetails `json:"data"`
}

type StoreDetailsResponse struct {
	Success bool         `json:"success"`
	Data    StoreDetails `json:"data"`
}

type BankDetailsResponse struct {
	Success bool        `json:"success"`
	Data    BankDetails `json:"data"`
}

type BusinessDetailsResponse struct {
	Success bool            `json:"success"`
	Data    BusinessDetails `json:"data"`
}

type UserDetails struct {
	Name    string `json:"name"`
	Email   string `json:"email"`
	City    string `json:"city"`
	State   string `json:"state"`
	PinCode string `json:"pin_code"`
	Address string `json:"address"`
}

type StoreDetails struct {
	Name               string `json:"name"`
	ShortDesc          string `json:"short_desc"`
	LongDesc           string `json:"long_desc"`
	Website            string `json:"website"`
	FssaiNumber        string `json:"fssai_number"`
	StoreSymbols       string `json:"store_symbols"`
	StoreBanner        string `json:"store_banner"`
	StoreContactNumber string `json:"store_contact_number"`
	StoreEmail         string `json:"store_email"`
	City               string `json:"city"`
	State              string `json:"state"`
	PinCode            string `json:"pin_code"`
	Address            string `json:"address"`
	OrderMinimumValue  string `json:"order_minimum_value"`
	FulfillmentType    string `json:"fulfillment_type"`
}

type BusinessDetails struct {
	BusinessName      string `json:"business_name"`
	AccountHolderName string `json:"account_holder_name"`
	Gstin             string `json:"gstin"`
	PanCard           string `json:"pan_card"`
}

type BankDetails struct {
	AccountHolderName string `json:"account_holder_name"`
	AccountNumber     string `json:"account_number"`
	IfscCode          string `json:"ifsc_code"`
	BankName          string `json:"bank_name"`
}



type GetLocationAndPaymentTypeApiResponse struct {
	Success bool `json:"success"`
	Data    struct {
		Locations []struct {
			Id   string `json:"id"`
			Name string `json:"name"`
		} `json:"locations"`
		PaymentTypes []struct {
			Id   string `json:"id"`
			Name string `json:"name"`
		} `json:"payment_types"`
	} `json:"data"`
}

type ProductDetail struct {
	SKUId            string   `json:"sku_id"`
	ShortDescription string   `json:"short_desc"`
	LongDescription  string   `json:"long_desc"`
	Level1Category   string   `json:"level1_category"`
	Level2Category   string   `json:"level2_category"`
	HSNReferenceCode string   `json:"hsn_reference_code"`
	Name             string   `json:"name"`
	ImageArr         []string `json:"image_arr"`
	Mrp              string   `json:"mrp"`
	LocationId       string   `json:"location_id"`
	PaymentTypeId    string   `json:"payment_type_id"`

	UserSalesPrice string `json:"user_sales_price"`
	UserQuantity   string `json:"user_quantity"`
}

type UpsertProductRequest struct {
	PhoneNumber        string   `json:"phone_number"`
	Name               string   `json:"name"`
	SkuId              string   `json:"sku_id"`
	Level1Category     string   `json:"level1_category"`
	Level2Category     string   `json:"level2_category"`
	ShortDesc          string   `json:"short_desc"`
	LongDesc           string   `json:"long_desc"`
	HsnReferenceNumber string   `json:"hsn_reference_number"`
	ImageArr           []string `json:"image_arr"`

	MRP            float64 `json:"mrp"`
	SalesPrice     float64 `json:"sales_price"`
	PaymentTypeId  int     `json:"payment_type_id"`
	LocationId     int     `json:"location_id"`
	OnHandQuantity int     `json:"on_hand_quantity"`
	AlertQuantity  int     `json:"alert_quantity"`
}

type MiddlewareResponse struct {
	Success bool   `json:"success"`
	Data    string `json:"data"`
}
