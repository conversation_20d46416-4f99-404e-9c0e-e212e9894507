import os
import logging
from flask import Flask
from flask_session import Session

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

def create_app():
    """Create and configure the Flask app"""
    app = Flask(__name__, static_folder='static', static_url_path='/static')
    
    # Configure app from environment variables
    app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'lead-agent-secret-key')
    app.config['SESSION_TYPE'] = 'filesystem'
    app.config['SESSION_FILE_DIR'] = 'flask_session'
    app.config['SESSION_COOKIE_NAME'] = 'lead_session'
    
    # Database configuration
    app.config['DATABASE_URL'] = os.environ.get('DATABASE_URL', 'sqlite:///lead_sessions.db')
    
    # Initialize Flask-Session
    Session(app)
    
    # Register blueprints
    from app.routes import main, lead, whatsapp
    app.register_blueprint(main)
    app.register_blueprint(lead)
    app.register_blueprint(whatsapp, url_prefix='/api/v1/spiro/whatsapp')
    
    return app
