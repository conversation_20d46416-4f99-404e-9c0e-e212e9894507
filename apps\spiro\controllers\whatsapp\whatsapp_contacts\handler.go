package whatsapp_contacts


import (
	"fmt"
	shared "libs/shared"
	"strconv"
	"github.com/labstack/echo/v4"
)

type Handler interface {
}

type handler struct {
	service Service
}

var newHandlerObj *handler //singleton object

// singleton function
func NewHandler() *handler {
	if newHandlerObj != nil {
		return newHandlerObj
	}

	new_service := NewService()
	newHandlerObj = &handler{new_service}
	return newHandlerObj
}

// ------------------Product Brand--------------------------------------------------------------------------------------------------
func (h *handler) CreateWhatsappContact(c echo.Context) error {
	fmt.Println("entered lookup handler-------")
	data := c.Get("sampleCreate").(CreatePayloadDto)

	fmt.Println("payload data", data)	

	metaData := shared.ApiMetaData{}
	shared.JsonMarshaller(c.Get("metaData"), &metaData)

	serviceResp, err := h.service.CreateWhatsappContact(data, metaData)
	if err != nil {
		return shared.RespFailure(c, "Internal Error", err)
	}

	fmt.Println("serviceResp", serviceResp)

	var apiResp = CreateorUpdateRespDto{}
	shared.JsonMarshaller(serviceResp, &apiResp)

	return shared.RespSuccess(c, "Created Successfully", apiResp)
}
func (h *handler) UpdateWhatsappContact(c echo.Context) error {
	data := c.Get("sampleUpdate").(UpdatePayloadDto)

	metaData := shared.ApiMetaData{}
	shared.JsonMarshaller(c.Get("metaData"), &metaData)

	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		return shared.RespFailure(c, "Invalid ID", err)
	}

	filterQuery := map[string]interface{}{
		"id": id,
	}

	_, err = h.service.UpdateWhatsappContact(data, metaData, filterQuery)
	if err != nil {
		return shared.RespFailure(c, "Internal Error", err)
	}

	var apiResp = CreateorUpdateRespDto{}
	shared.JsonMarshaller(filterQuery, &apiResp)

	return shared.RespSuccess(c, "Updated Successfully", apiResp)
}
func (h *handler) GetWhatsappContact(c echo.Context) error {
	metaData := shared.ApiMetaData{}
	shared.JsonMarshaller(c.Get("metaData"), &metaData)

	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		return shared.RespFailure(c, "Invalid ID", err)
	}

	filterQuery := map[string]interface{}{
		"id": id,
	}

	serviceResp, err := h.service.GetWhatsappContact(metaData, filterQuery)
	if err != nil {
		return shared.RespFailure(c, "Internal Error", err)
	}

	var apiResp = GetRespDto{}
	shared.JsonMarshaller(serviceResp, &apiResp)

	return shared.RespSuccess(c, "Data Retrieved Successfully", apiResp)
}
func (h *handler) ListWhatsappContact(c echo.Context) error {
	metaData := shared.ApiMetaData{}
	shared.JsonMarshaller(c.Get("metaData"), &metaData)

	filterQuery := map[string]interface{}{}
	sortings := map[string]interface{}{
		"created_at": -1,
		// "updated_at": -1,
	}
	perPage := 10
	pageNo := 1

	if per_page := c.QueryParam("per_page"); per_page != "" {
		perPage, _ = strconv.Atoi(per_page)
	}
	if page_no := c.QueryParam("page_no"); page_no != "" {
		pageNo, _ = strconv.Atoi(page_no)
	}
	if account_number := c.QueryParam("account_id"); account_number != "" {
		filterQuery["whatsapp_account_id"] = account_number
	}

	serviceResp, err := h.service.ListWhatsappContact(metaData, filterQuery, sortings, perPage, pageNo)
	if err != nil {
		return shared.RespFailure(c, "Internal Error", err)
	}

	var apiResp = []ListRespDto{}
	shared.JsonMarshaller(serviceResp["data"], &apiResp)

	var finalResp = []interface{}{}
	shared.JsonMarshaller(apiResp, &finalResp)

	return shared.RespSuccessWithPagination(c, "List retrieved successfully", finalResp, serviceResp["pagination"])
}
func (h *handler) DeleteWhatsappContact(c echo.Context) error {
	metaData := shared.ApiMetaData{}
	shared.JsonMarshaller(c.Get("metaData"), &metaData)

	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		return shared.RespFailure(c, "Invalid ID", err)
	}

	filterQuery := map[string]interface{}{
		"id": id,
	}

	serviceResp, err := h.service.DeleteWhatsappContact(metaData, filterQuery)
	fmt.Println("serviceResp", serviceResp)
	fmt.Println("err", err)
	fmt.Println("ffffffffffffffffffffff", filterQuery)
	if err != nil {
		return shared.RespFailure(c, "Internal Error", err)
	}

	return shared.RespSuccess(c, "Deleted Successfully", id)
}
