package backend

import (
	sampleRouter "apps/spiro/controllers/sample"
	lookupTypeRouter "apps/spiro/controllers/lookup_type"
	whatsapp  "apps/spiro/controllers/whatsapp"
	rasa  "apps/spiro/controllers/rasa"

	"github.com/labstack/echo/v4"
)

func Init(g *echo.Group) {
	// =========================== API Routes ===================================
	sampleRouter.NewHandler().Route(g.Group("/sample"))
	lookupTypeRouter.NewHandler().Route(g.Group("/lookup_type"))
	whatsapp.NewHandler().Route(g.Group("/whatsapp"))
	rasa.Route(g.Group("/rasa"))


}
