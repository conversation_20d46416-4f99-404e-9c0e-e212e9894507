# Spiro EV Sales Lead Agent

A dynamic LLM-based chatbot for Spiro electric vehicle sales with function calling architecture.

## Overview

This is a single-agent chatbot that follows the Spiro EV sales script dynamically using Gemini LLM. It has two main tools:
1. **Sales Flow Progression** - Main tool for advancing through the sales script
2. **Knowledge Base Q&A** - Secondary tool for answering user questions

## Features

- **Dynamic LLM Responses**: No hardcoded messages, everything generated by Gemini
- **Function Calling**: Two tools for flow progression and knowledge Q&A
- **State Persistence**: Database storage by phone number
- **Flow Priority**: Always prioritizes completing the sales script
- **WhatsApp Integration**: Webhook support for WhatsApp Business API
- **Web Interface**: Simple chat interface for testing

## Sales Script Flow

1. **Profile Selection**: University student / Salaried / Boda Boda rider / Fleet operator
2. **Petrol Bike Ownership**: Yes/No
3. **Ownership Duration**: < 3 years / > 3 years (if applicable)
4. **Model Selection**: Model A / Model B / Model C
5. **Loan Inquiry**: Yes/No
6. **Existing Loan Check**: Yes/No (if loan needed)
7. **Financing Options**: KCB, Mogo, Watu, Mkopa details
8. **Financing Selection**: User choice
9. **Document Collection**: ID, KRA pin, bank details
10. **Experience Rating**: 1-5 rating

## Installation

1. **Clone and navigate to the project:**
   ```bash
   cd apps/lead_agent
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your Gemini API key
   ```

4. **Run the application:**
   ```bash
   python run.py
   ```

## Environment Variables

Create a `.env` file with:

```env
# Gemini API Configuration
GEMINI_API_KEY=your_gemini_api_key_here

# Flask Configuration
SECRET_KEY=lead-agent-secret-key
FLASK_HOST=0.0.0.0
FLASK_PORT=5001
FLASK_DEBUG=1

# Database Configuration
DATABASE_URL=sqlite:///lead_sessions.db
```

## API Endpoints

### Start Conversation
```http
POST /api/lead/start
Content-Type: application/json

{
    "from": "************",
    "force_restart": false
}
```

### Continue Conversation
```http
POST /api/lead/continue
Content-Type: application/json

{
    "from": "************",
    "message": "University student"
}
```

### WhatsApp Webhook
```http
POST /api/v1/spiro/whatsapp/webhook
Content-Type: application/json

{
    "object": "whatsapp_business_account",
    "entry": [...]
}
```

### Clear Session
```http
POST /api/lead/clear/************
```

### List Sessions
```http
GET /api/lead/sessions
```

## Response Format

All API responses follow this format:

```json
{
    "success": true,
    "message": "Generated by LLM",
    "message_type": "flow",
    "flow_name": "profile_selection",
    "current_step": "profile_selection",
    "should_progress": false,
    "timestamp": "**********",
    "whatsapp_from": "************",
    "whatsapp_message_id": "MSG_**********_000000",
    "state_updates": []
}
```

## Project Structure

```
apps/lead_agent/
├── app/
│   ├── __init__.py              # Flask app factory
│   ├── routes.py                # API routes
│   ├── models/
│   │   ├── __init__.py
│   │   └── session_model.py     # Database models
│   ├── services/
│   │   ├── __init__.py
│   │   ├── agent_service.py     # Core agent logic
│   │   ├── session_service.py   # Session management
│   │   └── knowledge_service.py # Knowledge base (placeholder)
│   ├── static/
│   │   ├── css/style.css        # Styles
│   │   ├── js/chat.js          # Chat interface
│   │   └── img/                # Images
│   └── templates/
│       └── lead_chat.html      # Web interface
├── requirements.txt            # Dependencies
├── run.py                     # Application entry point
├── .env                       # Environment variables
├── README.md                  # This file
└── IMPLEMENTATION_PLAN.md     # Detailed implementation plan
```

## Architecture

### Single Agent Design
- **SpiroSalesAgent**: Handles both sales flow and knowledge Q&A
- **Two Tools**: `progress_sales_flow` (primary) and `answer_from_knowledge` (secondary)
- **Flow Priority**: Always prioritizes advancing the sales script

### State Management
- **SalesInformation**: Tracks user responses through the sales process
- **AgentState**: Manages overall conversation state
- **Database Persistence**: SQLite database with session storage

### LLM Integration
- **Gemini API**: Uses OpenAI-compatible interface
- **Function Calling**: Dynamic tool selection based on user input
- **No Hardcoded Messages**: Everything generated dynamically

## Usage

1. **Web Interface**: Visit `http://localhost:5001` for testing
2. **WhatsApp**: Configure webhook URL for production use
3. **API**: Use direct API calls for integration

## Development

### Adding Knowledge Base
1. Update `knowledge_service.py` with actual knowledge base
2. Integrate with vector database or search service
3. Update the `answer_from_knowledge` tool handler

### Customizing Sales Script
1. Modify the system prompt in `agent_service.py`
2. Update `SalesInformation` model fields
3. Adjust the `get_current_step()` logic

### Testing
- Use the web interface for quick testing
- Test API endpoints with curl or Postman
- Monitor logs for debugging

## Deployment

1. **Production Environment**:
   - Set `FLASK_DEBUG=0`
   - Use production database (PostgreSQL recommended)
   - Configure proper logging

2. **WhatsApp Integration**:
   - Set up WhatsApp Business API
   - Configure webhook URL
   - Add phone number validation

3. **Monitoring**:
   - Add application monitoring
   - Set up log aggregation
   - Monitor API response times

## License

This project is part of the Spiro EV sales system.
