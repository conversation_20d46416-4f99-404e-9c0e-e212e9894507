{"name": "apps/schedulars", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "apps/schedulars", "tags": [], "targets": {"build": {"executor": "@nx-go/nx-go:build", "options": {"main": "{projectRoot}/main.go"}}, "serve": {"executor": "@nx-go/nx-go:serve", "options": {"main": "{projectRoot}/main.go"}}, "test": {"executor": "@nx-go/nx-go:test"}, "lint": {"executor": "@nx-go/nx-go:lint"}, "tidy": {"executor": "@nx-go/nx-go:tidy"}}}