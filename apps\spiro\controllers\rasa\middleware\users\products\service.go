package products

import (
	// env "apps/spiro/config"
	"errors"
	"fmt"
	shared "libs/shared"
	helpers "libs/shared/utils/helpers"
	"os"
	// "math"
	// "strings"
)

var scheme = "https"
var host = ""

type Service interface {
	
	List(data ListRequest) (ListResponse, error)
	View(data ViewRequest) (ViewResponse, error)
}

type service struct {
	db shared.PostgresRepositoryFunctions
}

var newServiceObj *service //singleton object

var products_data = Products{
	Data: []struct {
		ID          string `json:"id"`
		Title       string `json:"title"`
		Description string `json:"description"`
		Image       string `json:"image"`
		ViewDescription string `json:"view_description"`
	}{

		{
			ID:          "1",
			Title:       "EKON 450M1 (E66)",
			ViewDescription: "𝗖𝗼𝗺𝗽𝗮𝘁𝗶𝗯𝗹𝗲 𝗕𝗮𝘁𝘁𝗲𝗿𝗶𝗲𝘀 : 46 Ah, 45 Ah, 45 Ah  \n𝗥𝗮𝗻𝗴𝗲 : <95 KM  \n𝗥𝗮𝘁𝗲𝗱 𝗣𝗮𝘆𝗹𝗼𝗮𝗱 : <250 KG  \n𝗗𝗶𝘀𝗽𝗹𝗮𝘆 : 5\" Colour LCD  \n𝗙𝗮𝘀𝘁 𝗖𝗵𝗮𝗿𝗴𝗲𝗿:Yes",
			Description: "• 𝗖𝗼𝗺𝗽𝗮𝘁𝗶𝗯𝗹𝗲 𝗕𝗮𝘁𝘁𝗲𝗿𝗶𝗲𝘀: 46 Ah\n• 𝗥𝗮𝗻𝗴𝗲: <95 KM\n• 𝗠𝗼𝘁𝗼𝗿 𝗣𝗼𝘄𝗲𝗿: 4.5 kW / 9 Kw",
			Image:  "",
		},
		{
			ID:          "2",
			Title:       "EKON 450M1 (E66)",
			ViewDescription: "𝗖𝗼𝗺𝗽𝗮𝘁𝗶𝗯𝗹𝗲 𝗕𝗮𝘁𝘁𝗲𝗿𝗶𝗲𝘀 : 46Ah, 4.17x2 Battery  \n𝗥𝗮𝗻𝗴𝗲 : <95 KM  \n𝗥𝗮𝘁𝗲𝗱 𝗣𝗮𝘆𝗹𝗼𝗮𝗱 : <250 KG  \n𝗗𝗶𝘀𝗽𝗹𝗮𝘆 : 5\" Colour LCD  \n𝗙𝗮𝘀𝘁 𝗖𝗵𝗮𝗿𝗴𝗲𝗿:Yes",
			Description: "• 𝗖𝗼𝗺𝗽𝗮𝘁𝗶𝗯𝗹𝗲 𝗕𝗮𝘁𝘁𝗲𝗿𝗶𝗲𝘀: 46Ah\n• 𝗥𝗮𝗻𝗴𝗲: <95 KM\n• 𝗠𝗼𝘁𝗼𝗿 𝗣𝗼𝘄𝗲𝗿: 4.5 kW / 9 kW",
		},
		{
			ID:          "3",
			Title:       "EKON 400M2 (QBC1)",
			ViewDescription: "𝗖𝗼𝗺𝗽𝗮𝘁𝗶𝗯𝗹𝗲 𝗕𝗮𝘁𝘁𝗲𝗿𝗶𝗲𝘀 : 46Ah, 38Ah, 45 Ah  \n𝗥𝗮𝗻𝗴𝗲 : <85 KM  \n𝗥𝗮𝘁𝗲𝗱 𝗣𝗮𝘆𝗹𝗼𝗮𝗱 : <280 KG  \n𝗗𝗶𝘀𝗽𝗹𝗮𝘆 : Inverted LCD Display  \n𝗙𝗮𝘀𝘁 𝗖𝗵𝗮𝗿𝗴𝗲𝗿:Yes",
			Description: "• 𝗖𝗼𝗺𝗽𝗮𝘁𝗶𝗯𝗹𝗲 𝗕𝗮𝘁𝘁𝗲𝗿𝗶𝗲𝘀: 46Ah\n• 𝗥𝗮𝗻𝗴𝗲: < 85 KM\n• 𝗠𝗼𝘁𝗼𝗿 𝗣𝗼𝘄𝗲𝗿: 4 kW / 7.5 kW",
		},
		{
			ID:          "4",
			Title:       "EKON 450M3 (E56)",
			ViewDescription: "𝗖𝗼𝗺𝗽𝗮𝘁𝗶𝗯𝗹𝗲 𝗕𝗮𝘁𝘁𝗲𝗿𝗶𝗲𝘀 : 46Ah, 38Ah, 45 Ah  \n𝗥𝗮𝗻𝗴𝗲 : <95 KM  \n𝗥𝗮𝘁𝗲𝗱 𝗣𝗮𝘆𝗹𝗼𝗮𝗱 : <225 KG  \n𝗗𝗶𝘀𝗽𝗹𝗮𝘆 : Inverted LCD Display  \n𝗙𝗮𝘀𝘁 𝗖𝗵𝗮𝗿𝗴𝗲𝗿:Yes",
			Description: "• 𝗖𝗼𝗺𝗽𝗮𝘁𝗶𝗯𝗹𝗲 𝗕𝗮𝘁𝘁𝗲𝗿𝗶𝗲𝘀: 46Ah\n• 𝗥𝗮𝗻𝗴𝗲: <95 KM\n• 𝗠𝗼𝘁𝗼𝗿 𝗣𝗼𝘄𝗲𝗿: 4.5 kW / 9 kW",
		},
		{
			ID:          "5",
			Title:       "EKON ALPHA 900M (Base)",
			ViewDescription: "𝗖𝗼𝗺𝗽𝗮𝘁𝗶𝗯𝗹𝗲 𝗕𝗮𝘁𝘁𝗲𝗿𝗶𝗲𝘀 : 46Ah, 38Ah, 45 Ah  \n𝗥𝗮𝗻𝗴𝗲 : <95 KM  \n𝗥𝗮𝘁𝗲𝗱 𝗣𝗮𝘆𝗹𝗼𝗮𝗱 : <225 KG  \n𝗗𝗶𝘀𝗽𝗹𝗮𝘆 : Inverted LCD Display with BT & Alerts with Speaker  \n𝗙𝗮𝘀𝘁 𝗖𝗵𝗮𝗿𝗴𝗲𝗿:Yes",
			Description: "• 𝗖𝗼𝗺𝗽𝗮𝘁𝗶𝗯𝗹𝗲 𝗕𝗮𝘁𝘁𝗲𝗿𝗶𝗲𝘀: 46Ah\n• 𝗥𝗮𝗻𝗴𝗲: <95 KM\n• 𝗠𝗼𝘁𝗼𝗿 𝗣𝗼𝘄𝗲𝗿: 4.5 kW / 9 kW",
		},
		{
			ID:          "6",
			Title:       "EKON ALPHA 900M (Base)",
			ViewDescription: "𝗖𝗼𝗺𝗽𝗮𝘁𝗶𝗯𝗹𝗲 𝗕𝗮𝘁𝘁𝗲𝗿𝗶𝗲𝘀 : 46Ah, 38Ah, 45 Ah  \n𝗥𝗮𝗻𝗴𝗲 : <95 KM  \n𝗥𝗮𝘁𝗲𝗱 𝗣𝗮𝘆𝗹𝗼𝗮𝗱 : <225 KG  \n𝗗𝗶𝘀𝗽𝗹𝗮𝘆 : Inverted LCD Display  \n𝗙𝗮𝘀𝘁 𝗖𝗵𝗮𝗿𝗴𝗲𝗿:Yes",
			Description: "• 𝗖𝗼𝗺𝗽𝗮𝘁𝗶𝗯𝗹𝗲 𝗕𝗮𝘁𝘁𝗲𝗿𝗶𝗲𝘀: 46Ah\n• 𝗥𝗮𝗻𝗴𝗲: <95 KM\n• 𝗠𝗼𝘁𝗼𝗿 𝗣𝗼𝘄𝗲𝗿: 4.5 kW / 9 kW",
		},
		{
			ID:          "7",
			Title:       "EKON ALPHA 900M (Collectible)",
			ViewDescription: "𝗖𝗼𝗺𝗽𝗮𝘁𝗶𝗯𝗹𝗲 𝗕𝗮𝘁𝘁𝗲𝗿𝗶𝗲𝘀 : 58Ah, 58Ah  \n𝗥𝗮𝗻𝗴𝗲 : <180 KM  \n𝗥𝗮𝘁𝗲𝗱 𝗣𝗮𝘆𝗹𝗼𝗮𝗱 : <225 KG  \n𝗗𝗶𝘀𝗽𝗹𝗮𝘆 : Touchscreen android based  \n𝗙𝗮𝘀𝘁 𝗖𝗵𝗮𝗿𝗴𝗲𝗿:Yes",
			Description: "• 𝗖𝗼𝗺𝗽𝗮𝘁𝗶𝗯𝗹𝗲 𝗕𝗮𝘁𝘁𝗲𝗿𝗶𝗲𝘀: 58Ah\n• 𝗥𝗮𝗻𝗴𝗲: <180 KM\n• 𝗠𝗼𝘁𝗼𝗿 𝗣𝗼𝘄𝗲𝗿: 4.5 kW / 9 kW",
		},
		{
			ID:          "8",
			Title:       "EKON BETA 900M (Base)",
			ViewDescription: "𝗖𝗼𝗺𝗽𝗮𝘁𝗶𝗯𝗹𝗲 𝗕𝗮𝘁𝘁𝗲𝗿𝗶𝗲𝘀 : 46Ah, 38Ah, 45 Ah  \n𝗥𝗮𝗻𝗴𝗲 : <95 KM  \n𝗥𝗮𝘁𝗲𝗱 𝗣𝗮𝘆𝗹𝗼𝗮𝗱 : <225 KG  \n𝗗𝗶𝘀𝗽𝗹𝗮𝘆 : Inverted LCD Display with BT & Alerts with Speaker  \n𝗙𝗮𝘀𝘁 𝗖𝗵𝗮𝗿𝗴𝗲𝗿:Yes",
			Description: "• 𝗖𝗼𝗺𝗽𝗮𝘁𝗶𝗯𝗹𝗲 𝗕𝗮𝘁𝘁𝗲𝗿𝗶𝗲𝘀: 46Ah\n• 𝗥𝗮𝗻𝗴𝗲: <95 KM\n• 𝗠𝗼𝘁𝗼𝗿 𝗣𝗼𝘄𝗲𝗿: 4 kW / 7.5 Kw (Peak goes upto 9kW)",
		},
		{
			ID:          "9",
			Title:       "EKON BETA 900M (collectible)",
			ViewDescription: "𝗖𝗼𝗺𝗽𝗮𝘁𝗶𝗯𝗹𝗲 𝗕𝗮𝘁𝘁𝗲𝗿𝗶𝗲𝘀 : 58Ah, 58Ah  \n𝗥𝗮𝗻𝗴𝗲 : <180 KM  \n𝗥𝗮𝘁𝗲𝗱 𝗣𝗮𝘆𝗹𝗼𝗮𝗱 : <225 KG  \n𝗗𝗶𝘀𝗽𝗹𝗮𝘆 : Touchscreen android based  \n𝗙𝗮𝘀𝘁 𝗖𝗵𝗮𝗿𝗴𝗲𝗿:Yes",
			Description: "• 𝗖𝗼𝗺𝗽𝗮𝗍𝗶𝗯𝗹𝗲 𝗕𝗮𝘁𝘁𝗲𝗿𝗶𝗲𝘀: 58Ah\n• 𝗥𝗮𝗻𝗴𝗲: <180 KM\n• 𝗠𝗼𝘁𝗼𝗿 𝗣𝗼𝘄𝗲𝗿: 4 kW / 7.5 Kw (Peak goes upto 9kW)",
		},
		{
			ID:          "10",
			Title:       "EKON 400M2 (V2)",
			ViewDescription: "𝗖𝗼𝗺𝗽𝗮𝘁𝗶𝗯𝗹𝗲 𝗕𝗮𝘁𝘁𝗲𝗿𝗶𝗲𝘀 : 46Ah, 38Ah, 45 Ah  \n𝗥𝗮𝗻𝗴𝗲 : <95 KM  \n𝗥𝗮𝘁𝗲𝗱 𝗣𝗮𝘆𝗹𝗼𝗮𝗱 : <240 KG  \n𝗗𝗶𝘀𝗽𝗹𝗮𝘆 : Inverted LCD Display  \n𝗙𝗮𝘀𝘁 𝗖𝗵𝗮𝗿𝗴𝗲𝗿:Yes",
			Description: "• 𝗖𝗼𝗺𝗽𝗮𝗍𝗶𝗯𝗹𝗲 𝗕𝗮𝘁𝘁𝗲𝗿𝗶𝗲𝘀: 46Ah\n• 𝗥𝗮𝗻𝗴𝗲: <95 KM\n• 𝗠𝗼𝘁𝗼𝗿 𝗣𝗼𝘄𝗲𝗿: 4.5 kW / 9 kW",
		},
		{
			ID:          "11",
			Title:       "EKON 400M2 (V2)",
			ViewDescription: "𝗖𝗼𝗺𝗽𝗮𝘁𝗶𝗯𝗹𝗲 𝗕𝗮𝘁𝘁𝗲𝗿𝗶𝗲𝘀 : 45 Ah, 45Ah  \n𝗥𝗮𝗻𝗴𝗲 : <95 KM  \n𝗥𝗮𝘁𝗲𝗱 𝗣𝗮𝘆𝗹𝗼𝗮𝗱 : <240 KG  \n𝗗𝗶𝘀𝗽𝗹𝗮𝘆 : Inverted LCD Display  \n𝗙𝗮𝘀𝘁 𝗖𝗵𝗮𝗿𝗴𝗲𝗿:Yes",
			Description: "• 𝗖𝗼𝗺𝗽𝗮𝗍𝗶𝗯𝗹𝗲 𝗕𝗮𝘁𝘁𝗲𝗿𝗶𝗲𝘀: 45 Ah\n• 𝗥𝗮𝗻𝗴𝗲: <95 KM\n• 𝗠𝗼𝘁𝗼𝗿 𝗣𝗼𝘄𝗲𝗿: 4 kW / 7.5 kW",
		},
		{
			ID:          "12",
			Title:       "EKON 350M1",
			ViewDescription: "𝗖𝗼𝗺𝗽𝗮𝘁𝗶𝗯𝗹𝗲 𝗕𝗮𝘁𝘁𝗲𝗿𝗶𝗲𝘀 : 46Ah, 38Ah, 45 Ah  \n𝗥𝗮𝗻𝗴𝗲 : <95 KM  \n𝗥𝗮𝘁𝗲𝗱 𝗣𝗮𝘆𝗹𝗼𝗮𝗱 : <200 KG  \n𝗗𝗶𝘀𝗽𝗹𝗮𝘆 : Inverted LCD Display  \n𝗙𝗮𝘀𝘁 𝗖𝗵𝗮𝗿𝗴𝗲𝗿:Yes",
			Description: "• 𝗖𝗼𝗺𝗽𝗮𝗍𝗶𝗯𝗹𝗲 𝗕𝗮𝘁𝘁𝗲𝗿𝗶𝗲𝘀: 46Ah\n• 𝗥𝗮𝗻𝗴𝗲: <95 KM\n• 𝗠𝗼𝘁𝗼𝗿 𝗣𝗼𝘄𝗲𝗿: 3.5 kW / <6.5kW",
		},
		{
			ID:          "13",
			Title:       "EKON 250M1",
			ViewDescription: "𝗖𝗼𝗺𝗽𝗮𝘁𝗶𝗯𝗹𝗲 𝗕𝗮𝘁𝘁𝗲𝗿𝗶𝗲𝘀 : 46Ah, 38Ah, 45 Ah  \n𝗥𝗮𝗻𝗴𝗲 : <95 KM  \n𝗥𝗮𝘁𝗲𝗱 𝗣𝗮𝘆𝗹𝗼𝗮𝗱 : <160 KG  \n𝗗𝗶𝘀𝗽𝗹𝗮𝘆 : Inverted LCD Display  \n𝗙𝗮𝘀𝘁 𝗖𝗵𝗮𝗿𝗴𝗲𝗿:Yes",
			Description: "• 𝗖𝗼𝗺𝗽𝗮𝗍𝗶𝗯𝗹𝗲 𝗕𝗮𝘁𝘁𝗲𝗿𝗶𝗲𝘀: 46Ah\n• 𝗥𝗮𝗻𝗴𝗲: <95 KM\n• 𝗠𝗼𝘁𝗼𝗿 𝗣𝗼𝘄𝗲𝗿: 2.5 kW / <5kW",
		},
		{
			ID:          "14",
			Title:       "EKON 450H1 i",
			ViewDescription: "𝗖𝗼𝗺𝗽𝗮𝘁𝗶𝗯𝗹𝗲 𝗕𝗮𝘁𝘁𝗲𝗿𝗶𝗲𝘀 : 45 Ah, 45Ah  \n𝗥𝗮𝗻𝗴𝗲 : <95 KM  \n𝗥𝗮𝘁𝗲𝗱 𝗣𝗮𝘆𝗹𝗼𝗮𝗱 : <180 KG  \n𝗗𝗶𝘀𝗽𝗹𝗮𝘆 : 5\" Colour LCD  \n𝗙𝗮𝘀𝘁 𝗖𝗵𝗮𝗿𝗴𝗲𝗿:Yes",
			Description: "• 𝗖𝗼𝗺𝗽𝗮𝗍𝗶𝗯𝗹𝗲 𝗕𝗮𝘁𝘁𝗲𝗿𝗶𝗲𝘀: 45 Ah\n• 𝗥𝗮𝗻𝗴𝗲: <95 KM\n• 𝗠𝗼𝘁𝗼𝗿 𝗣𝗼𝘄𝗲𝗿: 4.5 kW / 9 kW",
		},
	},Meta: struct{Status bool "json:\"status\""; Pagination struct{TotalPages uint "json:\"total_pages\""} "json:\"pagination\""}{
		Status: true,
		Pagination: struct{TotalPages uint "json:\"total_pages\""}{
			TotalPages: 1,
		},
	},
}

// singleton function
func NewService() *service {
	if newServiceObj != nil {
		return newServiceObj
	}
	new_db := shared.PostgresRepository()
	newServiceObj = &service{new_db}
	return newServiceObj
}

// ------------------Product Brand--------------------------------------------------------------------------------------------------


func (s *service) List(data ListRequest) (ListResponse, error) {

	// user, err := onboarding.GetUser(data.PhoneNumber)
	// if err != nil {
	// 	return ListResponse{}, err
	// }

	// if data.PageNo == 0 {
	// 	data.PageNo = 1
	// }

	// if data.PerPage == 0 {
	// 	data.PerPage = 20
	// }

	// if data.ProductIds != "" && data.ProductIds != " " {
	// 	listResponse := ListResponse{}
	// 	productIds := strings.Split(data.ProductIds, ",")
	// 	listResponse.TotalPages = uint(math.Ceil(float64(len(productIds)) / float64(data.PerPage)))
	// 	productIds = helpers.CustomPagination(productIds, int(data.PageNo), int(data.PerPage))
	// 	helpers.PrettyPrint("productIds", productIds)
	// 	for _, productId := range productIds {
	// 		productId = strings.Trim(productId, " ")
	// 		order, err := s.FindOne(ViewRequest{
	// 			PhoneNumber: data.PhoneNumber,
	// 			ProductId:   productId,
	// 		})
	// 		if err != nil {
	// 			continue
	// 		}
	// 		listResponse.Products = append(listResponse.Products, order)
	// 	}
	// 	return listResponse, nil
	// }

	// params := map[string]string{
	// 	"per_page": fmt.Sprint(data.PerPage),
	// 	"page_no":  fmt.Sprint(data.PageNo),
	// }

	// if data.Status != "" {
	// 	params["status"] = data.Status
	// }

	// if data.L1Category != "" {
	// 	params["level1_category"] = data.L1Category
	// }

	// bodyInterface, err := helpers.MakeRequest(helpers.Request{
	// 	Method: "GET",
	// 	Scheme: scheme,
	// 	Host:   host,
	// 	Path:   "/api/v1/catalog/products",
	// 	Header: map[string]string{
	// 		"Content-Type":  "application/json",
	// 		"Authorization": "Bearer " + "user.JWTToken",
	// 	},
	// 	Params: params,
	// })
	// if err != nil {
	// 	return ListResponse{}, err
	// }

	var products ListProductApiResponse
	helpers.JsonMarshaller(products_data, &products)
	if !products.Meta.Status {
		return ListResponse{}, errors.New("products not found")
	}

	response := ListResponse{
		TotalPages: products.Meta.Pagination.TotalPages,
		Products:   []Product{},
	}

	for _, product := range products.Data {
		currentDir, err := os.Getwd()
		if err != nil {
			return response,err
		}
		fmt.Println("Current Directory:", currentDir)
		var keyPath string
	
		// Construct path relative to current directory
		if(product.ID=="1" || product.ID=="6" || product.ID=="11" ){
			keyPath = currentDir + "/assets/images/spiro_bk_1.jpg"
		}else if(product.ID=="2" || product.ID=="7" || product.ID=="12"){
			keyPath = currentDir + "/assets/images/spiro_bk_2.jpeg"
		}else if(product.ID=="3" || product.ID=="8" || product.ID=="13"){
			keyPath = currentDir + "/assets/images/spiro_bk_3.jpeg"	
		}else if(product.ID=="4" || product.ID=="9" || product.ID=="14"){
			keyPath = currentDir + "/assets/images/spiro_bk_4.jpeg"
		}else if(product.ID=="5" || product.ID=="10"){
			keyPath = currentDir + "/assets/images/spiro_bk_5.jpeg"
		}

		// keyPath := currentDir + "/assets/images/spiro_bk_1.jpg"
		fmt.Println("Key Path:", keyPath)

		ImageFileToBase64,_ := helpers.ImageFileToBase64(keyPath)
		productResponse := Product{
			ProductId: fmt.Sprint(product.ID),
			Title:     product.Title,
			Text:      product.Description,
			Image:     ImageFileToBase64,
		}


		response.Products = append(response.Products, productResponse)
	}

	return response, nil
}


func (s *service) View(data ViewRequest) (ViewResponse, error) {

	// user, err := onboarding.GetUser(data.PhoneNumber)
	// if err != nil {
	// 	return ViewResponse{}, err
	// }

	// id := fmt.Sprint(data.ProductId)

	// bodyInterface, err := helpers.MakeRequest(helpers.Request{
	// 	Method: "GET",
	// 	Scheme: scheme,
	// 	Host:   host,
	// 	Path:   "/api/v1/catalog/products/get/" + id,
	// 	Header: map[string]string{
	// 		"Content-Type":  "application/json",
	// 		"Authorization": "Bearer " + user.JWTToken,
	// 	},
	// 	Params: map[string]string{},
	// })
	// if err != nil {
	// 	return ViewResponse{}, err
	// }

	// var product ViewProductApiResponse
	// helpers.JsonMarshaller(bodyInterface, &product)
	// if !product.Meta.Status {
	// 	return ViewResponse{}, errors.New("product not found")
	// }

	viewResponse := ViewResponse{
		ProductId:      data.ProductId,
		ProductName:    "",
		ProductDetails: "",
		ProductImages:  "",
	}

	for _, product := range products_data.Data {
		fmt.Println("product.ID", product.ID)
		fmt.Println("data.ProductId", data.ProductId)
        if product.ID == data.ProductId {
			fmt.Println("product.ID == data.ProductId", product)
			currentDir, err := os.Getwd()
			if err != nil {
				return viewResponse,err
			}
			fmt.Println("Current Directory:", currentDir)
		
			// Construct path relative to current directory
			// keyPath := currentDir + "/assets/images/spiro_bk_1.jpg"
			var keyPath string
	
			// Construct path relative to current directory
			if(product.ID=="1" || product.ID=="6" || product.ID=="11" ){
				keyPath = currentDir + "/assets/images/spiro_bk_1.jpg"
			}else if(product.ID=="2" || product.ID=="7" || product.ID=="12"){
				keyPath = currentDir + "/assets/images/spiro_bk_2.jpeg"
			}else if(product.ID=="3" || product.ID=="8" || product.ID=="13"){
				keyPath = currentDir + "/assets/images/spiro_bk_3.jpeg"	
			}else if(product.ID=="4" || product.ID=="9" || product.ID=="14"){
				keyPath = currentDir + "/assets/images/spiro_bk_4.jpeg"
			}else if(product.ID=="5" || product.ID=="10"){
				keyPath = currentDir + "/assets/images/spiro_bk_5.jpeg"
			}
			fmt.Println("Key Path:", keyPath)
	
			ImageFileToBase64,_ := helpers.ImageFileToBase64(keyPath)
			viewResponse = ViewResponse{
				ProductId:      product.ID,
				ProductName:    product.Title,
				ProductDetails: product.ViewDescription,
				ProductImages:  ImageFileToBase64,
			}
            return viewResponse, nil
        }
    }

	return viewResponse, nil
}

func (s *service) FindOne(data ViewRequest) (Product, error) {

	// user, err := onboarding.GetUser(data.PhoneNumber)
	// if err != nil {
	// 	return Product{}, err
	// }

	// bodyInterface, err := helpers.MakeRequest(helpers.Request{
	// 	Method: "GET",
	// 	Scheme: scheme,
	// 	Host:   host,
	// 	Path:   "/api/v1/catalog/products/get/" + data.ProductId,
	// 	Header: map[string]string{
	// 		"Content-Type":  "application/json",
	// 		"Authorization": "Bearer " + "user.JWTToken",
	// 	},
	// 	Params: map[string]string{},
	// })
	// if err != nil {
	// 	return Product{}, err
	// }

	// var product ViewProductApiResponse
	// helpers.JsonMarshaller(bodyInterface, &product)
	// if !product.Meta.Status {
	// 	return Product{}, errors.New("product not found")
	// }

	productResponse := Product{
		ProductId: data.ProductId,
		Text:      "",
		Image:     "",
	}

	// productResponse.Text += fmt.Sprintf("%v\n", product.Data.Name)
	// productResponse.Text += fmt.Sprintf("Category : %v\n", product.Data.Level1Category.Name)
	// productResponse.Text += fmt.Sprintf("SubCategory : %v\n", product.Data.Level2Category.Name)
	// productResponse.Text += fmt.Sprintf("Status : %v\n", product.Data.Status.DisplayName)

	// if len(product.Data.ImageArr) > 0 {
	// 	productResponse.Image = product.Data.ImageArr[0]
	// }

	for _, product := range products_data.Data {
        if product.ID == data.ProductId {
            productResponse = Product{
                ProductId: product.ID,
                Title:    product.Title, 
                Text:     product.Description,
                Image:     products_data.Data[0].Image,
            }
            return productResponse, nil
        }
    }
    

	return productResponse, nil
}
