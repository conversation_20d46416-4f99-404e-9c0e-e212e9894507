<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spiro EV Sales Pipeline</title>
    <link rel="stylesheet" href="/static/css/markdown.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
            color: #333;
        }
        .chat-container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .messages {
            height: 500px;
            overflow-y: auto;
            margin-bottom: 20px;
            padding: 10px;
            border: 1px solid #e1e1e1;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: 18px;
            max-width: 85%;
            word-wrap: break-word;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }
        .user-message {
            background-color: #e1f5fe;
            margin-left: auto;
            margin-right: 0;
            border-bottom-right-radius: 4px;
            text-align: right;
            border: 1px solid #b3e5fc;
        }
        .assistant-message {
            background-color: #f5f5f5;
            margin-right: auto;
            margin-left: 0;
            border-bottom-left-radius: 4px;
            border: 1px solid #e0e0e0;
        }
        .input-container {
            display: flex;
            gap: 10px;
        }
        input[type="text"] {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }
        input[type="text"]:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
        }
        button {
            padding: 12px 24px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        button:hover {
            background-color: #45a049;
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        button:active {
            transform: translateY(0);
            box-shadow: none;
        }
        .debug-panel {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 14px;
            overflow-y: auto;
            max-height: 600px;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        .controls button {
            flex: 1;
            background-color: #757575;
        }
        .controls button:hover {
            background-color: #616161;
        }
        h1 {
            color: #2e7d32;
            text-align: center;
            font-size: 2.2em;
            margin-bottom: 25px;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.1);
        }
        .llm-call {
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 10px;
        }
        .llm-call-header {
            display: flex;
            justify-content: space-between;
            background-color: #eaeaea;
            padding: 5px 10px;
            margin: -10px -10px 10px -10px;
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            font-weight: bold;
        }
        .llm-call-body {
            margin-top: 5px;
        }
        .prompt-container, .response-container {
            margin-top: 10px;
        }
        .prompt, .response {
            background-color: #f9f9f9;
            padding: 8px;
            border-radius: 3px;
            border-left: 3px solid #4CAF50;
            white-space: pre-wrap;
            font-size: 12px;
            max-height: 150px;
            overflow-y: auto;
        }
        .performance {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
            font-size: 12px;
            color: #666;
        }
        .collapsible {
            cursor: pointer;
            padding: 5px;
            width: 100%;
            text-align: left;
            outline: none;
            font-size: 14px;
            margin-bottom: 5px;
            background-color: #eee;
            border: none;
            border-radius: 3px;
        }
        .content {
            display: none;
            overflow: hidden;
            background-color: #f9f9f9;
            padding: 0 10px;
        }
        .decision-node {
            background-color: #fff0f0;
            border-left: 3px solid #ff8080;
        }
        .action-node {
            background-color: #f0fff0;
            border-left: 3px solid #80ff80;
        }
        .node-info {
            font-size: 11px;
            color: #555;
            margin-bottom: 5px;
        }
        .debug-summary {
            margin-bottom: 15px;
            padding: 10px;
            background-color: #e8f5e9;
            border-radius: 5px;
            font-size: 14px;
        }
        .stats {
            display: flex;
            justify-content: space-between;
        }
        .refresh-debug {
            background-color: #90a4ae;
            margin-left: auto;
            display: block;
            padding: 6px 12px;
            margin-bottom: 10px;
        }
        .badge {
            display: inline-block;
            border-radius: 10px;
            padding: 3px 8px;
            margin-right: 5px;
            font-size: 11px;
            color: white;
        }
        .badge-action {
            background-color: #4caf50;
        }
        .badge-decision {
            background-color: #ff5722;
        }
        /* Loading indicator */
        .loading {
            display: none;
            margin: 15px auto;
            text-align: center;
            font-style: italic;
            color: #666;
        }
        .loading::after {
            content: '';
            display: inline-block;
            width: 12px;
            height: 12px;
            margin-left: 10px;
            border: 2px solid #ccc;
            border-radius: 50%;
            border-top-color: #4CAF50;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        /* User avatar styles */
        .message-wrapper {
            display: flex;
            margin-bottom: 15px;
        }
        .avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            margin-right: 10px;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
        }
        .user-avatar {
            background-color: #2196F3;
        }
        .assistant-avatar {
            background-color: #4CAF50;
        }
        .user-message-wrapper {
            flex-direction: row-reverse;
        }
        .user-message-wrapper .avatar {
            margin-right: 0;
            margin-left: 10px;
        }
        
        /* Make the code blocks better */
        .markdown-content pre {
            background-color: #1e1e1e;
            color: #d4d4d4;
            padding: 10px;
            border-radius: 5px;
        }
        .markdown-content code {
            font-family: 'Consolas', 'Courier New', monospace;
        }
        
        /* File upload styles */
        .file-upload {
            margin-top: 15px;
            padding: 15px;
            border: 1px dashed #ccc;
            border-radius: 5px;
            background-color: #f9f9f9;
            text-align: center;
        }
        .file-upload p {
            margin-bottom: 10px;
            font-size: 14px;
        }
        .file-controls {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
            gap: 10px;
        }
        .file-name {
            padding: 8px 12px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            flex-grow: 1;
            max-width: 250px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .upload-button {
            padding: 10px 20px;
            background-color: #ff9800;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .upload-button:hover {
            background-color: #f57c00;
        }
        .upload-button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .loader {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4CAF50;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }
        .hidden {
            display: none;
        }
        
        /* User info panel */
        .user-info-panel {
            margin-top: 15px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            background-color: #f5f5f5;
            display: none;
        }
        .user-info-details {
            font-size: 14px;
            line-height: 1.5;
        }
        .missing-fields-form {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .missing-fields-form h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 10px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #666;
        }
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .missing-fields-form button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        }
        .missing-fields-form button:hover {
            background-color: #0056b3;
        }
        #upload-container {
            transition: all 0.3s ease;
        }
        #upload-button {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        }
        #upload-button:hover {
            background-color: #218838;
        }
        #upload-button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .system-message {
            background-color: #f8f9fa;
            border-left: 3px solid #6c757d;
            margin: 10px 0;
            padding: 10px;
            font-size: 0.9rem;
            border-radius: 5px;
        }
        .phone-selection {
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px;
            background-color: #f5f5f5;
            border-radius: 5px;
        }
        .phone-selection label {
            font-weight: bold;
            min-width: 120px;
        }
        .phone-dropdown {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .clear-btn {
            padding: 10px 20px;
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .clear-btn:hover {
            background-color: #218838;
        }
    </style>
    <script>
        // Available phone numbers for testing - expand this list for more examples
        const phoneNumbers = [
            "+254712345678",
            "+254787654321",
            "+254755123456",
            "+1234567890",
            "+1987654321"
        ];
    </script>
</head>
<body>
    <h1>Spiro EV Sales Pipeline</h1>
    
    <!-- Enhance the existing phone selection component -->
    <div class="phone-selection">
        <label for="phone-number">Phone Number:</label>
        <select id="phone-number" class="phone-dropdown">
            <option value="">Enter or select your phone number</option>
            <script>
                // Populate dropdown with phone numbers
                phoneNumbers.forEach(phone => {
                    document.write(`<option value="${phone}">${phone}</option>`);
                });
            </script>
        </select>
        <button id="clear-session" class="clear-btn">Clear Session</button>
    </div>

    <div class="chat-container">
        <div class="messages" id="messages"></div>
        
        <div id="loading-indicator" class="loading">Thinking...</div>
        
        <div class="input-container">
            <input type="text" id="user-input" placeholder="Type your message here..." />
            <button id="send-button">Send</button>
        </div>
        
        <div class="file-upload">
            <p><strong>Upload National ID</strong> to automatically fill in your information:</p>
            <input type="file" id="imageInput" accept="image/*" style="display: none;">
            <div class="file-controls">
                <button id="browse-button" class="btn btn-primary">Browse Files</button>
                <div class="file-name" id="fileName">No file selected</div>
                <button id="upload-button" class="upload-button" disabled>Upload</button>
                <div id="uploadSpinner" class="loader hidden"></div>
            </div>
            <p class="upload-note">You can upload your ID at any time during our conversation to speed up the process.</p>
        </div>
        
        <div class="user-info-panel" id="userInfoPanel">
            <h3>Extracted Information</h3>
            <div class="user-info-details" id="extractedInfo">
                No information available yet.
            </div>
        </div>
        
        <div class="controls">
            <button id="restart-button">Restart Conversation</button>
            <button id="debug-toggle">Toggle Debug Panel</button>
        </div>
    </div>
    
    <div class="debug-panel" id="debug-panel" style="display: none;">
        <h3>Debug Information</h3>
        
        <div class="debug-summary">
            <div class="stats">
                <div>
                    <strong>Current Node:</strong> <span id="current-node">start</span>
                </div>
                <div>
                    <strong>Next Node:</strong> <span id="next-node">None</span>
                </div>
                <div>
                    <strong>Total LLM Calls:</strong> <span id="llm-calls">0</span>
                </div>
            </div>
        </div>
        
        <button class="refresh-debug" id="refresh-debug">Refresh Debug Data</button>
        
        <div id="llm-calls-container">
            <!-- LLM calls will be displayed here -->
        </div>
    </div>
    
    <!-- Load our markdown utilities -->
    <script src="/static/js/markdown.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize markdown renderer
            MarkdownRenderer.init();
            
            const messagesContainer = document.getElementById('messages');
            const userInput = document.getElementById('user-input');
            const sendButton = document.getElementById('send-button');
            const restartButton = document.getElementById('restart-button');
            const debugToggle = document.getElementById('debug-toggle');
            const debugPanel = document.getElementById('debug-panel');
            const currentNodeSpan = document.getElementById('current-node');
            const nextNodeSpan = document.getElementById('next-node');
            const llmCallsSpan = document.getElementById('llm-calls');
            const llmCallsContainer = document.getElementById('llm-calls-container');
            const refreshDebugButton = document.getElementById('refresh-debug');
            const loadingIndicator = document.getElementById('loading-indicator');
            
            // Phone number selection elements
            const phoneNumberDropdown = document.getElementById('phone-number');
            const clearSessionButton = document.getElementById('clear-session');
            
            // Image upload elements
            const imageInput = document.getElementById('imageInput');
            const browseButton = document.getElementById('browse-button');
            const fileName = document.getElementById('fileName');
            const uploadButton = document.getElementById('upload-button');
            const userInfoPanel = document.getElementById('userInfoPanel');
            const userInfoDetails = document.getElementById('extractedInfo');
            
            // Disable UI until a phone number is selected
            function updateUIBasedOnPhoneSelection() {
                const isPhoneSelected = phoneNumberDropdown.value !== "";
                userInput.disabled = !isPhoneSelected;
                sendButton.disabled = !isPhoneSelected;
                browseButton.disabled = !isPhoneSelected;
                uploadButton.disabled = !isPhoneSelected || !imageInput.files[0];
                clearSessionButton.disabled = !isPhoneSelected;
                
                if (isPhoneSelected) {
                    userInput.placeholder = "Type your message here...";
                    // Clear messages when phone number changes
                    messagesContainer.innerHTML = '';
                    // Show loading indicator
                    loadingIndicator.style.display = 'block';
                    
                    // Check if there's an existing session for this phone number
                    fetchExistingSession(phoneNumberDropdown.value);
                } else {
                    userInput.placeholder = "Please select a phone number first";
                    // Clear messages when no phone number selected
                    messagesContainer.innerHTML = '<div class="system-message">Please select a phone number to start the conversation.</div>';
                }
            }
            
            // Fetch and display conversation history
            async function loadConversationHistory(phoneNumber) {
                try {
                    const response = await fetch(`/api/sessions/history/${phoneNumber}`);
                    
                    if (!response.ok) {
                        throw new Error('Failed to fetch conversation history');
                    }
                    
                    const data = await response.json();
                    
                    if (data.success && data.exists && data.conversation_history) {
                        // Clear existing messages
                        messagesContainer.innerHTML = '';
                        
                        // Display each message in the history if we have messages
                        if (data.conversation_history.length > 0) {
                            console.log(`Displaying ${data.conversation_history.length} messages from history`);
                            
                            // Display sequential messages in order
                            data.conversation_history.forEach((msg, index) => {
                                if (msg.content && msg.role) {
                                    displayMessage(msg.content, msg.role);
                                }
                            });
                            
                            // Update debug info if available
                            if (data.current_node) {
                                currentNodeSpan.textContent = data.current_node;
                            }
                            if (data.next_node) {
                                nextNodeSpan.textContent = data.next_node;
                            }
                            
                            // Add a system message indicating we loaded history
                            const systemMsg = document.createElement('div');
                            systemMsg.className = 'system-message';
                            systemMsg.innerHTML = `<p>Loaded existing conversation with ${data.conversation_history.length} messages for ${phoneNumber}</p>`;
                            messagesContainer.appendChild(systemMsg);
                            
                            // Scroll to bottom of chat
                            messagesContainer.scrollTop = messagesContainer.scrollHeight;
                            
                            return true; // Indicate we loaded history successfully
                        }
                    }
                    
                    console.log('No conversation history found or empty history');
                    return false; // No history to load
                } catch (error) {
                    console.error('Error loading conversation history:', error);
                    return false; // Failed to load history
                }
            }
            
            // Fetch existing session for a phone number
            async function fetchExistingSession(phoneNumber) {
                try {
                    // First try to load conversation history
                    const historyLoaded = await loadConversationHistory(phoneNumber);
                    
                    if (historyLoaded) {
                        // If history was loaded successfully, we're done
                        loadingIndicator.style.display = 'none';
                    } else {
                        // No existing history, start a new conversation
                        await startConversation();
                    }
                } catch (error) {
                    console.error('Error fetching session:', error);
                    // If there's an error, fall back to starting a new conversation
                    await startConversation();
                }
            }
            
            // Initial UI update
            updateUIBasedOnPhoneSelection();
            
            // Handle phone number selection change
            phoneNumberDropdown.addEventListener('change', updateUIBasedOnPhoneSelection);
            
            // Handle clear session button click
            clearSessionButton.addEventListener('click', async function() {
                const phoneNumber = phoneNumberDropdown.value;
                if (!phoneNumber) {
                    alert('Please select a phone number first');
                    return;
                }
                
                try {
                    const response = await fetch(`/api/sessions/clear/${phoneNumber}`, {
                        method: 'POST'
                    });
                    
                    if (response.ok) {
                        messagesContainer.innerHTML = '';
                        startConversation();
                        alert(`Session for ${phoneNumber} has been cleared.`);
                    } else {
                        const errorData = await response.json();
                        alert(`Failed to clear session: ${errorData.error || 'Unknown error'}`);
                    }
                } catch (error) {
                    console.error('Error clearing session:', error);
                    alert('An error occurred while clearing the session. Please try again.');
                }
            });
            
            // Start conversation on page load - now controlled by phone number selection
            // Only start if a phone number is selected
            if (phoneNumberDropdown.value) {
                startConversation();
            }
            
            // Event listeners
            sendButton.addEventListener('click', sendMessage);
            userInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });
            restartButton.addEventListener('click', function() {
                const phoneNumber = phoneNumberDropdown.value;
                if (!phoneNumber) {
                    alert('Please select a phone number first');
                    return;
                }
                
                if (confirm('Are you sure you want to restart the conversation? This will keep your conversation history but start from the beginning.')) {
                    startConversation(true); // Pass true to force restart
                }
            });
            debugToggle.addEventListener('click', function() {
                debugPanel.style.display = debugPanel.style.display === 'none' ? 'block' : 'none';
                if (debugPanel.style.display === 'block') {
                    refreshDebugData();
                }
            });
            refreshDebugButton.addEventListener('click', refreshDebugData);
            
            // Set up image upload
            browseButton.addEventListener('click', function() {
                imageInput.click();
            });
            
            imageInput.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    fileName.textContent = this.files[0].name;
                    uploadButton.disabled = false;
                } else {
                    fileName.textContent = 'No file selected';
                    uploadButton.disabled = true;
                }
            });
            
            // Handle image upload
            uploadButton.addEventListener('click', async function() {
                const fileInput = document.getElementById('imageInput');
                const phoneNumber = phoneNumberDropdown.value;
                
                // Check if file is selected and phone number is selected
                if (!fileInput.files[0]) {
                    alert('Please select an image file first');
                    return;
                }
                
                if (!phoneNumber) {
                    alert('Please select a phone number first');
                    return;
                }
                
                // Show loading indicator and disable button
                uploadButton.disabled = true;
                uploadButton.textContent = 'Uploading...';
                document.getElementById('uploadSpinner').style.display = 'inline-block';
                
                // Read the file as base64
                const reader = new FileReader();
                reader.onload = async function(e) {
                    const imageData = e.target.result;
                    
                    try {
                        // Upload image with phone number
                        const uploadResponse = await fetch('/api/image_upload', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                image_data: imageData,
                                phone_number: phoneNumber // Use phone number from dropdown
                            }),
                        });
                        
                        if (!uploadResponse.ok) {
                            throw new Error('Network response was not ok');
                        }
                        
                        const data = await uploadResponse.json();
                        
                        // Reset the input and button
                        fileInput.value = '';
                        fileName.textContent = 'No file selected';
                        uploadButton.disabled = true;
                        uploadButton.textContent = 'Upload';
                        document.getElementById('uploadSpinner').style.display = 'none';
                        
                        // Display extracted information if available
                        if (data.extracted_info && Object.values(data.extracted_info).some(val => val)) {
                            userInfoPanel.style.display = 'block';
                            
                            // Display formatted information in the user info panel
                            const infoPanel = document.getElementById('extractedInfo');
                            infoPanel.innerHTML = '';
                            
                            Object.entries(data.extracted_info).forEach(([key, value]) => {
                                if (value) {
                                    const formattedKey = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                                    infoPanel.innerHTML += `<div><strong>${formattedKey}:</strong> ${value}</div>`;
                                }
                            });
                        }
                        
                        // Add a user message to indicate upload
                        addMessage('user', 'I\'ve uploaded my national ID image.');
                        
                        // Add assistant response based on success
                        if (data.success) {
                            addMessage('assistant', data.message || 'Thanks for uploading your ID. I\'ve successfully extracted your information.');
                        } else {
                            // Handle case where extraction wasn't fully successful
                            let message = data.message || 'I couldn\'t fully extract your information from the image.';
                            
                            if (data.missing_fields && data.missing_fields.length > 0) {
                                addMessage('assistant', message + ' Please provide the following details:');
                                
                                // Create a form for missing fields
                                const formHtml = `
                                    <div class="missing-fields-form">
                                        <form id="missingFieldsForm">
                                            ${data.missing_fields.map(field => `
                                                <div class="form-group">
                                                    <label for="${field}">${field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:</label>
                                                    <input type="text" id="${field}" name="${field}" required>
                                                </div>
                                            `).join('')}
                                            <button type="submit">Submit</button>
                                        </form>
                                    </div>
                                `;
                                
                                // Add form to chat
                                const formDiv = document.createElement('div');
                                formDiv.innerHTML = formHtml;
                                document.querySelector('.message.assistant-message:last-child').appendChild(formDiv);
                                
                                // Handle form submission
                                document.getElementById('missingFieldsForm').addEventListener('submit', async function(e) {
                                    e.preventDefault();
                                    
                                    const formData = {};
                                    data.missing_fields.forEach(field => {
                                        formData[field] = document.getElementById(field).value;
                                    });
                                    
                                    // Show submitting state
                                    const submitButton = this.querySelector('button[type="submit"]');
                                    submitButton.disabled = true;
                                    submitButton.textContent = 'Submitting...';
                                    
                                    try {
                                        // Send the missing information with phone number
                                        const response = await fetch('/api/pipeline/provide-missing-info', {
                                            method: 'POST',
                                            headers: {
                                                'Content-Type': 'application/json',
                                            },
                                            body: JSON.stringify({
                                                fields: formData,
                                                phone_number: phoneNumber // Use phone number from dropdown
                                            }),
                                        });
                                        
                                        const result = await response.json();
                                        
                                        if (result.success) {
                                            // Add a confirmation message
                                            addMessage('user', 'I\'ve provided the missing information.');
                                            addMessage('assistant', result.message || 'Thank you for providing the additional information.');
                                            
                                            // Update the user info panel
                                            if (result.updated_info) {
                                                userInfoPanel.style.display = 'block';
                                                const infoPanel = document.getElementById('extractedInfo');
                                                
                                                // Update each field in the panel
                                                Object.entries(result.updated_info).forEach(([key, value]) => {
                                                    if (value) {
                                                        const formattedKey = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                                                        
                                                        // Check if this field already exists in the panel
                                                        const existingField = infoPanel.querySelector(`[data-field="${key}"]`);
                                                        if (existingField) {
                                                            existingField.innerHTML = `<strong>${formattedKey}:</strong> ${value}`;
                                                        } else {
                                                            infoPanel.innerHTML += `<div data-field="${key}"><strong>${formattedKey}:</strong> ${value}</div>`;
                                                        }
                                                    }
                                                });
                                            }
                                            
                                            // Remove the form
                                            this.closest('.missing-fields-form').remove();
                                        } else {
                                            addMessage('assistant', 'There was an issue saving your information: ' + (result.message || 'Please try again.'));
                                        }
                                    } catch (error) {
                                        console.error('Error:', error);
                                        addMessage('assistant', 'Sorry, there was an error submitting your information. Please try again.');
                                    } finally {
                                        // Reset button state
                                        submitButton.disabled = false;
                                        submitButton.textContent = 'Submit';
                                    }
                                });
                            } else {
                                addMessage('assistant', message);
                            }
                        }
                    } catch (error) {
                        console.error('Error:', error);
                        
                        // Reset UI elements
                        uploadButton.disabled = false;
                        uploadButton.textContent = 'Upload';
                        document.getElementById('uploadSpinner').style.display = 'none';
                        
                        // Show error message
                        addMessage('assistant', 'Sorry, there was an error processing your image. Please try again or continue the conversation.');
                    }
                };
                
                reader.readAsDataURL(fileInput.files[0]);
            });
            
            // Start or restart conversation
            async function startConversation(forceRestart = false) {
                const phoneNumber = phoneNumberDropdown.value;
                if (!phoneNumber) {
                    return; // Don't start conversation without a phone number
                }
                
                // Clear messages
                messagesContainer.innerHTML = '';
                loadingIndicator.style.display = 'block';
                
                try {
                    // Include phone number in the payload
                    const payload = {
                        from: phoneNumber, // Use the selected phone number
                        action: "fetch", // Indicate we want to fetch the existing session, not restart
                        force_restart: forceRestart // Set to true when restarting
                    };
                    
                    const response = await fetch('/api/pipeline/start', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(payload),
                    });
                    
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    
                    const data = await response.json();
                    
                    // Display bot's welcome message
                    if (data.message) {
                        displayMessage(data.message, 'assistant');
                        
                        // If restarting, add a system message
                        if (forceRestart) {
                            const systemMsg = document.createElement('div');
                            systemMsg.className = 'system-message';
                            systemMsg.innerHTML = '<p>Conversation has been restarted</p>';
                            messagesContainer.appendChild(systemMsg);
                        }
                        
                        // If there's history, show a message
                        if (data.history_count && data.history_count > 0) {
                            console.log(`Session has ${data.history_count} messages in history`);
                        }
                    }
                    
                    // Update debug info if available
                    if (data.current_node) {
                        currentNodeSpan.textContent = data.current_node;
                    }
                    if (data.next_node) {
                        nextNodeSpan.textContent = data.next_node;
                    }
                    
                    refreshDebugData();
                    
                } catch (error) {
                    console.error('Error starting conversation:', error);
                    messagesContainer.innerHTML += `
                        <div class="message assistant-message">
                            <p>Sorry, there was an error starting the conversation. Please try again.</p>
                        </div>
                    `;
                } finally {
                    loadingIndicator.style.display = 'none';
                }
            }
            
            // Send user message to the server
            async function sendMessage() {
                const message = userInput.value.trim();
                const phoneNumber = phoneNumberDropdown.value;
                
                if (!message || !phoneNumber) return;
                
                // Clear input
                userInput.value = '';
                
                // Display user message
                displayMessage(message, 'user');
                
                // Show loading indicator
                loadingIndicator.style.display = 'block';
                
                try {
                    // Create whatsapp-like payload with the selected phone number
                    const payload = {
                        messages: {
                            from: phoneNumber,
                            id: "msg_" + Date.now(),
                            timestamp: new Date().toISOString(),
                            type: "text",
                            text: {
                                body: message
                            }
                        },
                        contacts: {
                            profile: {
                                name: "Web User"
                            }
                        }
                    };
                    
                    const response = await fetch('/api/pipeline/continue', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(payload),
                    });
                    
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    
                    const data = await response.json();
                    
                    // Display bot's response
                    if (data.message) {
                        displayMessage(data.message, 'assistant');
                    }
                    
                    // Update debug info if available
                    if (data.current_node) {
                        currentNodeSpan.textContent = data.current_node;
                    }
                    if (data.next_node) {
                        nextNodeSpan.textContent = data.next_node;
                    }
                    
                    refreshDebugData();
                    
                } catch (error) {
                    console.error('Error sending message:', error);
                    messagesContainer.innerHTML += `
                        <div class="message assistant-message">
                            <p>Sorry, there was an error processing your message. Please try again.</p>
                        </div>
                    `;
                } finally {
                    loadingIndicator.style.display = 'none';
                }
            }
            
            // Display a message in the chat window
            function displayMessage(content, role) {
                const messagesDiv = document.getElementById('messages');
                
                // Create message wrapper with avatar
                const messageWrapper = document.createElement('div');
                messageWrapper.classList.add('message-wrapper');
                
                if (role === 'user') {
                    messageWrapper.classList.add('user-message-wrapper');
                }
                
                // Create avatar
                const avatar = document.createElement('div');
                avatar.classList.add('avatar');
                avatar.classList.add(role === 'user' ? 'user-avatar' : 'assistant-avatar');
                avatar.textContent = role === 'user' ? 'U' : 'A';
                
                // Create message div
                const messageDiv = document.createElement('div');
                messageDiv.classList.add('message');
                messageDiv.classList.add(role + '-message');
                
                // Convert markdown to HTML (if it's the assistant message)
                const messageContent = document.createElement('div');
                messageContent.classList.add('markdown-content');
                
                if (role === 'assistant') {
                    messageContent.innerHTML = MarkdownRenderer.render(content);
                } else {
                    messageContent.textContent = content;
                }
                
                messageDiv.appendChild(messageContent);
                
                // Add avatar and message to wrapper
                messageWrapper.appendChild(avatar);
                messageWrapper.appendChild(messageDiv);
                
                // Add wrapper to messages container
                messagesDiv.appendChild(messageWrapper);
                
                // Scroll to bottom
                messagesDiv.scrollTop = messagesDiv.scrollHeight;
            }
            
            function refreshDebugData() {
                fetch('/api/debug/llm-calls')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        llmCallsSpan.textContent = data.total_calls;
                        updateLLMCallsDisplay(data.llm_calls);
                    } else {
                        console.error('Error fetching debug data:', data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            }
            
            function updateLLMCallsDisplay(llmCalls) {
                // Clear existing content
                llmCallsContainer.innerHTML = '';
                
                // Sort calls by timestamp, newest first
                llmCalls.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
                
                // Create elements for each LLM call
                llmCalls.forEach((call, index) => {
                    const callDiv = document.createElement('div');
                    callDiv.classList.add('llm-call');
                    if (call.node_type === 'action') {
                        callDiv.classList.add('action-node');
                    } else if (call.node_type === 'decision') {
                        callDiv.classList.add('decision-node');
                    }
                    
                    // Create header
                    const headerDiv = document.createElement('div');
                    headerDiv.classList.add('llm-call-header');
                    
                    // Add node info and badge
                    const nodeTypeSpan = document.createElement('span');
                    nodeTypeSpan.classList.add('badge');
                    nodeTypeSpan.classList.add(call.node_type === 'decision' ? 'badge-decision' : 'badge-action');
                    nodeTypeSpan.textContent = call.node_type;
                    
                    const headerText = document.createElement('span');
                    headerText.textContent = `#${llmCalls.length - index}: ${call.node_name}`;
                    
                    const timestampSpan = document.createElement('span');
                    timestampSpan.textContent = call.timestamp;
                    
                    headerDiv.appendChild(nodeTypeSpan);
                    headerDiv.appendChild(headerText);
                    headerDiv.appendChild(timestampSpan);
                    callDiv.appendChild(headerDiv);
                    
                    // Create collapsible for details
                    const toggleButton = document.createElement('button');
                    toggleButton.classList.add('collapsible');
                    toggleButton.textContent = 'Show Details';
                    callDiv.appendChild(toggleButton);
                    
                    const contentDiv = document.createElement('div');
                    contentDiv.classList.add('content');
                    
                    // Node info
                    const nodeInfo = document.createElement('div');
                    nodeInfo.classList.add('node-info');
                    nodeInfo.textContent = `Node ID: ${call.node_id}`;
                    contentDiv.appendChild(nodeInfo);
                    
                    // Prompt container
                    const promptContainer = document.createElement('div');
                    promptContainer.classList.add('prompt-container');
                    
                    const promptLabel = document.createElement('div');
                    promptLabel.textContent = 'Prompt:';
                    promptContainer.appendChild(promptLabel);
                    
                    const promptContent = document.createElement('div');
                    promptContent.classList.add('prompt');
                    promptContent.textContent = call.prompt;
                    promptContainer.appendChild(promptContent);
                    
                    contentDiv.appendChild(promptContainer);
                    
                    // User input if present
                    if (call.user_input) {
                        const userInputContainer = document.createElement('div');
                        userInputContainer.classList.add('prompt-container');
                        
                        const userInputLabel = document.createElement('div');
                        userInputLabel.textContent = 'User Input:';
                        userInputContainer.appendChild(userInputLabel);
                        
                        const userInputContent = document.createElement('div');
                        userInputContent.classList.add('prompt');
                        userInputContent.textContent = call.user_input;
                        userInputContainer.appendChild(userInputContent);
                        
                        contentDiv.appendChild(userInputContainer);
                    }
                    
                    // Response container
                    const responseContainer = document.createElement('div');
                    responseContainer.classList.add('response-container');
                    
                    const responseLabel = document.createElement('div');
                    responseLabel.textContent = 'Response:';
                    responseContainer.appendChild(responseLabel);
                    
                    const responseContent = document.createElement('div');
                    responseContent.classList.add('response');
                    responseContent.textContent = call.response;
                    responseContainer.appendChild(responseContent);
                    
                    contentDiv.appendChild(responseContainer);
                    
                    // Performance info
                    const perfInfo = document.createElement('div');
                    perfInfo.classList.add('performance');
                    
                    const tokensSpan = document.createElement('span');
                    tokensSpan.textContent = `Tokens: ${call.tokens_used}`;
                    
                    const durationSpan = document.createElement('span');
                    durationSpan.textContent = `Duration: ${call.duration_ms.toFixed(2)}ms`;
                    
                    perfInfo.appendChild(tokensSpan);
                    perfInfo.appendChild(durationSpan);
                    
                    contentDiv.appendChild(perfInfo);
                    
                    callDiv.appendChild(contentDiv);
                    llmCallsContainer.appendChild(callDiv);
                    
                    // Set up collapsible behavior
                    toggleButton.addEventListener('click', function() {
                        this.classList.toggle('active');
                        const content = this.nextElementSibling;
                        if (content.style.display === 'block') {
                            content.style.display = 'none';
                            this.textContent = 'Show Details';
                        } else {
                            content.style.display = 'block';
                            this.textContent = 'Hide Details';
                        }
                    });
                });
                
                // If no calls yet
                if (llmCalls.length === 0) {
                    const noCallsDiv = document.createElement('div');
                    noCallsDiv.textContent = 'No LLM calls recorded yet.';
                    llmCallsContainer.appendChild(noCallsDiv);
                }
            }
        });
    </script>
</body>
</html> 