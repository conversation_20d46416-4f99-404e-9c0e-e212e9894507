package whatsapp

import (
	"fmt"
	shared "libs/shared"

	"strconv"
	"net/http"
	"io"
    "os"
    "path/filepath"
	"github.com/labstack/echo/v4"
	helpers "libs/shared/utils/helpers"
)

type Handler interface {
}

type handler struct {
	service Service
	flowSevice        FlowsService
	webhook Webhook
}

var newHandlerObj *handler //singleton object

// singleton function
func NewHandler() *handler {
	if newHandlerObj != nil {
		return newHandlerObj
	}

	newHandlerObj = &handler{
		service:     NewService(),
		flowSevice: NewFlowsService(),
		webhook: NewWebhook(),
	}
	return newHandlerObj
}


func (h *handler) DataChannelUri(c echo.Context) error {
    fmt.Println("-------DataChannelUri handler-----------")
	var requestBody interface{}
	c.Bind(&requestBody)
	helpers.PrettyPrint("data channel requestBody", requestBody)

	encryptedData, err := h.flowSevice.DataChannelService(requestBody)
	if err != nil {
		fmt.Println("DataChannelUri Error : ", err)
	}

	return c.String(200, encryptedData)
}

func (h *handler) Webhook(c echo.Context) error {

	var requestBody interface{}
	c.Bind(&requestBody)
	fmt.Println("WEBHOOK REQUEST BODY::========================", requestBody)
	go h.AsyncWebhook(requestBody)

	return c.String(200, "OK")
}

func (h *handler) AsyncWebhook(requestBody interface{}) error {

	if requestBody == nil {
		fmt.Println("Webhook Error : nil requestBody")
		return nil
	}	

	
	h.service.SaveWebhookData(requestBody)

	var data WebhookRequest
	err := helpers.JsonMarshaller(requestBody, &data)
	fmt.Println("webhoook REQUEST data::", data)
	if err != nil {
		fmt.Println("Webhook Error : ", err)
		return nil
	}

	err = h.service.Webhook(data)
	if err != nil {
		fmt.Println("Webhook Error : ", err)
		return nil
	}

	return nil
}

func (h *handler) VerifyWebhook(c echo.Context) error {

	var data VerifyWebhookRequest
	fmt.Println("context", c)
	err := c.Bind(&data)
	if err != nil {
		return c.JSON(http.StatusBadGateway, 0)
	}

	fmt.Println("data params", data)

	err = h.service.VerifyWebhook(data)
	if err != nil {
		return c.JSON(http.StatusBadGateway, 0)
	}

	return c.JSON(http.StatusOK, data.Challenge)
}

func (h *handler) InitialWhatsappMessage(c echo.Context) error {
	
	var requestBody interface{}
	c.Bind(&requestBody)
	helpers.PrettyPrint("Intial Message requestBody", requestBody)
	var data MessageRequest
	err := helpers.JsonMarshaller(requestBody, &data)
	err = h.webhook.IntialMessage(data)
	if err != nil {
		return c.JSON(http.StatusBadGateway, 0)
	}

	return nil
}

func (h *handler) VerifySpiroWebhook(c echo.Context) error {
	fmt.Println("-------VerifySpiroWebhook handler-----------")
    // Get the challenge parameter from query string
    challenge := c.QueryParam("challange")
    
    if challenge == "" {
        return c.String(http.StatusBadRequest, "Challenge parameter is missing")
    }

    // Return the challenge value
    return c.String(http.StatusOK, challenge)
}

func (h *handler) InitialWhatsappTemplate(c echo.Context) error {
    // Get the uploaded file
    file, err := c.FormFile("data")
    if err != nil {
        return c.JSON(http.StatusBadRequest, map[string]string{
            "error": "No file uploaded",
        })
    }
    
    // Open the uploaded file
    src, err := file.Open()
    if err != nil {
        return c.JSON(http.StatusInternalServerError, map[string]string{
            "error": "Failed to open uploaded file",
        })
    }
    defer src.Close()

    // Create a temporary file path with absolute path
    currentDir, err := os.Getwd()
    if err != nil {
        return c.JSON(http.StatusInternalServerError, map[string]string{
            "error": "Failed to get current directory",
        })
    }
    
    tempDir := filepath.Join(currentDir, "temp")
    if err := os.MkdirAll(tempDir, 0755); err != nil {
        return c.JSON(http.StatusInternalServerError, map[string]string{
            "error": "Failed to create temp directory",
        })
    }
    
    tempFilePath := filepath.Join(tempDir, file.Filename)
    fmt.Println("tempFilePath", tempFilePath)

    // Create the destination file
    dst, err := os.Create(tempFilePath)
    if err != nil {
        return c.JSON(http.StatusInternalServerError, map[string]string{
            "error": "Failed to create temporary file",
        })
    }
    defer dst.Close()

    // Copy the uploaded file content
    if _, err = io.Copy(dst, src); err != nil {
        os.Remove(tempFilePath) // Clean up if copy fails
        return c.JSON(http.StatusInternalServerError, map[string]string{
            "error": "Failed to save file",
        })
    }

    whatsapp_account_number := c.Param("whatsapp_account_number")
    data := IntialTemplateRequest{
        FilePath: tempFilePath,
        WhatsappAccountNumber: whatsapp_account_number,
    }

    // Close file handles before processing
    dst.Close()
    src.Close()

    err = h.service.IntialTemplate(data)
    
    // Ensure file is removed even if there's an error
    defer os.RemoveAll(tempFilePath)
    
    if err != nil {
        return c.JSON(http.StatusBadGateway, map[string]string{
            "error": err.Error(),
        })
    }

    return c.JSON(http.StatusOK, map[string]string{
        "message": "File processed successfully",
    })
}

func (h *handler) GetMessages(c echo.Context) error {

	var requestBody interface{}
	c.Bind(&requestBody)
	helpers.PrettyPrint(" Messages requestBody", requestBody)
	var data MessageRequest
	helpers.JsonMarshaller(requestBody, &data)
	response,err := h.service.GetMessages(data)
	if err != nil {
		return c.JSON(http.StatusBadGateway, 0)
	}
	var apiResp = []MessageResponses{}
	shared.JsonMarshaller(response, &apiResp)

	return shared.RespSuccess(c, "Data Retrieved Successfully", apiResp)
}


func (h *handler) ListMessages(c echo.Context) error {
	metaData := shared.ApiMetaData{}
	shared.JsonMarshaller(c.Get("metaData"), &metaData)
	whatsapp_account_number :=c.Param("whatsapp_account_number")
	filterQuery := map[string]interface{}{
		"whatsapp_account_number":whatsapp_account_number,
	}
	sortings := map[string]interface{}{
		"created_at": -1,
		// "updated_at": -1,
	}
	perPage := 10
	pageNo := 1

    if per_page := c.QueryParam("per_page"); per_page != "" {
        perPage, _ = strconv.Atoi(per_page)
     
        if perPage > 50 {
            return c.JSON(http.StatusBadRequest, map[string]string{
                "error": "Maximum allowed per_page value is 50",
            })
        }
    }
	if page_no := c.QueryParam("page_no"); page_no != "" {
		pageNo, _ = strconv.Atoi(page_no)
	}
	response,err := h.service.ListMessages(metaData, filterQuery, sortings, perPage, pageNo)
	if err != nil {
		return c.JSON(http.StatusBadGateway, 0)
	}
	var finalResp = []interface{}{}
	shared.JsonMarshaller(response["data"], &finalResp)

	return shared.RespSuccessWithPagination(c, "List retrieved successfully",  finalResp, response["pagination"])
	
}

func (h *handler) NationalIDRecords(c echo.Context) error {
	metaData := shared.ApiMetaData{}
	shared.JsonMarshaller(c.Get("metaData"), &metaData)
	whatsapp_account_number :=c.Param("whatsapp_account_number")
	filterQuery := map[string]interface{}{
		"whatsapp_account_number":whatsapp_account_number,
	}
	sortings := map[string]interface{}{
		"created_at": -1,
	}
	perPage := -1
	pageNo := 1

    if per_page := c.QueryParam("per_page"); per_page != "" {
        perPage, _ = strconv.Atoi(per_page)
     
        if perPage > 50 {
            return c.JSON(http.StatusBadRequest, map[string]string{
                "error": "Maximum allowed per_page value is 50",
            })
        }
    }
	if page_no := c.QueryParam("page_no"); page_no != "" {
		pageNo, _ = strconv.Atoi(page_no)
	}
	response,err := h.service.ListNationalIds(metaData, filterQuery, sortings, perPage, pageNo)
	if err != nil {
		return c.JSON(http.StatusBadGateway, err)
	}
	var finalResp = []interface{}{}
	shared.JsonMarshaller(response["data"], &finalResp)

	return shared.RespSuccessWithPagination(c, "List retrieved successfully",  finalResp, response["pagination"])
	
}