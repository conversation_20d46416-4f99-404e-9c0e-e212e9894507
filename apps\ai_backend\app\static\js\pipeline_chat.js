// Add this at the top of the file with other variables
let showUpload = false;
let phoneNumber = null; // Variable to store the phone number

// Update the sendMessage function to handle the show_upload flag
async function sendMessage() {
    const messageInput = document.getElementById('message-input');
    const message = messageInput.value.trim();
    
    if (!message) return;
    
    // Add user message to chat
    addMessageToChat('user', message);
    messageInput.value = '';
    
    try {
        // Check if we need to set phone number from the message
        if (!phoneNumber && message.match(/^\+?[0-9]{10,15}$/)) {
            phoneNumber = message;
            console.log('Set phone number:', phoneNumber);
        }
        
        const response = await fetch('/api/pipeline/continue', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ 
                message,
                from: phoneNumber // Include phone number if available
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            // Add assistant message to chat
            addMessageToChat('assistant', data.message);
            
            // Update show_upload flag based on pipeline response
            showUpload = data.show_upload || false;
            updateUploadVisibility();
            
            // If there are missing fields, show the form
            if (data.missing_fields && data.missing_fields.length > 0) {
                showMissingFieldsForm(data.missing_fields, data.extracted_info);
            }
            
            // Update phone number if it was set in the response
            if (data.whatsapp_from) {
                phoneNumber = data.whatsapp_from;
            }
        } else {
            addMessageToChat('assistant', 'Sorry, I encountered an error. Please try again.');
        }
    } catch (error) {
        console.error('Error:', error);
        addMessageToChat('assistant', 'Sorry, I encountered an error. Please try again.');
    }
}

// Add function to update upload visibility
function updateUploadVisibility() {
    const uploadContainer = document.getElementById('upload-container');
    if (uploadContainer) {
        uploadContainer.style.display = 'block'; // Always show the upload container
    }
}

// Add function to get phone number
function getPhoneNumber() {
    if (phoneNumber) return phoneNumber;
    
    // If we don't have a phone number yet, prompt the user
    const inputPhoneNumber = prompt('Please enter your phone number (required for image processing):', '');
    if (inputPhoneNumber && inputPhoneNumber.match(/^\+?[0-9]{10,15}$/)) {
        phoneNumber = inputPhoneNumber;
        return phoneNumber;
    } else if (inputPhoneNumber) {
        alert('Please enter a valid phone number (10-15 digits)');
    }
    
    return null;
}

// Update the image upload handling
async function handleImageUpload(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    // Get phone number
    const currentPhoneNumber = getPhoneNumber();
    if (!currentPhoneNumber) {
        alert('A valid phone number is required to process your image.');
        return;
    }
    
    // Show loading state
    const uploadButton = document.getElementById('upload-button');
    if (uploadButton) {
        uploadButton.disabled = true;
        uploadButton.textContent = 'Uploading...';
    }
    
    try {
        // Read file as data URL
        const reader = new FileReader();
        reader.onload = async (e) => {
            const imageData = e.target.result; // This is a data URL
            
            // Send JSON data with image and phone number
            const response = await fetch('/api/image_upload', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    image_data: imageData,
                    phone_number: currentPhoneNumber
                })
            });
            
            const data = await response.json();
            
            // Add message to chat about the upload attempt
            addMessageToChat('user', 'I\'ve uploaded my national ID image.');
            
            if (data.success) {
                // Add success message to chat
                addMessageToChat('assistant', data.message);
                
                // If we have extracted info, show it
                if (data.extracted_info) {
                    // Display in user info panel
                    const userInfoDetails = document.getElementById('user-info-details');
                    if (userInfoDetails) {
                        userInfoDetails.innerHTML = formatExtractedInfo(data.extracted_info);
                        const userInfoPanel = document.getElementById('user-info-panel');
                        if (userInfoPanel) {
                            userInfoPanel.style.display = 'block';
                        }
                    }
                }
            } else {
                // Add message about missing fields
                addMessageToChat('assistant', data.message);
                
                // Show the form for missing fields if any
                if (data.missing_fields && data.missing_fields.length > 0) {
                    showMissingFieldsForm(data.missing_fields, data.extracted_info);
                }
            }
        };
        
        reader.readAsDataURL(file);
    } catch (error) {
        console.error('Error:', error);
        addMessageToChat('assistant', 'Sorry, I encountered an error uploading the image. Please try again or provide your information manually.');
    } finally {
        // Reset upload button state
        if (uploadButton) {
            uploadButton.disabled = false;
            uploadButton.textContent = 'Upload';
        }
    }
}

// Helper function to format extracted information
function formatExtractedInfo(info) {
    let html = '';
    for (const [key, value] of Object.entries(info)) {
        if (value) {
            const formattedKey = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
            html += `<div><strong>${formattedKey}:</strong> ${value}</div>`;
        }
    }
    return html || 'No information available yet.';
}

// Add function to show missing fields form
function showMissingFieldsForm(missingFields, extractedInfo) {
    const formHtml = `
        <div class="missing-fields-form">
            <h3>Please provide the following ${missingFields.length > 0 ? 'missing information' : 'information'}:</h3>
            <form id="missing-fields-form">
                ${missingFields.map(field => `
                    <div class="form-group">
                        <label for="${field}">${field.charAt(0).toUpperCase() + field.slice(1).replace('_', ' ')}:</label>
                        <input type="text" id="${field}" name="${field}" value="${extractedInfo && extractedInfo[field] ? extractedInfo[field] : ''}" required>
                    </div>
                `).join('')}
                <button type="submit">Submit</button>
            </form>
        </div>
    `;
    
    // Add form to chat
    addMessageToChat('assistant', formHtml);
    
    // Add form submit handler
    document.getElementById('missing-fields-form').addEventListener('submit', async (e) => {
        e.preventDefault();
        const formData = new FormData(e.target);
        const missingInfo = {};
        formData.forEach((value, key) => {
            missingInfo[key] = value;
        });
        
        try {
            // Show submitting state
            const submitButton = e.target.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.textContent = 'Submitting...';
                submitButton.disabled = true;
            }
            
            const response = await fetch('/api/pipeline/provide-missing-info', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ 
                    fields: missingInfo,
                    phone_number: phoneNumber // Include phone number
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                // Add success message to chat
                addMessageToChat('assistant', 'Thank you for providing the information. ' + data.message);
                
                // Handle the next message in the conversation if there is one
                if (data.pipeline_response && data.pipeline_response.message) {
                    addMessageToChat('assistant', data.pipeline_response.message);
                }
            } else {
                addMessageToChat('assistant', 'There was an issue saving your information: ' + data.message);
            }
        } catch (error) {
            console.error('Error:', error);
            addMessageToChat('assistant', 'Sorry, I encountered an error. Please try again.');
        } finally {
            // Reset button state (in case the form is still visible)
            const submitButton = document.querySelector('#missing-fields-form button[type="submit"]');
            if (submitButton) {
                submitButton.textContent = 'Submit';
                submitButton.disabled = false;
            }
        }
    });
}

// Add function to add messages to chat
function addMessageToChat(role, content) {
    // Get the messages container
    const messagesContainer = document.getElementById('messages');
    if (!messagesContainer) return;
    
    // Create message elements using the same structure as in pipeline_chat.html
    const messageWrapper = document.createElement('div');
    messageWrapper.classList.add('message-wrapper');
    if (role === 'user') {
        messageWrapper.classList.add('user-message-wrapper');
    }
    
    // Create avatar
    const avatar = document.createElement('div');
    avatar.classList.add('avatar');
    avatar.classList.add(role + '-avatar');
    avatar.textContent = role === 'user' ? 'U' : 'A';
    
    // Create message div
    const messageDiv = document.createElement('div');
    messageDiv.classList.add('message');
    messageDiv.classList.add(role + '-message');
    
    // Create content div for markdown
    const contentDiv = document.createElement('div');
    contentDiv.classList.add('markdown-content');
    
    // Render markdown for assistant messages only
    if (role === 'assistant') {
        // Check if MarkdownRenderer is available
        if (typeof MarkdownRenderer !== 'undefined') {
            contentDiv.innerHTML = MarkdownRenderer.render(content);
            
            // Add click listeners to any code blocks for selecting text
            setTimeout(() => {
                const codeBlocks = contentDiv.querySelectorAll('pre');
                codeBlocks.forEach(block => {
                    block.addEventListener('click', function() {
                        const range = document.createRange();
                        range.selectNode(this);
                        window.getSelection().removeAllRanges();
                        window.getSelection().addRange(range);
                    });
                });
            }, 100);
        } else {
            contentDiv.innerHTML = content;
        }
    } else {
        // User messages are plain text
        contentDiv.textContent = content;
    }
    
    // Assemble the message
    messageDiv.appendChild(contentDiv);
    messageWrapper.appendChild(avatar);
    messageWrapper.appendChild(messageDiv);
    messagesContainer.appendChild(messageWrapper);
    
    // Scroll to bottom
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// Add this to your HTML file
document.addEventListener('DOMContentLoaded', function() {
    // Initialize upload visibility
    updateUploadVisibility();
    
    // Add event listener for file input
    const fileInput = document.getElementById('image-input');
    if (fileInput) {
        fileInput.addEventListener('change', handleImageUpload);
    }
}); 