package whatsapp

import (
	env "apps/spiro/config"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rsa"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"errors"
	"fmt"
	shared "libs/shared"
	"os"
	"strings"

	// "github.com/youmark/pkcs8"
 "crypto/x509"
	// "code_develop_go_0.0/backend_core/controllers/whatsapp/whatsapp/buyer"
	// "code_develop_go_0.0/backend_core/controllers/whatsapp/whatsapp/flows"
	flows "apps/spiro/controllers/whatsapp/flows"
	// "code_develop_go_0.0/backend_core/controllers/whatsapp/whatsapp/seller"
	users "apps/spiro/controllers/whatsapp/users"
	whatsapp "libs/shared/db_connectors/model"

	// whatsapp_repo "code_develop_go_0.0/backend_core/internal/repository/whatsapp"

	helpers "libs/shared/utils/helpers"
	whatsapp_repository "libs/shared/db_connectors/repository/whatsapp"
)

type FlowsService interface {
	DataChannelService(requestBody interface{}) (string, error)
}

type flowsService struct {
	// buyerFlowsService             buyer.BuyerFlowsService
	 sellerFlowsService            users.UserFlowsService
	 dataChannelRepository         whatsapp_repository.WhatsappRepository[whatsapp.WhatsappDataChannel]
	 accountRepository whatsapp_repository.WhatsappRepository[whatsapp.WhatsappAccount]
	// accountRepository             whatsapp_repo.WhatsappAccount
	// contactRepository             whatsapp_repo.WhatsappContact
	// whatsappDataChannelRepository whatsapp_repo.WhatsappDataChannel
	contact_Repository             whatsapp_repository.WhatsappContact
	db shared.PostgresRepositoryFunctions
}

var flowsServiceObj *flowsService

func NewFlowsService() *flowsService {
	if flowsServiceObj != nil {
		return flowsServiceObj
	}
	flowsServiceObj = &flowsService{
		// buyerFlowsService:             buyer.NewBuyerFlowsService(),
		sellerFlowsService:            	  users.NewUserFlowsService(),
		dataChannelRepository:            whatsapp_repository.NewRepository(whatsapp.WhatsappDataChannel{}),
		accountRepository:                whatsapp_repository.NewRepository(whatsapp.WhatsappAccount{}),
		// accountRepository:             whatsapp_repo.NewWhatsappAccountRepository(),
		// contactRepository:             whatsapp_repo.NewWhatsappContactRepository(),
		// whatsappDataChannelRepository: whatsapp_repo.NewWhatsappDataChannelRepository(),

		contact_Repository:             whatsapp_repository.NewWhatsappContactRepository(),
	}
	return flowsServiceObj
}

func (s *flowsService) DataChannelService(requestBody interface{}) (string, error) {
	fmt.Println("-------DataChannelService-----------")

	whatsappDataChannel := whatsapp.WhatsappDataChannel{}
	whatsappDataChannel.Request, _ = json.Marshal(requestBody)


	var data flows.DataChannelRequest
	helpers.JsonMarshaller(requestBody, &data)

	data, decryptedData, err := s.DataChannelDecryptv2(data)
	if err != nil {
		fmt.Println("Error in decryptedData------")
		s.dataChannelRepository.Save(env.GlobalEnv["POSTGRES_CREDENTIAL"],&whatsappDataChannel)
		return "", err
	}

	helpers.PrettyPrint("DataChannelRequest", decryptedData)

	if decryptedData.Action == "ping" {
		fmt.Println("Action is Ping-----")
		dataChannelResponse := flows.DataChannelResponse{
			Version: decryptedData.Version,
			Data: map[string]interface{}{
				"status": "active",
			},
		}
		return s.DataChannelEncryptv2(data, dataChannelResponse)
	}

	whatsappDataChannel.DecryptedRequest, _ = json.Marshal(decryptedData)

	flowToken := strings.Split(decryptedData.FlowToken, ":")
	if len(flowToken) != 3 && len(flowToken) != 4 {
		fmt.Println("FLOW TOKEN : ", len(flowToken))
		s.dataChannelRepository.Save(env.GlobalEnv["POSTGRES_CREDENTIAL"],&whatsappDataChannel)
		return "", errors.New("invalid flowtoken")
	}

	account, err := s.accountRepository.FindOne(env.GlobalEnv["POSTGRES_CREDENTIAL"], map[string]interface{}{
		"whatsapp_number": flowToken[0],
	})
	if err != nil {
		s.dataChannelRepository.Save(env.GlobalEnv["POSTGRES_CREDENTIAL"],&whatsappDataChannel)
		return "", err
	}

	contact, err := s.contact_Repository.FindOne(env.GlobalEnv["POSTGRES_CREDENTIAL"],map[string]interface{}{
		"whatsapp_number":     flowToken[1],
		"whatsapp_account_id": account.ID,
	})

	if err != nil {
		s.dataChannelRepository.Save(env.GlobalEnv["POSTGRES_CREDENTIAL"],&whatsappDataChannel)
		return "", err
	}

	fmt.Println("Account", account.WhatsappNumber)
	fmt.Println("RasaFlow.Code", account.RasaFlow.Code)


	var dataChannelResponse flows.DataChannelResponse
	if account.RasaFlow.Code == "LEAD_FLOW" {
		fmt.Println("----account----", account)

		dataChannelResponse, err = s.sellerFlowsService.UserScreen(account, contact, decryptedData)
		if err != nil {
			s.dataChannelRepository.Save(env.GlobalEnv["POSTGRES_CREDENTIAL"],&whatsappDataChannel)
			return "", err
		}
	}

	whatsappDataChannel.Response, _ = json.Marshal(dataChannelResponse)

	encryptedData, err := s.DataChannelEncryptv2(data, dataChannelResponse)
	if err != nil {
		s.dataChannelRepository.Save(env.GlobalEnv["POSTGRES_CREDENTIAL"],&whatsappDataChannel)
		return "", err
	}

	whatsappDataChannel.EncryptedResponse, _ = json.Marshal(encryptedData)

	s.dataChannelRepository.Save(env.GlobalEnv["POSTGRES_CREDENTIAL"],&whatsappDataChannel)

	helpers.PrettyPrint("DataChannelResponse", dataChannelResponse)

	return encryptedData, nil
}

func (s *flowsService) DataChannelDecryptv2(data flows.DataChannelRequest) (flows.DataChannelRequest, flows.DataChannelDecrypted, error) {

	var dataChannelDecrypted flows.DataChannelDecrypted

	flowData, err := base64.StdEncoding.DecodeString(data.EncryptedFlowData)
	if err != nil {
		return data, dataChannelDecrypted, err
	}

	initialVector, err := base64.StdEncoding.DecodeString(data.InitialVector)
	if err != nil {
		return data, dataChannelDecrypted, err
	}

	encryptedAesKey, err := base64.StdEncoding.DecodeString(data.EncryptedAesKey)
	if err != nil {
		return data, dataChannelDecrypted, err
	}

	privateKey, err := s.LoadPrivateKey()
	if err != nil {
		return data, dataChannelDecrypted, err
	}

	aesKey, err := rsa.DecryptOAEP(sha256.New(), nil, privateKey, encryptedAesKey, nil)
	if err != nil {
		return data, dataChannelDecrypted, err
	}

	block, err := aes.NewCipher(aesKey)
	if err != nil {
		return data, dataChannelDecrypted, err
	}

	encryptedFlowDataBody := flowData[:len(flowData)-16]
	encryptedFlowDataTag := flowData[len(flowData)-16:]

	aesGCM, err := cipher.NewGCMWithNonceSize(block, 16)
	if err != nil {
		return data, dataChannelDecrypted, err
	}

	ciphertext := append(encryptedFlowDataBody, encryptedFlowDataTag...)

	decryptedData, err := aesGCM.Open(nil, initialVector, ciphertext, nil)
	if err != nil {
		return data, dataChannelDecrypted, err
	}

	json.Unmarshal(decryptedData, &dataChannelDecrypted)

	// if dataChannelDecrypted.Version != "3.0" {
	// 	return data, dataChannelDecrypted, errors.New("decryption error")
	// }

	data.AESKey = aesKey
	data.IV = initialVector

	return data, dataChannelDecrypted, nil
}

func (s *flowsService) DataChannelEncryptv2(data flows.DataChannelRequest, dataChannelResponse flows.DataChannelResponse) (string, error) {

	responseJSON, err := json.Marshal(dataChannelResponse)
	if err != nil {
		return "", err
	}

	flippedIV := make([]byte, len(data.IV))
	for i, b := range data.IV {
		flippedIV[i] = b ^ 0xFF
	}

	block, err := aes.NewCipher(data.AESKey)
	if err != nil {
		return "", err
	}

	aesGCM, err := cipher.NewGCMWithNonceSize(block, 16)
	if err != nil {
		return "", err
	}

	encryptedData := aesGCM.Seal(nil, flippedIV, responseJSON, nil)

	encryptedDataString := base64.StdEncoding.EncodeToString(encryptedData)

	return encryptedDataString, nil
}

func (s *flowsService) LoadPrivateKey() (*rsa.PrivateKey, error) {
    currentDir, err := os.Getwd()
    if err != nil {
        return nil, errors.New("Error getting current directory: " + err.Error())
    }
    fmt.Println("Current Directory:", currentDir)

    keyPath := currentDir + "/controllers/whatsapp/private.pem"
    fmt.Println("Key Path:", keyPath)
    
    pemBytes, err := os.ReadFile(keyPath)
    if err != nil {
        alternatePath := "private.pem"
        pemBytes, err = os.ReadFile(alternatePath)
        if err != nil {
            return nil, errors.New("Error reading PEM file from both paths: " + err.Error())
        }
    }

    block, rest := pem.Decode(pemBytes)
    if len(rest) > 0 {
        return nil, fmt.Errorf("ERROR : pem.Decode")
    }

    // Handle encrypted PEM with PKCS#5 v2.0
    if block.Type == "RSA PRIVATE KEY" && block.Headers["Proc-Type"] == "4,ENCRYPTED" {
        password := []byte("9999")
        decryptedKey, err := x509.DecryptPEMBlock(block, password)
        if err != nil {
            return nil, fmt.Errorf("failed to decrypt private key: %v", err)
        }
        
        privateKey, err := x509.ParsePKCS1PrivateKey(decryptedKey)
        if err != nil {
            return nil, fmt.Errorf("failed to parse decrypted private key: %v", err)
        }
        
        return privateKey, nil
    }

    return nil, fmt.Errorf("unsupported key format")
}
