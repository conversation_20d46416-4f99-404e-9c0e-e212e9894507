package whatsapp

import (
	// env "spiro/config"
	// "fmt"
	// shared "libs/shared"

	"github.com/labstack/echo/v4"
		whatsapp_contacts  "apps/spiro/controllers/whatsapp/whatsapp_contacts"
)

func (h *handler) Route(g *echo.Group) {
	// =========================== API Routes ===================================
    whatsapp_contacts.NewHandler().Route(g.Group("/whatsapp_contacts"))

	// ============================== Sample Route =============================

	g.POST("/data_channel_uri", h.DataChannelUri)
	g.POST("/webhook", h.Webhook)
	// g.GET("/webhook", h.VerifyWebhook)
	g.GET("/webhook", h.VerifySpiroWebhook)

	g.POST("/initial_message",h.InitialWhatsappMessage)
	g.POST("/initial_template/:whatsapp_account_number",h.InitialWhatsappTemplate)

	g.POST("/get_messages", h.GetMessages)
	g.GET("/list_messages/:whatsapp_account_number",h.ListMessages)
	g.GET("/national_id/:whatsapp_account_number",h.NationalIDRecords)

}
