package whatsapp_contacts


import (
	// "fmt"
	env "apps/spiro/config"

	shared "libs/shared"
	model "libs/shared/db_connectors/model"
	pagination "libs/shared/utils/pagination"
	// repository "libs/shared/db_connectors/repository"
	whatsapp "libs/shared/db_connectors/model"
	whatsapp_repository "libs/shared/db_connectors/repository/whatsapp"
)

type Service interface {
	CreateWhatsappContact(data CreatePayloadDto, metaData shared.ApiMetaData) (interface{}, error)
	UpdateWhatsappContact(data UpdatePayloadDto, metaData shared.ApiMetaData, query map[string]interface{}) (interface{}, error)
	GetWhatsappContact(metaData shared.ApiMetaData, query map[string]interface{}) (interface{}, error)
	ListWhatsappContact(metaData shared.ApiMetaData, query map[string]interface{}, sortings interface{}, perPage int, pageNo int) (map[string]interface{}, error)
	DeleteWhatsappContact(metaData shared.ApiMetaData, query map[string]interface{}) (interface{}, error)
}

type service struct {
	db shared.PostgresRepositoryFunctions
	whatsappContactRepository whatsapp_repository.WhatsappRepository[whatsapp.WhatsappContact]
	contact_Repository             whatsapp_repository.WhatsappContact
}

var newServiceObj *service //singleton object

// singleton function
func NewService() *service {
	if newServiceObj != nil {
		return newServiceObj
	}
	// new_db := shared.PostgresRepository()
	// newServiceObj = &service{new_db}
	newServiceObj = &service{

		db:               shared.PostgresRepository(),
		whatsappContactRepository: whatsapp_repository.NewRepository(whatsapp.WhatsappContact{}),
		contact_Repository:             whatsapp_repository.NewWhatsappContactRepository(),
	}
	return newServiceObj
}

// ------------------Product Brand--------------------------------------------------------------------------------------------------
func (s *service) CreateWhatsappContact(data CreatePayloadDto, metaData shared.ApiMetaData) (interface{}, error) {

	// collectionName := shared.PostgresCollectionNames["LOOKUP_TYPE"]
	var create_payload = model.WhatsappContact{}
	shared.JsonMarshaller(data, &create_payload)

	// resp, err := s.db.CreateOne(env.GlobalEnv["POSTGRES_CREDENTIAL"], collectionName, create_payload)
	err:=s.whatsappContactRepository.Save(env.GlobalEnv["POSTGRES_CREDENTIAL"],&create_payload)

	// fmt.Println("resp", resp)
	if err != nil {
		return nil, err
	}

	return create_payload,  nil
}
func (s *service) UpdateWhatsappContact(data UpdatePayloadDto, metaData shared.ApiMetaData, query map[string]interface{}) (interface{}, error) {

	// shared.PrettyPrint("data", data)
	// shared.PrettyPrint("metaData", metaData)

	//Any other db functions or doing any functionality means do it here .....

	// collectionName := shared.PostgresCollectionNames["LOOKUP_TYPE"]
	var update_payload = model.WhatsappContact{}
	shared.JsonMarshaller(data, &update_payload)
	// resp, err := s.db.UpdateOne(env.GlobalEnv["POSTGRES_CREDENTIAL"], collectionName, update_payload, query)
	err:=s.whatsappContactRepository.Update(env.GlobalEnv["POSTGRES_CREDENTIAL"],query,&update_payload)
	if err != nil {
		return nil, err
	}
	return update_payload, nil
}
func (s *service) GetWhatsappContact(metaData shared.ApiMetaData, query map[string]interface{}) (interface{}, error) {

	// shared.PrettyPrint("data", data)
	// shared.PrettyPrint("metaData", metaData)

	//Any other db functions or doing any functionality means do it here .....

	// collectionName := shared.PostgresCollectionNames["LOOKUP_TYPE"]

	// resp, err :=s.whatsappContactRepository.FindOne(env.GlobalEnv["POSTGRES_CREDENTIAL"],query)

	resp, err := s.contact_Repository.FindOne(env.GlobalEnv["POSTGRES_CREDENTIAL"], query)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (s *service) ListWhatsappContact(metaData shared.ApiMetaData, query map[string]interface{}, sortings interface{}, perPage int, pageNo int) (map[string]interface{}, error) {

	// shared.PrettyPrint("data", data)
	// shared.PrettyPrint("metaData", metaData)

	//Any other db functions or doing any functionality means do it here .....

	// collectionName := shared.PostgresCollectionNames["LOOKUP_TYPE"]
	resp, err := s.whatsappContactRepository.FindAllWithPagination(env.GlobalEnv["POSTGRES_CREDENTIAL"], query, &pagination.Paginatevalue{
        Page_no:  pageNo,
        Per_page: perPage,
    })
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (s *service) DeleteWhatsappContact(metaData shared.ApiMetaData, query map[string]interface{}) (interface{}, error) {

	// shared.PrettyPrint("data", data)
	// shared.PrettyPrint("metaData", metaData)

	//Any other db functions or doing any functionality means do it here .....

	collectionName := shared.PostgresCollectionNames["LOOKUP_TYPE"]
	resp, err := s.db.DeleteOne(env.GlobalEnv["POSTGRES_CREDENTIAL"], collectionName, query)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
