package whatsapp_contacts

import (
	shared "libs/shared"

	validation "github.com/go-ozzo/ozzo-validation"
	"github.com/labstack/echo/v4"
)



func SampleCreateValidation(next echo.HandlerFunc) echo.HandlerFunc {

	var data = new(CreatePayloadDto)
	return func(c echo.Context) error {
		err := c.Bind(data)
		if err != nil {
			validation_err := shared.BindErrorStructure(err)
			return shared.RespValidationFailure(c, "Invalid Fields or Parameter Found", validation_err)
		}


		// validation rules here
		err = validation.ValidateStruct(data)

		if err != nil {
			validation_err := shared.ValidationErrorStructure(err)
			if validation_err != nil {
				return shared.RespValidationFailure(c, "Invalid Fields or Parameter Found", validation_err)
			}
		}

		// if data.LookupCode != "TESTING" {
		// 	return shared.RespValidationFailure(c, "Lookup code Mismatch", "Lookup code Mismatch if should be equal to TESTING")
		// }

		c.Set("sampleCreate", *data)
		return next(c)
	}
}
func SampleUpdateValidation(next echo.HandlerFunc) echo.HandlerFunc {

	var data = new(UpdatePayloadDto)
	return func(c echo.Context) error {
		err := c.Bind(data)
		if err != nil {
			validation_err := shared.BindErrorStructure(err)
			return shared.RespValidationFailure(c, "Invalid Fields or Parameter Found", validation_err)
		}


		// validation rules here
		err = validation.ValidateStruct(data)

		if err != nil {
			validation_err := shared.ValidationErrorStructure(err)
			if validation_err != nil {
				return shared.RespValidationFailure(c, "Invalid Fields or Parameter Found", validation_err)
			}
		}

		// if data.LookupCode != "TESTING" {
		// 	return shared.RespValidationFailure(c, "Lookup code Mismatch", "Lookup code Mismatch if should be equal to TESTING")
		// }

		c.Set("sampleUpdate", *data)
		return next(c)
	}
}
