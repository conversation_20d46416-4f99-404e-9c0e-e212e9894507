import logging
import json
import os
import re
from typing import Dict, Any, List, Optional, Tuple
from enum import Enum
from pydantic import BaseModel, Field
from openai import OpenAI

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# Initialize OpenAI client with Gemini API
try:
    client = OpenAI(
        api_key=os.environ.get("GEMINI_API_KEY", "your_gemini_api_key_here"),
        base_url="https://generativelanguage.googleapis.com/v1beta/openai/"
    )
    logger.info("OpenAI client initialized with Gemini API")
except Exception as e:
    logger.error(f"Error initializing OpenAI client: {str(e)}")
    client = None

class StateAction(str, Enum):
    """Enum for state update actions"""
    UPDATE = "update"
    DELETE = "delete"
    NONE = "none"

class SalesInformation(BaseModel):
    """Model for storing user sales information"""
    user_profile: Optional[str] = None  # University student/Salaried/Boda Boda rider/Fleet operator
    owns_petrol_bike: Optional[str] = None  # Yes/No
    ownership_years: Optional[str] = None  # < 3 years / > 3 years
    selected_model: Optional[str] = None  # Model A/B/C
    needs_loan: Optional[str] = None  # Yes/No
    has_existing_loan: Optional[str] = None  # Yes/No
    selected_financing: Optional[str] = None  # KCB/Mogo/Watu/Mkopa
    documents_uploaded: Optional[bool] = None  # True when user uploads
    experience_rating: Optional[int] = None  # 1-5 rating

    def get_current_step(self) -> str:
        """Determine current step in sales process"""
        if not self.user_profile:
            return "profile_selection"
        elif not self.owns_petrol_bike:
            return "petrol_bike_inquiry"
        elif self.owns_petrol_bike == "Yes" and not self.ownership_years:
            return "ownership_duration"
        elif not self.selected_model:
            return "model_selection"
        elif not self.needs_loan:
            return "loan_inquiry"
        elif self.needs_loan == "Yes" and not self.has_existing_loan:
            return "existing_loan_check"
        elif self.needs_loan == "Yes" and not self.selected_financing:
            return "financing_options"
        elif self.needs_loan == "Yes" and not self.documents_uploaded:
            return "document_collection"
        elif not self.experience_rating:
            return "experience_rating"
        else:
            return "completed"

    def is_complete(self) -> bool:
        """Check if all required fields are filled"""
        return self.get_current_step() == "completed"

    def get_missing_fields(self) -> List[str]:
        """Get a list of missing required fields"""
        missing = []
        if self.user_profile is None:
            missing.append("user_profile")
        if self.owns_petrol_bike is None:
            missing.append("owns_petrol_bike")
        if self.owns_petrol_bike == "Yes" and self.ownership_years is None:
            missing.append("ownership_years")
        if self.selected_model is None:
            missing.append("selected_model")
        if self.needs_loan is None:
            missing.append("needs_loan")
        if self.needs_loan == "Yes" and self.has_existing_loan is None:
            missing.append("has_existing_loan")
        if self.needs_loan == "Yes" and self.selected_financing is None:
            missing.append("selected_financing")
        if self.needs_loan == "Yes" and self.documents_uploaded is None:
            missing.append("documents_uploaded")
        if self.experience_rating is None:
            missing.append("experience_rating")
        return missing

    def get_collected_fields(self) -> List[str]:
        """Get a list of collected fields"""
        collected = []
        if self.user_profile is not None:
            collected.append("user_profile")
        if self.owns_petrol_bike is not None:
            collected.append("owns_petrol_bike")
        if self.ownership_years is not None:
            collected.append("ownership_years")
        if self.selected_model is not None:
            collected.append("selected_model")
        if self.needs_loan is not None:
            collected.append("needs_loan")
        if self.has_existing_loan is not None:
            collected.append("has_existing_loan")
        if self.selected_financing is not None:
            collected.append("selected_financing")
        if self.documents_uploaded is not None:
            collected.append("documents_uploaded")
        if self.experience_rating is not None:
            collected.append("experience_rating")
        return collected

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary, excluding None values"""
        return {k: v for k, v in self.model_dump().items() if v is not None}

class StateUpdate(BaseModel):
    """Model for state updates"""
    key: str
    value: Any
    action: StateAction = StateAction.UPDATE

class AgentState(BaseModel):
    """Model for agent state"""
    data: Dict[str, Any] = {}
    sales_info: SalesInformation = Field(default_factory=SalesInformation)

    def update(self, updates: List[StateUpdate]) -> None:
        """Update state based on state updates"""
        for update in updates:
            if update.action == StateAction.UPDATE:
                # Check if this is a sales information field
                if update.key in SalesInformation.model_fields:
                    setattr(self.sales_info, update.key, update.value)
                    # Also save the value in data for backward compatibility
                    self.data[update.key] = update.value
                else:
                    self.data[update.key] = update.value
            elif update.action == StateAction.DELETE:
                if update.key in SalesInformation.model_fields:
                    setattr(self.sales_info, update.key, None)
                    # Also remove from data if present
                    if update.key in self.data:
                        del self.data[update.key]
                elif update.key in self.data:
                    del self.data[update.key]

    def get(self, key: str, default: Any = None) -> Any:
        """Get a value from state or sales_info"""
        if key in SalesInformation.model_fields:
            value = getattr(self.sales_info, key)
            return value if value is not None else default
        return self.data.get(key, default)

    def set(self, key: str, value: Any) -> None:
        """Set a value in state or sales_info"""
        if key in SalesInformation.model_fields:
            setattr(self.sales_info, key, value)
            # Also save in data for backward compatibility
            self.data[key] = value
        else:
            self.data[key] = value

    def delete(self, key: str) -> None:
        """Delete a key from state or sales_info"""
        if key in SalesInformation.model_fields:
            setattr(self.sales_info, key, None)
            # Also remove from data if present
            if key in self.data:
                del self.data[key]
        elif key in self.data:
            del self.data[key]

    def to_dict(self) -> Dict[str, Any]:
        """Convert state to dictionary including sales info"""
        result = self.data.copy()
        sales_info_dict = self.sales_info.to_dict()
        # Add sales_info to result but also keep the top-level fields for backward compatibility
        result["sales_info"] = sales_info_dict
        for key, value in sales_info_dict.items():
            result[key] = value
        return result

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AgentState':
        """Create an AgentState instance from a dictionary"""
        sales_info_data = data.pop("sales_info", {}) if "sales_info" in data else {}

        # Create a new instance
        instance = cls(data=data)

        # Handle sales_info if present in the data
        if sales_info_data:
            instance.sales_info = SalesInformation(**sales_info_data)
        else:
            # Extract sales_info fields from the main data
            sales_info_fields = {}
            for field in SalesInformation.model_fields:
                if field in data:
                    sales_info_fields[field] = data[field]

            if sales_info_fields:
                instance.sales_info = SalesInformation(**sales_info_fields)

        return instance

# Define function tools for the single agent

# Tool 1: Sales Flow Progression (Primary)
sales_flow_tool = {
    "type": "function",
    "function": {
        "name": "progress_sales_flow",
        "description": "Main tool to progress through Spiro EV sales script. Always use this to advance the conversation through the sales process.",
        "parameters": {
            "type": "object",
            "properties": {
                "current_step": {
                    "type": "string",
                    "description": "Current step in sales process (profile_selection, petrol_bike_inquiry, ownership_duration, model_selection, loan_inquiry, existing_loan_check, financing_options, document_collection, experience_rating, completed)"
                },
                "user_response": {
                    "type": "string",
                    "description": "User's response to process"
                },
                "extracted_info": {
                    "type": "object",
                    "description": "Information extracted from user response",
                    "properties": {
                        "user_profile": {"type": "string"},
                        "owns_petrol_bike": {"type": "string"},
                        "ownership_years": {"type": "string"},
                        "selected_model": {"type": "string"},
                        "needs_loan": {"type": "string"},
                        "has_existing_loan": {"type": "string"},
                        "selected_financing": {"type": "string"},
                        "documents_uploaded": {"type": "boolean"},
                        "experience_rating": {"type": "integer"}
                    }
                },
                "next_message": {
                    "type": "string",
                    "description": "Next message to send to user to continue sales flow"
                },
                "should_progress": {
                    "type": "boolean",
                    "description": "Whether to advance to next step in sales process"
                }
            },
            "required": ["current_step", "next_message"]
        }
    }
}

# Tool 2: Knowledge Base Q&A (Secondary)
knowledge_qa_tool = {
    "type": "function",
    "function": {
        "name": "answer_from_knowledge",
        "description": "Use this tool ONLY when user asks questions that require knowledge base search about Spiro EV products, financing, or company information.",
        "parameters": {
            "type": "object",
            "properties": {
                "question": {
                    "type": "string",
                    "description": "User's question that needs knowledge base search"
                },
                "answer": {
                    "type": "string",
                    "description": "Answer based on knowledge base search"
                },
                "return_to_flow": {
                    "type": "boolean",
                    "description": "Always true - return to main sales flow after answering"
                }
            },
            "required": ["question", "answer", "return_to_flow"]
        }
    }
}

class AgentResponse(BaseModel):
    """Model for agent responses"""
    message: str
    state_updates: List[StateUpdate] = []
    should_progress: bool = False
    message_type: str = "flow"  # "flow" or "knowledge"
    flow_name: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert response to dictionary"""
        return {
            "message": self.message,
            "state_updates": [update.model_dump() for update in self.state_updates],
            "should_progress": self.should_progress,
            "message_type": self.message_type,
            "flow_name": self.flow_name
        }

# System prompt for the single agent
SPIRO_SALES_AGENT_PROMPT = """
You are Sales Ninja from Spiro, a single agent with TWO main functions:

PRIMARY FUNCTION (Main Goal): Progress through the Spiro EV sales script using the 'progress_sales_flow' tool
SECONDARY FUNCTION: Answer user questions using 'answer_from_knowledge' tool when knowledge base search is needed

SALES SCRIPT SEQUENCE (Your main goal):
1. Profile Selection: "Hi! I'm Sales Ninja from Spiro. Let me help you choose most suitable Spiro electric vehicle model and available financing options. Please select what fits most with your current profile?"
   Options: University student / Salaried / Boda Boda rider / Fleet operator

2. Petrol Bike Ownership: "Do you currently own a petrol bike?"
   Options: Yes/No

3. Ownership Duration (if Yes): "How many years have you owned it?"
   Options: < 3 years / > 3 years

4. Model Selection: "Awesome. Based on your profile, below are the most suitable Spiro EV products for you. Which one would you choose?"
   Options: Model A / Model B / Model C

5. Loan Inquiry: "Great choice! The price of [Selected Model] is KES [XX,XXX]. Would you require loan assistance?"
   Options: Yes/No

6. Existing Loan Check (if Yes): "Before sharing financing options may I know do you have any existing loan?"
   Options: Yes/No

7. Financing Options: Present KCB, Mogo, Watu, Mkopa options with details

8. Financing Selection: "Which loan option do you prefer?"

9. Document Collection: Request National ID, KRA pin, KCB account (if applicable)

10. Experience Rating: "Please rate your experience so we can improve"

TOOL USAGE STRATEGY:
1. ALWAYS try to use 'progress_sales_flow' tool first - this is your primary goal
2. ONLY use 'answer_from_knowledge' tool when:
   - User asks specific questions about Spiro products, features, pricing
   - User asks about financing details, loan terms, requirements
   - User asks about company information, policies, support
   - The question requires searching knowledge base information

3. After using 'answer_from_knowledge' tool, IMMEDIATELY return to 'progress_sales_flow' tool

DECISION LOGIC:
- If user provides information for current sales step → Use 'progress_sales_flow'
- If user asks question needing knowledge base → Use 'answer_from_knowledge' then return to flow
- If user gives general response → Use 'progress_sales_flow' to guide them
- ALWAYS prioritize advancing the sales script

CRITICAL: Your main mission is to complete the sales script. Questions are secondary.

RESPONSE REQUIREMENTS:
- Generate dynamic, conversational responses
- NEVER use hardcoded messages
- Be friendly and professional
- Use appropriate emoji (1-2 maximum)
- Always end with a question to encourage engagement
"""

class SpiroSalesAgent:
    """Single agent that handles both sales flow progression and knowledge Q&A"""

    def __init__(self):
        self.name = "SpiroSalesAgent"
        self.tools = [sales_flow_tool, knowledge_qa_tool]

    def process(self, user_input: str, state: AgentState) -> AgentResponse:
        """Process user input using appropriate tool based on context"""

        if not client:
            # Fallback if LLM client is not available
            fallback_message = "I'm having trouble connecting to our system. Please try again in a moment."
            logger.info("Using fallback message (no LLM client)")
            return AgentResponse(message=fallback_message, message_type="flow")

        try:
            # Get current sales information
            current_step = state.sales_info.get_current_step()
            collected_fields = state.sales_info.get_collected_fields()
            missing_fields = state.sales_info.get_missing_fields()

            # Build system prompt with current context
            system_prompt = f"""
            {SPIRO_SALES_AGENT_PROMPT}

            CURRENT CONTEXT:
            - Current Step: {current_step}
            - Collected Info: {state.sales_info.to_dict()}
            - Missing Fields: {missing_fields}
            - User Input: "{user_input}"

            INSTRUCTIONS:
            1. Analyze if user input progresses the sales flow OR asks a question
            2. Choose appropriate tool:
               - 'progress_sales_flow' for advancing sales script (PRIMARY)
               - 'answer_from_knowledge' for questions needing knowledge base (SECONDARY)
            3. Generate dynamic, conversational responses
            4. NEVER use hardcoded messages
            5. Extract information from user input when relevant
            6. Always provide next appropriate message for the sales flow

            You MUST call one of your tools to respond.
            """

            # Call LLM with function calling
            response = client.chat.completions.create(
                model="gemini-2.5-flash-preview-05-20",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_input}
                ],
                tools=self.tools,
                tool_choice="auto"
            )

            # Process function calls and return response
            return self.process_function_calls(response, state)

        except Exception as e:
            logger.error(f"Error processing message: {str(e)}")
            fallback_message = "I apologize, but I'm experiencing some technical difficulties. Let me help you with your Spiro EV inquiry. Could you please tell me what you're looking for?"
            return AgentResponse(message=fallback_message, message_type="flow", flow_name=current_step)

    def process_function_calls(self, response, state: AgentState) -> AgentResponse:
        """Process function calls from LLM response"""
        try:
            if hasattr(response, 'choices') and len(response.choices) > 0:
                choice = response.choices[0]

                if hasattr(choice, 'message') and hasattr(choice.message, 'tool_calls') and choice.message.tool_calls:
                    tool_call = choice.message.tool_calls[0]
                    function_name = tool_call.function.name
                    function_args = json.loads(tool_call.function.arguments)

                    logger.info(f"Function called: {function_name} with args: {function_args}")

                    if function_name == "progress_sales_flow":
                        return self.handle_progress_sales_flow(function_args, state)
                    elif function_name == "answer_from_knowledge":
                        return self.handle_answer_from_knowledge(function_args, state)
                    else:
                        logger.warning(f"Unknown function called: {function_name}")
                        return AgentResponse(
                            message="I'm not sure how to help with that. Let me continue with your Spiro EV inquiry.",
                            message_type="flow"
                        )
                else:
                    # No function call, use the message content
                    message_content = choice.message.content if hasattr(choice.message, 'content') else "How can I help you with Spiro EV today?"
                    return AgentResponse(message=message_content, message_type="flow")
            else:
                logger.warning("No valid response from LLM")
                return AgentResponse(
                    message="I'm here to help you with Spiro EV. What would you like to know?",
                    message_type="flow"
                )

        except Exception as e:
            logger.error(f"Error processing function calls: {str(e)}")
            return AgentResponse(
                message="Let me help you with your Spiro EV inquiry. What are you interested in?",
                message_type="flow"
            )

    def handle_progress_sales_flow(self, function_args: Dict[str, Any], state: AgentState) -> AgentResponse:
        """Handle sales flow progression"""
        try:
            current_step = function_args.get("current_step", "")
            user_response = function_args.get("user_response", "")
            extracted_info = function_args.get("extracted_info", {})
            next_message = function_args.get("next_message", "")
            should_progress = function_args.get("should_progress", False)

            state_updates = []

            # Update sales information if extracted
            if extracted_info:
                for key, value in extracted_info.items():
                    if value is not None and key in SalesInformation.model_fields:
                        state_updates.append(StateUpdate(
                            key=key,
                            value=value,
                            action=StateAction.UPDATE
                        ))
                        logger.info(f"Updating {key} = {value}")

            # Update current step if progressing
            if should_progress:
                state_updates.append(StateUpdate(
                    key="current_step",
                    value=current_step,
                    action=StateAction.UPDATE
                ))

            return AgentResponse(
                message=next_message,
                state_updates=state_updates,
                should_progress=should_progress,
                message_type="flow",
                flow_name=current_step
            )

        except Exception as e:
            logger.error(f"Error handling progress_sales_flow: {str(e)}")
            return AgentResponse(
                message="Let me continue helping you with your Spiro EV inquiry. What would you like to know?",
                message_type="flow"
            )

    def handle_answer_from_knowledge(self, function_args: Dict[str, Any], state: AgentState) -> AgentResponse:
        """Handle knowledge base Q&A"""
        try:
            question = function_args.get("question", "")
            answer = function_args.get("answer", "")
            return_to_flow = function_args.get("return_to_flow", True)

            # For now, placeholder implementation
            # Later will integrate with actual knowledge base
            logger.info(f"Knowledge Q&A - Question: {question}, Answer: {answer}")

            # Add a note to return to the main flow
            if return_to_flow:
                current_step = state.sales_info.get_current_step()
                if current_step != "completed":
                    answer += f"\n\nNow, let's continue with your Spiro EV selection process. "

            return AgentResponse(
                message=answer,
                state_updates=[],
                message_type="knowledge",
                should_progress=False
            )

        except Exception as e:
            logger.error(f"Error handling answer_from_knowledge: {str(e)}")
            return AgentResponse(
                message="I'd be happy to help with your question. Could you please rephrase it?",
                message_type="knowledge"
            )

class AgentManager:
    """Manages the single Spiro sales agent"""

    def __init__(self):
        self.agent = SpiroSalesAgent()

    def process_message(self, user_input: str, state: AgentState) -> Tuple[str, List[StateUpdate], bool]:
        """Process user message and return response"""
        try:
            # Process the message with the agent
            response = self.agent.process(user_input, state)

            # Apply state updates
            if response.state_updates:
                state.update(response.state_updates)

            # Return message, state updates, and progress flag
            return response.message, response.state_updates, response.should_progress

        except Exception as e:
            logger.error(f"Error in AgentManager.process_message: {str(e)}")
            fallback_message = "I'm here to help you with Spiro EV. How can I assist you today?"
            return fallback_message, [], False