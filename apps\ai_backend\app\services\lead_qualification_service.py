import os
import logging
import json
from typing import Dict, Any, List, Optional, Tuple
from pydantic import BaseModel
import requests
from openai import OpenAI
from app.services.session_service import SessionManager
from app.services.pipeline_service import SalePipeline
from app.models.lead_qualification import LeadQualificationResult, Base
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
import re

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# Initialize Gemini client with OpenAI format
gemini_client = OpenAI(
    api_key="AIzaSyDtRxzdQ1RLZNH2KSMtsNWP8ZKyIrtDBUo",
    base_url="https://generativelanguage.googleapis.com/v1beta/openai/"
)

# Set the model name for Gemini
GEMINI_MODEL = "gemini-2.0-flash"
LLM_TEMPERATURE = float(os.environ.get('LLM_TEMPERATURE', '0.7'))

# Set up database
DATABASE_URL = os.environ.get('DATABASE_URL', 'sqlite:///persistent_sessions.db')
engine = create_engine(DATABASE_URL)
Base.metadata.create_all(engine)  # Create tables if they don't exist
db_session = scoped_session(sessionmaker(autocommit=False, autoflush=False, bind=engine))

class LeadCategory(str):
    """Lead classification categories"""
    HOT = "Hot Lead"
    WARM = "Warm Lead"
    COLD = "Cold Lead"

class LeadScores(BaseModel):
    """Scores for each lead qualification dimension"""
    primary_use: int = 0  # 0-10
    model_selection: int = 0  # 0-10
    purchase_timeline: int = 0  # 0-10
    budget_range: int = 0  # 0-10
    interest_level: int = 0  # 0-10
    total_score: int = 0  # Sum of all scores

class LeadDetails(BaseModel):
    """Details extracted from conversation about lead qualification"""
    primary_use: Optional[str] = None  # a/b/c
    model_selection: Optional[str] = None  # a/b
    purchase_timeline: Optional[str] = None  # a/b/c
    budget_range: Optional[str] = None  # a/b/c
    interest_level: Optional[str] = None  # a/b/c/d
    selected_model: Optional[str] = None  # Specific model if mentioned
    selected_showroom: Optional[str] = None  # Specific showroom if mentioned
    confidence: float = 0.0  # 0.0-1.0 confidence in extraction

class LeadQualification(BaseModel):
    """Complete lead qualification with details, scores and classification"""
    lead_details: LeadDetails
    lead_scores: LeadScores
    category: str  # Hot/Warm/Cold
    rationale: str  # Explanation of classification
    recommendation: str  # Recommended next steps

def call_llm_api(prompt: str, system_prompt: str = None, temperature: float = None) -> str:
    """Call Gemini API using OpenAI format with the given prompt and return the response"""
    try:
        # Use configured values if not specified
        if temperature is None:
            temperature = LLM_TEMPERATURE
        
        # Create messages format for OpenAI-compatible API
        messages = []
        
        # Add system prompt if provided
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        
        # Add user prompt
        messages.append({"role": "user", "content": prompt})
        
        # Make the API call using the Gemini client
        response = gemini_client.chat.completions.create(
            model=GEMINI_MODEL,
            messages=messages,
            temperature=temperature
        )
        
        # Extract and return the response text
        if response.choices and len(response.choices) > 0:
            return response.choices[0].message.content
        else:
            logger.error("No response content received from Gemini API")
            return "Error: No response content received"
    
    except Exception as e:
        logger.error(f"Error calling Gemini API: {str(e)}", exc_info=True)
        return f"Error: {str(e)}"

def clean_json_string(text):
    """Extract valid JSON from potentially malformed responses"""
    # First try to find JSON within triple backticks
    import re
    json_pattern = r"```(?:json)?\s*([\s\S]*?)\s*```"
    match = re.search(json_pattern, text)
    
    if match:
        # Found JSON inside triple backticks, try to parse it
        try:
            json_text = match.group(1).strip()
            return json.loads(json_text)
        except json.JSONDecodeError as e:
            logger.warning(f"Found JSON in triple backticks but failed to parse: {e}")
    
    # If no valid JSON found in backticks, try direct parsing
    try:
        return json.loads(text)
    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse response as JSON: {e}")
        # Try to clean up the text and parse again
        cleaned_text = text.replace('```json', '').replace('```', '').strip()
        try:
            return json.loads(cleaned_text)
        except json.JSONDecodeError:
            logger.error("Failed to parse cleaned response as JSON")
            return {}

def extract_lead_details(conversation_history: List[Dict[str, str]], user_context: Any) -> LeadDetails:
    """
    Extract lead qualification details from conversation history
    
    Args:
        conversation_history: List of conversation messages
        user_context: User context with additional information
        
    Returns:
        LeadDetails with extracted information
    """
    # Convert conversation history to text format for the LLM
    conversation_text = ""
    for msg in conversation_history:
        role = msg.get("role", "unknown")
        content = msg.get("content", "")
        conversation_text += f"{role.upper()}: {content}\n\n"
    
    # Extract relevant user information from context
    user_info = ""
    if user_context:
        if hasattr(user_context, 'selected_model') and user_context.selected_model:
            user_info += f"Selected Model: {user_context.selected_model}\n"
        
        if hasattr(user_context, 'selected_showroom') and user_context.selected_showroom:
            user_info += f"Selected Showroom: {user_context.selected_showroom}\n"
        
        if hasattr(user_context, 'first_name') and user_context.first_name:
            name = f"{user_context.first_name} {user_context.last_name or ''}".strip()
            user_info += f"Customer Name: {name}\n"
            
        if hasattr(user_context, 'wants_emi') and user_context.wants_emi is not None:
            user_info += f"Interested in EMI: {'Yes' if user_context.wants_emi else 'No'}\n"
            
        if hasattr(user_context, 'scheduled_time') and user_context.scheduled_time:
            user_info += f"Scheduled Time: {user_context.scheduled_time}\n"
    
    # Create system prompt for extraction agent
    system_prompt = """
    You are an expert lead qualification agent for Spiro electric bikes. 
    Your task is to analyze the conversation history and extract key information about lead qualification.
    You must objectively analyze the conversation and extract ONLY what's directly mentioned or clearly implied.
    
    The required lead qualification dimensions are:
    
    1. Primary use of the bike:
       a) Daily Commuting
       b) Delivery Service (e.g. food, groceries, e-commerce)
       c) Just Exploring, not sure yet
    
    2. Model selection status:
       a) Yes, interested in a specific model
       b) No, needs guidance on choosing
    
    3. Purchase timeline:
       a) Within a week
       b) Within a month
       c) Not yet sure/just gathering information
    
    4. Budget range:
       a) Within INR 80000
       b) Between INR ************
       c) Above 100000
    
    5. Interest in more details:
       a) Test Ride
       b) EMI/Finance Option
       c) Product/Warranty Details
       d) No, just exploring options
    
    For each dimension, identify the BEST MATCHING option based on the conversation. 
    If a dimension is not mentioned at all, leave it as "Unknown".
    If the specific model name is mentioned, extract it as well.
    
    YOU MUST RESPOND IN VALID JSON FORMAT with the following structure:
    {
      "primary_use": "a/b/c or Unknown",
      "model_selection": "a/b or Unknown",
      "purchase_timeline": "a/b/c or Unknown",
      "budget_range": "a/b/c or Unknown",
      "interest_level": "a/b/c/d or Unknown",
      "selected_model": "model name if mentioned or null",
      "selected_showroom": "showroom location if mentioned or null",
      "confidence": 0.0-1.0 (your confidence in the extraction)
    }
    """
    
    # Create agent prompt
    prompt = f"""
    Based on the following conversation between a customer and a Spiro electric bike sales agent, 
    extract the required lead qualification information.
    
    USER CONTEXT INFORMATION:
    {user_info}
    
    CONVERSATION HISTORY:
    {conversation_text}
    
    Extract the lead qualification details from this conversation.
    """
    
    # Call LLM to extract lead details
    llm_response = call_llm_api(prompt, system_prompt, temperature=0.2)
    
    # Clean and parse the response
    extracted_data = clean_json_string(llm_response)
    
    # Create and return LeadDetails object
    return LeadDetails(
        primary_use=extracted_data.get("primary_use"),
        model_selection=extracted_data.get("model_selection"),
        purchase_timeline=extracted_data.get("purchase_timeline"),
        budget_range=extracted_data.get("budget_range"),
        interest_level=extracted_data.get("interest_level"),
        selected_model=extracted_data.get("selected_model"),
        selected_showroom=extracted_data.get("selected_showroom"),
        confidence=extracted_data.get("confidence", 0.0)
    )

def score_and_classify_lead(lead_details: LeadDetails) -> LeadQualification:
    """
    Score the lead based on extracted details and classify it as Hot/Warm/Cold
    
    Args:
        lead_details: Extracted lead qualification details
        
    Returns:
        LeadQualification with scores, classification and rationale
    """
    # Create the system prompt for scoring agent
    system_prompt = """
    You are an expert lead qualification agent for Spiro electric bikes.
    Your task is to analyze the extracted lead information, assign scores to each dimension,
    and classify the lead as Hot, Warm, or Cold based on the scoring criteria.
    
    Scoring Criteria:
    
    1. Primary use of the bike:
       a) Daily Commuting: 8-10 points (clear need)
       b) Delivery Service: 9-10 points (business need, highest conversion potential)
       c) Just Exploring: 3-5 points (undefined need)
       Unknown: 0 points
    
    2. Model selection status:
       a) Yes, interested in specific model: 8-10 points (done research, serious buyer)
       b) No, needs guidance: 4-6 points (needs education, longer sales cycle)
       Unknown: 0 points
    
    3. Purchase timeline:
       a) Within a week: 9-10 points (immediate purchase intent)
       b) Within a month: 7-8 points (planned purchase)
       c) Not yet sure: 3-5 points (no timeline, just exploring)
       Unknown: 0 points
    
    4. Budget range:
       a) Within INR 80000: 6-8 points (budget conscious but potential)
       b) Between INR ************: 7-9 points (mid-range budget)
       c) Above 100000: 8-10 points (premium buyer, higher conversion potential)
       Unknown: 0 points
    
    5. Interest in more details:
       a) Test Ride: 8-10 points (high interest, ready to experience)
       b) EMI/Finance Option: 7-9 points (financially planning the purchase)
       c) Product/Warranty Details: 6-8 points (serious consideration)
       d) No, just exploring: 3-5 points (casual interest)
       Unknown: 0 points
    
    Lead Classification Criteria:
    - Hot Lead: Total score 35-50, with strong signals in use case, timeline, and interest
    - Warm Lead: Total score 20-34, shows interest but may have uncertainties in timeline or budget
    - Cold Lead: Total score 0-19, undefined needs, no timeframe, just exploring
    
    YOU MUST RESPOND IN VALID JSON FORMAT with the following structure:
    {
      "scores": {
        "primary_use": 0-10,
        "model_selection": 0-10,
        "purchase_timeline": 0-10,
        "budget_range": 0-10,
        "interest_level": 0-10,
        "total_score": sum of all scores
      },
      "category": "Hot Lead" / "Warm Lead" / "Cold Lead",
      "rationale": "detailed explanation of why this lead is classified this way",
      "recommendation": "specific recommended next steps for this lead"
    }
    """
    
    # Convert lead details to formatted text for the agent
    lead_details_text = f"""
    Primary Use: {lead_details.primary_use or 'Unknown'}
    Model Selection: {lead_details.model_selection or 'Unknown'}
    Purchase Timeline: {lead_details.purchase_timeline or 'Unknown'}
    Budget Range: {lead_details.budget_range or 'Unknown'}
    Interest Level: {lead_details.interest_level or 'Unknown'}
    Selected Model: {lead_details.selected_model or 'Unknown'}
    Selected Showroom: {lead_details.selected_showroom or 'Unknown'}
    Confidence Score: {lead_details.confidence}
    """
    
    # Create agent prompt
    prompt = f"""
    Based on the following extracted lead qualification details, score each dimension,
    calculate the total score, and classify the lead as Hot, Warm, or Cold.
    
    LEAD DETAILS:
    {lead_details_text}
    
    Please analyze this lead information, apply the scoring criteria, and provide lead classification.
    """
    
    # Call LLM to score and classify lead
    llm_response = call_llm_api(prompt, system_prompt, temperature=0.3)
    
    # Clean and parse the response
    classification_data = clean_json_string(llm_response)
    
    # Extract scores
    scores = classification_data.get("scores", {})
    lead_scores = LeadScores(
        primary_use=scores.get("primary_use", 0),
        model_selection=scores.get("model_selection", 0),
        purchase_timeline=scores.get("purchase_timeline", 0),
        budget_range=scores.get("budget_range", 0),
        interest_level=scores.get("interest_level", 0),
        total_score=scores.get("total_score", 0)
    )
    
    # Create and return LeadQualification object
    return LeadQualification(
        lead_details=lead_details,
        lead_scores=lead_scores,
        category=classification_data.get("category", "Cold Lead"),
        rationale=classification_data.get("rationale", ""),
        recommendation=classification_data.get("recommendation", "")
    )

def generate_lead_qualification_report(lead_qualification: LeadQualification) -> str:
    """
    Generate a formatted lead qualification report
    
    Args:
        lead_qualification: Complete lead qualification data
        
    Returns:
        Formatted lead qualification report
    """
    # Create system prompt for report generation with explicit formatting requirements
    system_prompt = """
    You are an expert lead qualification analyst for Spiro electric bikes.
    Your task is to create a clear, concise, and professional lead qualification report
    based on the provided lead qualification data.
    
    The report MUST follow this EXACT structure and formatting:

    # Spiro Electric Bikes - Lead Qualification Report
    
    ## 1. Lead Classification Summary
    
    Category: [Category]
    
    Overall Assessment: [1-2 sentence assessment of the lead's potential]
    
    ## 2. Qualification Details
    
    | Dimension | Score | Explanation |
    |-----------|-------|-------------|
    | Primary Use | X/10 | Explanation of the score |
    | Model Selection | X/10 | Explanation of the score |
    | Purchase Timeline | X/10 | Explanation of the score |
    | Budget Range | X/10 | Explanation of the score |
    | Interest Level | X/10 | Explanation of the score |
    
    ## 3. Recommendations
    
    1. [First recommendation]
    2. [Second recommendation]
    3. [Third recommendation]
    
    CRITICAL FORMATTING RULES:
    1. The table MUST have proper markdown formatting with header separator row
    2. Include empty lines before and after the table
    3. Don't omit any sections of this format
    4. Use proper markdown headers with # and ## symbols
    5. The table header separator row MUST be exactly as shown: |-----------|-------|-------------|
    6. DO NOT include any HTML tags like <div> or CSS classes in your output - use PURE markdown only
    7. Each table row should be on a single line without line breaks
    8. Table column separator symbols | must be properly aligned
    9. IMPORTANT: DO NOT wrap your response in triple backticks (```). Just provide the raw markdown.
    """
    
    # Prepare data for report generation
    details = lead_qualification.lead_details
    scores = lead_qualification.lead_scores
    
    lead_data = f"""
    LEAD QUALIFICATION DATA:
    
    Category: {lead_qualification.category}
    
    Score Details:
    - Primary Use: {details.primary_use or 'Unknown'} (Score: {scores.primary_use}/10)
    - Model Selection: {details.model_selection or 'Unknown'} (Score: {scores.model_selection}/10)
    - Purchase Timeline: {details.purchase_timeline or 'Unknown'} (Score: {scores.purchase_timeline}/10)
    - Budget Range: {details.budget_range or 'Unknown'} (Score: {scores.budget_range}/10)
    - Interest Level: {details.interest_level or 'Unknown'} (Score: {scores.interest_level}/10)
    - Total Score: {scores.total_score}/50
    
    Selected Model (if any): {details.selected_model or 'None specified'}
    Selected Showroom (if any): {details.selected_showroom or 'None specified'}
    
    Classification Rationale: {lead_qualification.rationale}
    
    Recommendation: {lead_qualification.recommendation}
    """
    
    # Create prompt for report generation with very explicit formatting instructions
    prompt = f"""
    Generate a professional lead qualification report based on the following data:
    
    {lead_data}
    
    Follow EXACTLY the format specified in the system prompt:
    1. Use "# Spiro Electric Bikes - Lead Qualification Report" as the main title
    2. Use "## 1. Lead Classification Summary", "## 2. Qualification Details", and "## 3. Recommendations" as section headers
    3. Format the qualification details EXACTLY as a markdown table with this structure:
    
    | Dimension | Score | Explanation |
    |-----------|-------|-------------|
    | Primary Use | {scores.primary_use}/10 | Explanation here |
    
    4. Make sure to include empty lines before and after the table
    5. For recommendations, use a numbered list (1., 2., 3.)
    6. DO NOT include any HTML tags or CSS classes in your output
    7. Make sure the table rows are properly formatted with | symbols aligned
    8. IMPORTANT: DO NOT add any backticks (```) around your response.
    """
    
    # Call LLM to generate report
    report = call_llm_api(prompt, system_prompt, temperature=0.3)
    
    # Post-process the report to ensure proper formatting
    # Ensure the report has proper line breaks for markdown parsing
    report = report.replace('\r\n', '\n')
    
    # Remove any backticks that might have been included
    if report.strip().startswith('```') and report.strip().endswith('```'):
        report = report.strip()[3:-3].strip()
    else:
        # Just remove all backticks to be safe
        report = report.replace('```', '')
    
    # Remove any HTML tags that might have been included
    report = re.sub(r'<div[^>]*>|</div>', '', report)
    report = re.sub(r'<[^>]*>', '', report)
    
    # Fix table formatting issues
    table_pattern = r'\|\s*Dimension\s*\|\s*Score\s*\|\s*Explanation\s*\|'
    separator_pattern = r'\|[-]+\|[-]+\|[-]+\|'
    
    if re.search(table_pattern, report):
        # Find table header
        header_match = re.search(table_pattern, report)
        if header_match:
            header_pos = header_match.start()
            
            # Check if there's a proper separator row
            after_header = report[header_match.end():]
            separator_match = re.search(separator_pattern, after_header)
            
            if not separator_match or separator_match.start() > 10:  # If separator is missing or too far from header
                # Insert proper separator after header
                header_line_end = header_pos + report[header_pos:].find('\n')
                if header_line_end > header_pos:
                    separator_row = "\n|-----------|-------|-------------|\n"
                    report = report[:header_line_end] + separator_row + report[header_line_end:]
    
    # Ensure proper spacing around table
    report = report.replace("## 2. Qualification Details\n|", "## 2. Qualification Details\n\n|")
    
    # Ensure there's a line break after the table before the next section
    if "|\n##" in report and "|\n\n##" not in report:
        report = report.replace("|\n##", "|\n\n##")
    
    # Fix common table formatting issues
    table_rows = re.findall(r'\|[^\n]+\|[^\n]+\|[^\n]+\|', report)
    for row in table_rows:
        # Check if row is broken across multiple lines or has missing column separators
        if row.count('|') != 4:  # Should have 4 | symbols for 3 columns
            fixed_row = row.replace('\n', ' ').strip()
            # Ensure the row has the right number of | symbols
            while fixed_row.count('|') < 4:
                fixed_row += ' |'
            report = report.replace(row, fixed_row)
    
    return report

def process_lead_qualification(phone_number: str) -> Dict[str, Any]:
    """
    Process lead qualification for a specific phone number
    
    Args:
        phone_number: The phone number to process lead qualification for
        
    Returns:
        Dictionary with lead qualification results and status
    """
    try:
        # First check if we already have a qualification result for this phone number
        existing_result = db_session.query(LeadQualificationResult).filter_by(
            phone_number=phone_number,
            is_active=True
        ).first()
        
        # If we have a recent result (within the last hour), return it
        if existing_result:
            # For now, we'll always re-analyze even if there's an existing result
            # But we could add logic here to return the existing result if it's recent
            logger.info(f"Found existing lead qualification for {phone_number}, but will re-analyze")
        
        # Get session for the phone number
        session_data = SessionManager.get_session_by_phone(phone_number)
        if not session_data:
            logger.error(f"No session found for phone number: {phone_number}")
            return {
                "success": False,
                "error": "No active session found for this phone number",
                "phone_number": phone_number
            }
        
        # Extract session and pipeline
        db_session_obj, pipeline = session_data
        
        # Check if pipeline and context exist
        if not pipeline or not hasattr(pipeline, 'context'):
            logger.error(f"Invalid pipeline or context for phone number: {phone_number}")
            return {
                "success": False,
                "error": "Invalid session data",
                "phone_number": phone_number
            }
        
        # Get conversation history from pipeline
        conversation_history = []
        if hasattr(pipeline.context, 'conversation_history'):
            conversation_history = pipeline.context.conversation_history
        
        if not conversation_history:
            logger.warning(f"No conversation history found for phone number: {phone_number}")
            return {
                "success": False,
                "error": "No conversation history found",
                "phone_number": phone_number
            }
        
        # Step 1: Extract lead details from conversation
        logger.info(f"Extracting lead details for {phone_number} from {len(conversation_history)} messages")
        lead_details = extract_lead_details(conversation_history, pipeline.context)
        
        # Step 2: Score and classify the lead
        logger.info(f"Scoring and classifying lead for {phone_number}")
        lead_qualification = score_and_classify_lead(lead_details)
        
        # Step 3: Generate lead qualification report
        logger.info(f"Generating lead qualification report for {phone_number}")
        qualification_report = generate_lead_qualification_report(lead_qualification)
        
        # Step 4: Store the result in the database
        try:
            # Create or update the lead qualification result
            if existing_result:
                # Update existing record
                existing_result.category = lead_qualification.category
                existing_result.total_score = lead_qualification.lead_scores.total_score
                existing_result.confidence = lead_details.confidence
                
                # Update scores
                existing_result.primary_use_score = lead_qualification.lead_scores.primary_use
                existing_result.model_selection_score = lead_qualification.lead_scores.model_selection
                existing_result.purchase_timeline_score = lead_qualification.lead_scores.purchase_timeline
                existing_result.budget_range_score = lead_qualification.lead_scores.budget_range
                existing_result.interest_level_score = lead_qualification.lead_scores.interest_level
                
                # Update details
                existing_result.primary_use = lead_details.primary_use
                existing_result.model_selection = lead_details.model_selection
                existing_result.purchase_timeline = lead_details.purchase_timeline
                existing_result.budget_range = lead_details.budget_range
                existing_result.interest_level = lead_details.interest_level
                existing_result.selected_model = lead_details.selected_model
                
                # Update analysis
                existing_result.rationale = lead_qualification.rationale
                existing_result.recommendation = lead_qualification.recommendation
                existing_result.qualification_report = qualification_report
                
                logger.info(f"Updated lead qualification result for {phone_number}")
                result_record = existing_result
            else:
                # Create new record
                result_record = LeadQualificationResult(
                    phone_number=phone_number,
                    category=lead_qualification.category,
                    total_score=lead_qualification.lead_scores.total_score,
                    confidence=lead_details.confidence,
                    
                    # Individual scores
                    primary_use_score=lead_qualification.lead_scores.primary_use,
                    model_selection_score=lead_qualification.lead_scores.model_selection,
                    purchase_timeline_score=lead_qualification.lead_scores.purchase_timeline,
                    budget_range_score=lead_qualification.lead_scores.budget_range,
                    interest_level_score=lead_qualification.lead_scores.interest_level,
                    
                    # Extracted details
                    primary_use=lead_details.primary_use,
                    model_selection=lead_details.model_selection,
                    purchase_timeline=lead_details.purchase_timeline,
                    budget_range=lead_details.budget_range,
                    interest_level=lead_details.interest_level,
                    selected_model=lead_details.selected_model,
                    
                    # Analysis
                    rationale=lead_qualification.rationale,
                    recommendation=lead_qualification.recommendation,
                    qualification_report=qualification_report,
                    
                    # Status
                    is_active=True
                )
                db_session.add(result_record)
                logger.info(f"Created new lead qualification result for {phone_number}")
            
            # Commit the changes
            db_session.commit()
            logger.info(f"Saved lead qualification result for {phone_number}")
            
        except Exception as db_err:
            logger.error(f"Error saving lead qualification result: {str(db_err)}", exc_info=True)
            db_session.rollback()
            # Continue processing even if we failed to save to the database
        
        # Create response with all qualification data
        response = {
            "success": True,
            "phone_number": phone_number,
            "id": result_record.id if result_record else None,
            "lead_details": lead_details.dict(),
            "lead_scores": lead_qualification.lead_scores.dict(),
            "scores": {
                "primary_use": lead_qualification.lead_scores.primary_use,
                "model_selection": lead_qualification.lead_scores.model_selection,
                "purchase_timeline": lead_qualification.lead_scores.purchase_timeline,
                "budget_range": lead_qualification.lead_scores.budget_range,
                "interest_level": lead_qualification.lead_scores.interest_level
            },
            "total_score": lead_qualification.lead_scores.total_score,
            "category": lead_qualification.category,
            "lead_category": lead_qualification.category,
            "rationale": lead_qualification.rationale,
            "recommendation": lead_qualification.recommendation,
            "qualification_report": qualification_report,
            "confidence": lead_details.confidence,
            "created_at": result_record.created_at.isoformat() if result_record and result_record.created_at else None,
            "updated_at": result_record.updated_at.isoformat() if result_record and result_record.updated_at else None,
            "is_active": True
        }
        
        return response
        
    except Exception as e:
        logger.error(f"Error processing lead qualification for {phone_number}: {str(e)}", exc_info=True)
        return {
            "success": False,
            "error": str(e),
            "phone_number": phone_number
        }

def get_lead_qualification_by_phone(phone_number: str) -> Optional[Dict[str, Any]]:
    """
    Retrieve stored lead qualification results by phone number
    
    Args:
        phone_number: The phone number to look up
        
    Returns:
        Dictionary with lead qualification results or None if not found
    """
    try:
        result = db_session.query(LeadQualificationResult).filter_by(
            phone_number=phone_number,
            is_active=True
        ).first()
        
        if not result:
            return None
        
        # Format the result to match the expected structure in the frontend
        formatted_result = result.to_dict()
        
        # Ensure all necessary fields are present
        formatted_result.update({
            "success": True,
            "phone_number": result.phone_number,
            "scores": {
                "primary_use": result.primary_use_score or 0,
                "model_selection": result.model_selection_score or 0,
                "purchase_timeline": result.purchase_timeline_score or 0,
                "budget_range": result.budget_range_score or 0,
                "interest_level": result.interest_level_score or 0
            },
            "total_score": result.total_score or 0,
            "category": result.category,
            "lead_category": result.category,
            "qualification_report": result.qualification_report or "",
            "is_active": result.is_active
        })
        
        return formatted_result
        
    except Exception as e:
        logger.error(f"Error retrieving lead qualification for {phone_number}: {str(e)}", exc_info=True)
        return None

def list_lead_qualifications(limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
    """
    List all lead qualification results
    
    Args:
        limit: Maximum number of results to return
        offset: Number of results to skip
        
    Returns:
        List of lead qualification results
    """
    try:
        results = db_session.query(LeadQualificationResult).filter_by(
            is_active=True
        ).order_by(
            LeadQualificationResult.created_at.desc()
        ).limit(limit).offset(offset).all()
        
        # Format the results to match the expected structure in the frontend
        formatted_results = []
        for result in results:
            result_dict = result.to_dict()
            
            # Ensure all necessary fields are present
            result_dict.update({
                "success": True,
                "phone_number": result.phone_number,
                "scores": {
                    "primary_use": result.primary_use_score or 0,
                    "model_selection": result.model_selection_score or 0,
                    "purchase_timeline": result.purchase_timeline_score or 0,
                    "budget_range": result.budget_range_score or 0,
                    "interest_level": result.interest_level_score or 0
                },
                "total_score": result.total_score or 0,
                "category": result.category,
                "lead_category": result.category,
                "qualification_report": result.qualification_report or "",
                "is_active": result.is_active
            })
            
            formatted_results.append(result_dict)
        
        return formatted_results
        
    except Exception as e:
        logger.error(f"Error listing lead qualifications: {str(e)}", exc_info=True)
        return [] 